<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="物流码" borderBottom required labelWidth="100">
          <u--input v-model="model.logisticCode" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('logisticCode')"></view>
        </u-form-item>
        <u-form-item label="箱码" required labelWidth="100">
          <u--input v-model="model.boxCode" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('boxCode')"></view>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
import useNls from "@/mixins/useNls";

export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    this.changeboxCode = this.$debounce(this.changeboxCode, 1000)
    this.changelogisticCode = this.$debounce(this.changelogisticCode, 1000)
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      rulesTip: {
        logisticCode: '物流码不能为空',
        boxCode: '箱码不能为空',
      },
      model: {},
    }
  },
  watch: {
    'model.boxCode': {
      handler(val) {
        this.changeboxCode(val)
      }
    },
    'model.logisticCode': {
      handler(val) {
        this.changelogisticCode(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()

  },

  methods: {
    initModel() {
      this.model = {
        boxCode: '',
        logisticCode: '',
      }
    },
    changeboxCode(value) {
      if (!value) {
        return
      }
    },
    changelogisticCode(value) {
      if (!value) {
        return
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        boxCode: this.model.boxCode,
        logisticCode: this.model.logisticCode,
      }
      this.$service.BarcodeBinding.bindingCode(params).then(res => {
        this.initModel()
        this.$Toast('操作成功')
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        // case 'boxCode':
        //   this.model.boxCode = ''
        //   break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
