<template>
  <view class="listPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="topContainer"> </view>
    <view class="listContainer ma5">
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">在制品条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">markingNo</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">不良现象</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">操作</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in list" :key="index" class="flex bl_e1e1e1 h40">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ item.lotName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ item.markingNo }}</view>

              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.reasonCodeText }}</view>

              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2">
                <!-- <view class="btn" >删除</view> -->
                <view class="w80x" @click="submit(item, index)">
                  <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
                </view>
              </view>
            </view>

            <view class="pt100" v-if="!list || list.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  mixins: [ScrollMixin],
  data() {
    return {
      // list: Array.from({ length: 50 }, (v, i) => ({
      //   lotName: i,
      //   markingNo: i < 10 ? '66' : i > 30 ? '88' : '77',
      // })),
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.UnqualifiedEntryList;
      },
      set(value) {
        this.$store.commit('UnqualifiedEntryList', value);
      }
    }
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailpageTitle // 标题
    this.nlsMap = nlsMap
  },
  methods: {
    moment,
    initModel() {
    },
    submit(item, index) {
      if (item.markingNo) {
        // 如果是有marking单号得 可能是多个
        this.list = this.list.filter(ele => ele.markingNo !== item.markingNo)
      } else {
        this.list.splice(index, 1)
      }
      // uni.showModal({
      //   title: '提示',
      //   content: `是否确认删除？`,
      //   cancelText: '取消',
      //   confirmText: '确认',
      //   cancelColor: '#666',
      //   confirmColor: '#409eff',
      //   success: (res) => {
      //     if (res.confirm) {
      //       this.list.splice(index, 1)
      //     }
      //     if (res.cancel) { }
      //   },
      // })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/uform.scss';
@import '../../../../styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_header {
      flex-shrink: 0;
    }
    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}

.btn {
  width: 120rpx;
  height: 50rpx;
  border-radius: 10%;
  background-color: #0285be;
  border: 2rpx solid #b1c2db;
  text-align: center;
  line-height: 50rpx;
  font-size: 24rpx;
  color: #fff;
}
</style>
