<template>
	<view class="bc_f3f3f7 myContainerPage">
		<u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
			leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
		<!-- {{ nlsMap }} -->
		<view class="myContainer ma10">
			<u--form labelPosition="left" :model="model" labelWidth="100">

				<u-form-item label="设备编号" labelWidth="100">
					<u--input v-model="model.machineName" border="none" placeholder="请扫描或输入"></u--input>
					<view class="iconfont icon-saoma" @click="scan('machineName')"></view>
				</u-form-item>
			</u--form>

			<!-- 上料明细列表 -->

			<view class="mt10">
				<scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered"
					@refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll"
					refresher-background="#f3f3f7">
					<view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in filteredList" :key="index">
						<view class="flex between h40 hcenter c_999">
							<view>物料标签</view>
							<view>{{ ele.consumableName }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>物料NC</view>
							<view>{{ ele.consumableSpecName }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>物料描述</view>
							<view>{{ ele.description }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>所处设备</view>
							<view>{{ ele.machineName }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>设备描述</view>
							<view>{{ ele.machineDesc }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>初始数量</view>
							<view> {{ ele.createQuantity }}</view>
						</view>
						<view class="flex between h40 hcenter c_999">
							<view> 剩余数量</view>
							<view> {{ ele.quantity }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view> 单位</view>
							<view> {{ ele.consumableUnit }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>上料时间</view>
							<view> {{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
							</view>
						</view>
						<view class="flex betweeen mt5">
							<u-button type="success" @click="feeding(ele)">下料</u-button>
							<u-button style="margin-left: 10%;" type="success" @click="summit(ele)">修改剩余数量</u-button>
						</view>
					</view>
					<NoData v-if="!filteredList || filteredList.length === 0"></NoData>

				</scroll-view>
			</view>

			<u-modal :show="isShowModal" title="编辑" confirmText="确认" @confirm="changeConfirm" @cancel="modalCancel"
				:showCancelButton="true">
				<view class="listContainer">
					<view class="table_header  flex">

						<u--form labelPosition="left" :model="model" labelWidth="100" ref="form" :border="false">
							<u-form-item label="物料标签" labelWidth="120">
								<view class="w100x flex right"> {{ model.consumableName }}</view>
							</u-form-item>
							<u-form-item label="剩余数量" labelWidth="120">
								<view class="w100x flex right"> {{ model.quantity }}</view>
							</u-form-item>
							<u-form-item label="修改后的数量" required labelWidth="120">
								<u--input v-model="model.remainingQty" type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
				</view>
			</u-modal>

			<u-modal :show="isShowModal2" title="下料" confirmText="确认" @confirm="changeConfirm2" @cancel="modalCancel2"
				:showCancelButton="true">
				<view class="listContainer">
					<view class="table_header  flex">

						<u--form labelPosition="left" :model="model" labelWidth="100" ref="form" :border="false">
							<u-form-item label="设备号" labelWidth="120">
								<view class="w100x flex right"> {{ model.machineNameX }}</view>
							</u-form-item>
							<u-form-item label="物料标签" labelWidth="120">
								<view class="w100x flex right"> {{ model.consumableName }}</view>
							</u-form-item>
							<u-form-item label="是否完全消耗" labelWidth="120">
								<view class="w100x">
									<u-radio-group v-model="model.czfs" placement="column" style="width: 100%;">
										<view style="display: flex;flex-direction: row-reverse">
											<u-radio v-for="item in radiolist" :key="item.value"
												:customStyle="{ marginRight: '10rpx' }" :label="item.name"
												:name="item.name">
											</u-radio>
										</view>
									</u-radio-group>
								</view>
							</u-form-item>
							<u-form-item label="理论剩余数量" required labelWidth="120">
								<view v-show="model.czfs === '是'" class="w100x flex right"> {{ model.remainingQty }}
								</view>
								<u--input v-show="model.czfs !== '是'" v-model="model.remainingQty"
									type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
				</view>
			</u-modal>
		</view>


	</view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData';
import useNls from "@/mixins/useNls";
import moment from 'moment';
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
import { USER_ID } from '@/utils/common/evtName.js'


const SHOW_MODEL_KEY = 'CONSUMABLE_RECIEVE_EXIST'
export default {
	mixins: [useNls, ScrollMixin, PrintPackageMixin],
	components: {
		NoData,
	},
	data() {
		this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
		return {
			isShowModal: false,
			isShowModal2: false,
			lodash: _,
			pageParams: {},
			pageTitle: '',
			globalMap: getApp().globalData.globalMap, // 获取全局数据
			nlsMap: {

			},

			columns: [],
			select: false,
			selectType: '',
			trayNoParmas: {},
			model: {},
			filteredList: [],
			originalList: [],
			selectedJobType: '',
			radiolist: [
				{
					name: '是',
					value: 1
				},
				{
					name: '否',
					value: 0
				}
			],
		}
	},
	computed: {
	},
	watch: {
		'model.machineName': {
			handler(val) {
				if (val === '' || val === null || val === undefined) return
				this.model.machineName2 = val
				this.changeboxNo(val)
			},
		},
		'model.czfs': {
			handler(newVal) {
				if (newVal === '是') {
					this.model.remainingQty = 0; // "是" 时设置为 0
				}
			},
			immediate: true // 组件加载时立即执行一次
		},
	},
	async onLoad(options) {
		let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
		this.pageParams = pageParams
		this.pageTitle = pageParams.pageTitle // 标题
		await this.initNls(pageParams, this.nlsMap)

		this.initModel()
	},
	methods: {
		moment,

		initModel() {
			this.model = {
				machineName: '',
				machineName2: '',
				machineNameX: '',
				remainingQty: '',
				consumableName: '',
				quantity: '',
				consumableSpecName: '',
				description: '',
				machineDesc: '',
				consumableUnit: '',
				createTime: '',
				czfs: '否',
			}
		},
		async changeboxNo(value) {

			if (value === '' || value === null || value === undefined) return
			try {
				let params = {
					machineName: value,
				}

				let res = await this.$service.ThreeCodeToOne.GetPackMachineFeedingmaterial(params)

				if (res.success) {
					this.filteredList = res.datas
				} else {
					this.$Toast(res.msg)
				}
				this.model.machineName = ''
			} catch (error) {
				this.model.machineName = ''
			}
		},
		// 模态框取消
		modalCancel() {
			this.isShowModal = false
		},
		modalCancel2() {
			this.isShowModal2 = false
		},
		async changeConfirm() {
			if (this.model.remainingQty === '' || this.model.remainingQty === null || this.model.remainingQty === undefined) {
				return this.$Toast('请输入修改后剩余数量!')
			}

			try {
				let params = {
					consumableName: this.model.consumableName,
					quantity: this.model.remainingQty
				}

				let res = await this.$service.ThreeCodeToOne.updateQuantity(params)


				if (res.success) {
					this.$Toast("修改成功")
					this.modalCancel()
					this.changeboxNo(this.model.machineName2)
					this.model.remainingQty = ''

				} else {
					this.$Toast(res.msg)
				}
			} catch (error) {
			} finally {

			}
		},
		async changeConfirm2() {
			if (this.model.remainingQty === '' || this.model.remainingQty === null || this.model.remainingQty === undefined) {
				return this.$Toast('请输入理论剩余数量!')
			}

			try {
				let params = {
					machineNo: this.model.machineNameX,
					labelno: this.model.consumableName,
					isconsumption: this.model.czfs == '否' ? 0 : 1,
					remainingQty: this.model.remainingQty,
					eventUser: this.$getLocal(USER_ID)
				}

				let res = await this.$service.ThreeCodeToOne.UnloadReportIot(params)


				console.log(res.resultCode)
				console.log('res.resultCode:', res.resultCode, typeof res.resultCode)

				if (res.resultCode === 0) {
					this.$Toast(res.resultMsg)
					this.modalCancel2()
					this.changeboxNo(this.model.machineName2)
					this.model.remainingQty = ''

				} else {
					this.$Toast(res.resultMsg)
				}
			} catch (error) {
				console.log(error)
				if (error.resultCode === 0) {
					this.$Toast(error.resultMsg)
					this.modalCancel2()
					this.changeboxNo(this.model.machineName2)
					this.model.remainingQty = ''

				} else {
					this.$Toast(error.resultMsg)
				}
				this.$Toast(error)
			} finally {
				//this.model.remainingQty = ''
			}
		},
		summit(item) {
			if (!item) return
			this.model.consumableName = item.consumableName
			this.model.quantity = item.quantity
			this.isShowModal = true

		},
		async feeding(item) {
			if (!item) return
			this.model.consumableName = item.consumableName
			this.model.machineNameX = item.machineName
			this.model.czfs = '否'
			this.isShowModal2 = true
		},
		scan(key) {
			// #ifdef H5
			switch (key) {
				case 'machineName':
					this.model.machineName = 'XNY-C3-1F-177'
					break;
				default:
					break;
			}
			// #endif
			//#ifdef APP-PLUS
			uni.scanCode({
				success: (res) => {
					this.model.machineName = res.result
				},
			})
			// #endif
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.list {
	margin-top: 10px;
}

.item {
	padding: 10px;
	background: #f2f2f2;
	margin-bottom: 5px;
}
</style>
