<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="数据来源方式" borderBottom required labelWidth="120">
          <view class="w100x flex right" @click="checkSelect('unqualifiedSource')">
            <view v-if="model.unqualifiedSource">{{ $utils.filterObjLabel(dicts.unqualifiedSourceList,
              model.unqualifiedSource) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'unqualifiedSource' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="条码号" borderBottom labelWidth="120" v-if="model.unqualifiedSource === 'Marking'">
          <view class="w100x flex right" @click="checkSelect('lotName')">
            <view v-if="model.lotName">{{ $utils.filterObjLabel(dicts.markingNoList, model.lotName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'lotName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="条码号" borderBottom labelWidth="100" v-else>
          <u--input v-model="model.lotName" border="none" :focus="focus_lotName" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>

        <u-form-item label="物料类型" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.filterObjLabel(dicts.consumableTypeList, model.consumableType) }}
          </view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecText) }}
          </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.processOperationName }}
          </view>
        </u-form-item>

        <template v-if="model.unqualifiedSource !== 'Defect'">
          <view class="lin40 fs16 pl20 c_00b17b mt20">不良现象</view>
          <u-form-item label="不良现象" borderBottom required labelWidth="120">
            <view class="w100x flex right" @click="checkSelect('reasonCodeType')">
              <view v-if="model.reasonCodeType">{{ $utils.filterObjLabel(dicts.reasonCodeTypeList, model.reasonCodeType)
              }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5"
                :style="{ transform: select && selectType === 'reasonCodeType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="不良数量" labelWidth="100">
            <view class="w100x flex right">
              <!-- {{ list.length }} -->
              {{ ngTotal }}
            </view>
          </u-form-item>
        </template>

        <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
          @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c c_00b17b flex hcenter mt5">
        <view class="mr5">待提交不良明细</view>
        <u-icon class="ml2" @click="gotoQuery" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        <view v-show="isBatch" class="ml12" @click="batchInput">【批量录入】</view>
      </view>
      <my-dialog ref="myDialog">
        <template v-slot:content>
          <view class="flex flex_column center pa20">
            <view class="flex center fs18 c_333 fb">批量输入条码号</view>
            <view class="flex center w100x pt10">
              <u--textarea :maxlength="-1" v-model="model.lotNames" placeholder="请输入条码号"></u--textarea>
            </view>
          </view>
        </template>
      </my-dialog>
    </view>
    <view class="btnContainer" @click="submit">不良提交</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      pageTitle: '',
      isBatch: false,
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        detailpageTitle: '待提交不良明细',
      },
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      model: {},
      ngLotItemList: [],
      columns: [],
      select: false,
      selectType: '',
      focus_lotName: false,
      dicts: {
        unqualifiedSourceList: [
          // { value: '1', label: '产品打缺陷', },
          // { value: '2', label: '缺陷条码', },
          // { value: '3', label: 'Marking单据', },
        ],
        consumableTypeList: [],
        reasonCodeTypeList: [],
        markingNoList: [],
      },
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.UnqualifiedEntryList;
      },
      set(value) {
        console.log(value, 'value');
        this.$store.commit('setUnqualifiedEntryList', value);
      }
    },
    ngTotal() {
      let a = this.list.reduce((total, curr) => {
        return total + Number(curr.ngQuantity)
      }, 0)
      return a
    }
  },
  watch: {
    'model.lotName': {
      handler(val) {
        this.changeLotName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)


    this.getEnumValue('Unqualified_Source', 'unqualifiedSourceList') // 单据类型
    this.getEnumValue('ConsumableType', 'consumableTypeList') // 物料类型
    this.getListMarkingNo()
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    getListMarkingNo() {
      this.$service.common.getDictByQueryId('DisposalOrder_ListMarkingNo').then(res => {
        this.dicts.markingNoList = res.datas.map(item => ({
          label: item.markingNo,
          value: item.markingNo,
        }))
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'lotName':
          this.columns = this.dicts.markingNoList
          break;
        case 'unqualifiedSource':
          this.columns = this.dicts.unqualifiedSourceList
          break;
        case 'reasonCodeType':
          this.columns = this.dicts.reasonCodeTypeList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      if (this.selectType == 'reasonCodeType') {
        
        console.log(this.list)
        console.log(this.ngLotItemList)
        
        // 如果存在
        let flag = false
        this.list.forEach(item => {
            if (item.lotName == this.model.lotName) {
              if(item.reasonCode == e.value[0].reasonCode){
                flag = true
              }
            }
        })
        if (flag) {
          this.$Toast('在制品条码已存在待提交不良明细中！')
          this.model.lotName = ''
          this.dicts.reasonCodeTypeList = []
          this.focus_lotName = true
          this.select = false
          return
        }
        
        let arr = this.ngLotItemList.map(item => ({
          ...item,
          reasonCode: e.value[0].reasonCode,
          reasonCodeText: e.value[0].reasonCodeDesc,
        }))
        this.list.push(...arr)
        this.$Toast(`在条码号[${this.model.lotName}]加入不良明细成功`)
        // setTimeout(() => {
        // this.model.lotName = ''
        this.model.reasonCode = ''
        // this.dicts.reasonCodeTypeList = []
        this.focus_lotName = true
        // }, 0);
      }
      if (this.selectType == 'unqualifiedSource') {
        this.model.lotName = null
        this.model.consumableType = null
        this.model.consumableSpecName = null
        this.model.consumableSpecText = null
        this.model.processOperationName = null
        this.model.reasonCodeType = null
        this.list = []
      }
      if (e.value[0].value === 'Defect') {
        this.isBatch = true
      }
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },
    initModel() {
      this.model = {
        unqualifiedSource: '',//  数据来源方式
        lotName: null, // 条码号
        consumableType: null, // 物料类型
        consumableSpecName: null, // 物料编码
        consumableSpecText: null, // 物料名称	
        processOperationName: null, // 工序
        reasonCodeType: null, // 不良现象
        halfRohFlag: null,
        lotNames: '',
      }
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/QualityControl/UnqualifiedEntry/modules/detail?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`
      })
    },
    submit() {
      if (this.list.length == 0) {
        this.$Toast('暂无待提交不良明细！')
        return
      }
      let params = {
        ...this.model,
        ngLotItemList: this.list,
      }
      // this.$Toast('操作成功!')
      // this.initModel()
      // this.list = []
      // return
      this.$service.UnqualifiedEntry.add(params).then(res => {
        this.$Toast('操作成功!')
        this.initModel()
        this.list = []
      })
    },
    async batchInput() {
      this.$refs.myDialog.showDialog({
        cancelText: '取消',
        confirmText: '确定',
        onCancel: () => { },
        onConfirm: async (myDialogVm) => {
          if (!this.model.lotNames || !this.model.lotNames.trim()) {
            this.$Toast('请输入条码号')
            return
          }

          const lotNameList = this.model.lotNames.split('\n').map(x => x.trim()).filter(x => x)

          if (lotNameList.length === 0) {
            this.$Toast('请输入有效的条码')
            return
          }

          let newItems = []

          try {
            for (let lotName of lotNameList) {
              let params = {
                consumableSpecName: this.model.consumableSpecName,
                consumableType: this.model.consumableType,
                unqualifiedSource: this.model.unqualifiedSource,
                lotName: lotName,
                halfRohFlag: this.model.halfRohFlag,
              }

              let res = await this.$service.UnqualifiedEntry.scanLotName(params)

              if (res.datas && res.datas.length > 0) {
                let data = res.datas[0]

                // 查重逻辑：ngLotItemList 中的任意 lotName 是否已存在于 this.list
                let hasDuplicate = data.ngLotItemList.some(item2 =>
                  this.list.some(item1 => item1.lotName === item2.lotName)
                )

                if (hasDuplicate) {
                  this.$Toast(`条码 ${lotName} 已存在待提交不良明细中，已跳过`)
                  continue
                }

                // 初始化只处理第一条数据时赋值
                if (newItems.length === 0) {
                  this.model.halfRohFlag = data.halfRohFlag
                  this.model.consumableSpecName = data.consumableSpecName
                  this.model.consumableSpecText = data.consumableSpecText
                  this.model.consumableType = data.consumableType
                  this.model.processOperationName = data.processOperationName

                  this.dicts.reasonCodeTypeList = (data.reasonCodeList || []).map(item => ({
                    ...item,
                    value: item.reasonCode,
                    label: item.reasonCodeDesc
                  }))
                }

                // 累加处理结果
                newItems.push(...data.ngLotItemList)
              }
            }

            // 最终统一 push 到 this.list 中（如果为缺陷条码）
            if (this.model.unqualifiedSource === 'Defect' && newItems.length > 0) {
              this.list.push(...newItems)
              this.$Toast('操作成功!')
            }

            myDialogVm.close()
            setTimeout(() => {
              this.model.lotNames = ''
            }, 800)
          } catch (error) {
            console.error(error)
            this.$Toast(error.msg)
          }
        }
      })
    }
    ,
    async changeLotName(value) {
      if (!value) return
      this.model.reasonCodeType = undefined
      this.dicts.reasonCodeTypeList = []
      this.focus_lotName = false
      let params = {
        consumableSpecName: this.model.consumableSpecName,
        consumableType: this.model.consumableType,
        unqualifiedSource: this.model.unqualifiedSource,
        lotName: value,
        halfRohFlag: this.model.halfRohFlag,
      }
      try {
        let res = await this.$service.UnqualifiedEntry.scanLotName(params)
        if (res.datas.length > 0) {
          let data = res.datas[0]

          // 如果存在
          // let flag = false
          // this.list.forEach(item => {
          //   data.ngLotItemList.forEach(item2 => {
          //     if (item.lotName == item2.lotName) {
          //       flag = true
          //     }
          //   })
          // })
          // if (flag) {
          //   this.$Toast('在制品条码已存在待提交不良明细中！')
          //   this.model.lotName = ''
          //   this.focus_lotName = true
          //   return
          // }

          this.model.halfRohFlag = data.halfRohFlag
          this.model.consumableSpecName = data.consumableSpecName
          this.model.consumableSpecText = data.consumableSpecText
          this.model.consumableType = data.consumableType
          this.ngLotItemList = data.ngLotItemList

          this.model.processOperationName = data.processOperationName
          this.dicts.reasonCodeTypeList = data.reasonCodeList && data.reasonCodeList.map(item => ({
            ...item,
            value: item.reasonCode,
            label: item.reasonCodeDesc
          }))
          // 如果是缺陷条码得
          if (this.model.unqualifiedSource == 'Defect') {
            this.list.push(...this.ngLotItemList)

            this.$Toast('操作成功!')
            setTimeout(() => {
              this.model.lotName = ''
            }, 800);
          }

        } else {
          this.model.lotName = ''
          this.$Toast('在制品不存在！')
        }

      } catch (error) {
        console.log('error', error);
        this.model.lotName = ''
        // this.initModel()
      }
    },
    keepFocus() {
      this.isFocus = false; // 先置为 false
      this.$nextTick(() => {
        this.isFocus = true; // 再置为 true，确保光标恢复
      });
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'lotName':
          this.model.lotName = 'CELL001' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
