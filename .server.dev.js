//yarn start dev          读取.server.dev.js文件中的default配置
//yarn start dev:test     读取.server.dev.js文件中的test配置，test覆盖default
//yarn start dev:uat      读取.server.dev.js文件中的uat配置，uat覆盖default
//yarn start pro:test     读取.server.pro.js文件中的配置
module.exports = {
  default: {//默认配置，被使用到的其他配置会覆盖默认配置
    host: 'localhost',
    port: 9202,
    proxy: {
      '/gcapi': {//反向代理到真实服务
        target : "http://************:30416/",
        secure: false,//忽略https
        changeOrigin: true,//解决跨域
        xfwd: false,
        pathRewrite: { '^/gcapi': '' },
      },
    }
  },
}
