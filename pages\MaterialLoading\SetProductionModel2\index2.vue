<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    {{nlsMap}}
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备号描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineSpecDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item v-if="showSelect" label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="clickProcessOperationName">
            <u--input readonly v-model="model.processOperationName" border="none" placeholder="请选择"></u--input>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item v-if="!showSelect" label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <u--input readonly v-model="model.processOperationName" border="none"></u--input>
          </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="clickProductSpecName">
            <u--input readonly v-model="model.productSpecName" border="none" placeholder="请选择"></u--input>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="工艺卡" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.cardName ? model.cardName + '/' + model.cardRevision : '' }}
          </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">Input参数</view>

        <view v-for="(item, index) in parameterList" :key="index">
          <u-form-item :label="item.description" borderBottom labelWidth="180">
            <u--input readonly v-model="item.parameter.standard" border="none"></u--input>
            <u--input readonly v-model="item.parameter.polling" border="none"></u--input>
            <u--input readonly v-model="item.parameter.inspection" border="none"></u--input>
          </u-form-item>
        </view>
        <NoData v-if="parameterList.length == 0"></NoData>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer2">
      <view @click="submit">设置</view>
      <view @click="gotoQuery">查看已设置数据</view>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },

      rulesTip: {
        machineName: '设备编号不能为空',
        productSpecName: '产品编码不能为空',
      },
      model: {},
      columns: [],
      ProcessOperationNameList: [],
      ProductSpecNameList: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      showSelect: false,
      focusObj: {
        durableName: false,
        materialPosition: false
      },
      parameterList: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productSpecName) {
        obj = this.columns.find(item => item.productSpecName === this.model.productSpecName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.cardName': {
      handler(val) {
        if (val) {
          this.changeCardName(val)
        }
      }
    },
  },
  async onLoad(options) {

    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)


    this.initModel()
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/MaterialLoading/SetProductionModel/modules/SetProductionModules?machineName=${this.model.machineName}&machineSpecDesc=${this.model.machineSpecDesc}`,
      })
    },
    focusEvent(type) {
      this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备号
        machineSpecDesc: null, // 设备描述
        processOperationName: '', // 工序
        productSpecName: null, // 产品编码
        cardName: null, // 工艺卡编号
      }
      this.parameterList = []
    },
    // 选择
    async selectFirm(e) {
      if (e.value[0].productSpecName) {
        this.model.cardName = e.value[0].cardName
        this.model.cardRevision = e.value[0].cardRevision
        this.model.productSpecName = e.value[0].productSpecName
        this.changeCardName()
      } else {
        this.model.processOperationName = e.value[0]
        // 获取产品列表
        let data = {
          machineName: this.model.machineName,
          processOperationName: this.model.processOperationName
        }
        let resData = await this.$service.SetProductionModel.GetProductSpecInfo(data)
        resData.datas.forEach(item => {
          item.labelText = `${item.productSpecName} / ${item.description}`
        });
        this.ProductSpecNameList = resData.datas
      }
      this.select = false
    },
    async changeCardName() {
      let data = {
        cardRevision: this.model.cardRevision,
        cardName: this.model.cardName
      }
      let resData = await this.$service.SetProductionModel.GetMachineInputParamInfo(data)
      this.parameterList = resData.datas
      if (this.parameterList.length > 0) {
        this.parameterList.forEach(item => {
          item.parameter = JSON.parse(item.parameter)
        });
      }
    },
    clickProcessOperationName() {
      this.select = true
      this.columns = this.ProcessOperationNameList
    },
    clickProductSpecName() {
      this.select = true
      this.columns = this.ProductSpecNameList
    },
    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.SetProductionModel.MachineTaskApiMerge(params).then(res => {
        this.$Toast('设置成功！')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.SetProductionModel.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, machineSpecDesc, processOperationName, processOperationNames, productSpecName, cardName, cardRevision } = res.datas[0]
          this.model.machineSpecDesc = machineSpecDesc
          this.model.machineName = machineName
          // this.model.productSpecName = productSpecName
          this.model.cardName = cardName
          this.model.cardRevision = cardRevision
          this.ProcessOperationNameList = processOperationNames.split(',')
          // if (this.ProcessOperationNameList.length > 1) {
          //   this.showSelect = true
          //   if (processOperationName) {
          //     this.model.processOperationName = processOperationName
          //   }
          // } else {
          //   this.showSelect = false
          //   this.model.processOperationName = processOperationName || processOperationNames
          // }
          if (this.ProcessOperationNameList.length > 0) {
            this.showSelect = true
            this.model.processOperationName = this.ProcessOperationNameList[0]
          } else {
            this.showSelect = false
            this.model.processOperationName = processOperationName || processOperationNames
          }
          // 获取产品列表
          let data = {
            machineName,
            processOperationName: this.model.processOperationName
          }
          let resData = await this.$service.SetProductionModel.GetProductSpecInfo(data)
          resData.datas.forEach(item => {
            item.labelText = `${item.productSpecName} / ${item.description}`
          });
          this.ProductSpecNameList = resData.datas
        } else {
          this.initModel()
          this.$Toast('设备不存在！')
        }
      } catch (error) {
        this.initModel()
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
