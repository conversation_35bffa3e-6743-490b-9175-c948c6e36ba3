<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="材料卸料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="topContainer ma5">
      <u--form labelPosition="left" :model="modelForm" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="modelForm.machineName" border="none" focus placeholder="请扫描或输入设备编号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>
        <u-form-item label="设备描述" labelWidth="100">
          <u--input readonly v-model="modelForm.machineDesc" border="none"></u--input>
        </u-form-item>
      </u--form>
    </view>
    <view class="listContainer pa10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower" refresher-background="#f3f3f7" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(item, index) in model" :key="index">
            <view class="pb10 bb_999_dashed c_00b17b">
              <view class="lin30">
                <text class="mr10">位置：</text>
                <text>{{ item.materialPosition }}</text>
              </view>
              <view class="lin30">
                <text class="mr10">材料：</text>
                <text>{{ item.consumableSpecName }} / {{ item.consumableSpecDesc }}</text>
              </view>
            </view>
            <view class="ele_consumable" v-for="(ele, eleIndex) in item.pdaConsumableItemList" :key="eleIndex">
              <view class="flex between h40 hcenter c_999">
                <view>标签条码</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料数量</view>
                <view>{{ ele.quantity }} {{ ele.consumeUnit }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料时间</view>
                <view>{{ ele.lastEventTime }}</view>
              </view>
              <view>
                <u-button type="success" text="卸载" @click="uninstall(item, ele)"></u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import RedScan from "@/mixins/RedScan";
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  mixins: [RedScan, ScrollMixin],
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      modelForm: {},
      searchModel: {},
      model: [],
    }
  },
  watch: {
    'modelForm.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
    this.initSearchModel()
  },
  methods: {
    initModel() {
      this.modelForm = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
      }
    },
    initSearchModel() {
      this.searchModel = {
        machineName: null,
        pageNo: this.pageNumber,
        limit: this.pageSize,
      }
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      // setTimeout(() => {
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return
      let params = JSON.parse(JSON.stringify(this.searchModel))
      params.machineName = this.modelForm.machineName
      await this.$service.materialunFeeding.queryConsumableItemPageList(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.data.records : [...this.model, ...res.data.records]
          // console.log('res.data.pages', res.data.pages, this.searchModel.pageNo);
          if (this.searchModel.pageNo == res.data.pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
          // uni.stopPullDownRefresh()
        }
      }).catch((e) => {
        this.refresherTriggered = false
        // uni.stopPullDownRefresh()
      })
    },

    /* 设备编号 */
    changeMachineName(value) {
      if (!value) {
        this.model = []
        return
      }
      this.modelForm.machineDesc = null
      let params = {
        machineName: value,
      }
      this.$service.materialunFeeding.queryMachineInfo(params).then(res => {
        this.modelForm.machineDesc = res.data.machineDesc
      })
      this.getData(true, true)
    },

    uninstall(item, ele) {
      uni.showModal({
        title: '提示',
        content: '是否确认下料',
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              consumableName: ele.consumableName,
              machineName: this.modelForm.machineName,
              materialPosition: item.materialPosition,
              userId: this.$getLocal(USER_ID)
            }
            this.$service.materialunFeeding.unload(params).then(res => {
              this.$Toast('卸料成功')
              setTimeout(() => {
                this.getData(true, true)
              }, 1000);
            })
          }
          if (res.cancel) { }
        },
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.modelForm.machineName = 'C1Z001002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.modelForm, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import "../../../styles/uform.scss";
@import "../../../styles/publicStyle.scss";
.ele_consumable {
  &:not(:last-child) {
    border-bottom: 1px dashed #999999;
  }
}
</style>
