<template>
  <view class="bc_fff listPage">
    <!-- <u-navbar title="材料上料-已上料明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar> -->
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="listContainer ma10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(item, index) in simpleTrackProduct" :key="index">
            <view class="topContainer bc_999 br12 ma10">
              <view class="flex h30 hcenter c_00b17b">
                <view>{{ nlsMap.lbmaterialLocationName }}</view>
                <view class="ml6">{{ item.data[index].materialLocationName }}</view>
              </view>
              <view class="flex h30 hcenter c_00b17b">
                <view>材料:</view>
                <view class="ml6">{{ item.data[index].consumableSpecName }}</view>
              </view>
            </view>
            <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index) in item.data" :key="index">
              <view class="flex between h40 hcenter c_999">
                <view>标签条码:</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料时间:</view>
                <view>{{ ele.createTime }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料数量(kg):</view>
                <view>{{ ele.createQuantity }}</view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        // 需要维护再外层
        // LoadedDetails
      },
      machineName: '',
      simpleTrackProduct: []
    };
  },

  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.lbSumTitle // 标题
    this.nlsMap = nlsMap

    this.machineName = options && options.machineName
    this.getData()
  },

  methods: {
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      return resArr
    },
    getData(clearOldData = false, refresh = false) {
      let params = {
        machineName: this.machineName,
      }
      this.$service.MaterialLoading.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>