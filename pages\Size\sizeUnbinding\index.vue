<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="涂布工序-浆料批次解绑" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="涂布机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入涂布机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="涂布机描述" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + ' / ' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="配方号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.consumableSpecName ? model.consumableSpecName + ' / ' + model.bomcomponentDesc : '' }}
            <!-- <u-icon @click="gotoBatchQuery" name="info-circle-fill" color="#2979ff" size="28"></u-icon> -->
          </view>
        </u-form-item>

        <u-form-item label="储蓄罐" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.carrierName }}</view>
        </u-form-item>

        <u-form-item label="浆料出货牌号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="完工时间" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.createTime }} </view>
        </u-form-item>

        <u-form-item label="指定数量(kg)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.createQuantity }} </view>
        </u-form-item>

        <u-form-item label="当前数量(kg)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.quantity }} </view>
        </u-form-item>

        <!-- <u-form-item label="气动泵" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.portName }} </view>
        </u-form-item> -->

        <u-form-item label="指定时间" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.apponitTime ? moment(model.apponitTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
        </u-form-item>

        <u-form-item label="剩余数量(kg)" required borderBottom labelWidth="130">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.remaingQuantity" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>

        <view v-for="(item, index) in lotNames" :key="index">
          <u-form-item :label="'产出批次' + (index + 1)" required borderBottom labelWidth="130">
            <view class="w100x flex right"> {{ item.consumerId }} </view>
          </u-form-item>
          <u-form-item :label="'产出批次' + (index + 1) + '数量（M）'" labelWidth="180" borderBottom v-if="item.type == 'End'">
            <view class="w100x flex right"> {{ item.productQuantity }} </view>
          </u-form-item>
          <u-form-item :label="'产出批次' + (index + 1) + '数量（M）'" required labelWidth="180" borderBottom v-else>
            <view class="w100x flex right">
              <view class="flex w50x hcenter">
                <u--input v-model="item.productQuantity" border="bottom" type="number"></u--input>
              </view>
            </view>
          </u-form-item>
        </view>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">解绑</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
import _ from "lodash";
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changesaveNo = this.$debounce(this.changesaveNo, 1000)
    return {
      rulesTip: {
        machineName: '涂布机编号不能为空',
        remaingQuantity: '剩余数量不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      content: '',
      lotNames: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    // 'model.carrierName': {
    //   handler(val) {
    //     this.changesaveNo(val)
    //   }
    // }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    /* 储蓄编码 */
    async getLotNames() {
      let params = {
        machineName: this.model.machineName,
        lotName: this.model.lotName,
      }
      try {
        let res = await this.$service.Size.getCoatingQuantity(params)
        if (res.datas.length > 0) {
          this.lotNames = res.datas
        } else {
          this.lotNames = []
        }
      } catch (error) {
        this.lotNames = []
      }
    },
    moment,
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 涂布机
        machineSpecDesc: null, // 涂布机描述
        productOrderName: null, // 工单号
        productSpecName: null, // 产品编码
        consumableSpecName: null, // 配方号
        carrierName: '', // 储蓄编码
        lotName: null, // 浆料出货牌号
        createTime: null, // 完工时间
        plannedOutPut: null, // 数量
        remaingQuantity: null // 剩余数量
      }
      this.lotNames = []
    },

    gotoBatchQuery() {
      uni.navigateTo({
        url: `/pages/Size/sizeDesignation/modules/stockBatchQuery`,
      })
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.saveNo = false
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.saveNo = true
      this.select = false
    },
    submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确搅拌机号')
      }
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.remaingQuantity > this.model.createQuantity) {
        return this.$Toast('剩余数量不能大于指定数量！')
      } else if (this.model.remaingQuantity < 0) {
        return this.$Toast('剩余数量不能小于0！')
      }
      uni.showModal({
        title: '提示',
        content: `设备${this.model.machineName}，现解绑浆料${this.model.lotName},剩余数量：${this.model.remaingQuantity}，请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let flag = false
            let tip = ''
            let num = 0
            this.lotNames.forEach((item, index) => {
              if (item.type != 'End') {
                if (!item.productQuantity) {
                  flag = true
                  tip = `产出批次${index + 1}数量（M）不能为空`
                } else {
                  if (!isNaN(item.productQuantity)) {
                    num += Number(item.productQuantity)
                  }
                }
              } else {
                if (!isNaN(item.productQuantity)) {
                  num += Number(item.productQuantity)
                }
              }
            })
            this.allCount = num
            if (flag) {
              return this.$Toast(tip)
            }
            if (this.model.remaingQuantity == 0) {
              const params = {
                carrierName: this.model.carrierName
              }
              this.$service.Size.verificationCarrierName(params).then(res => {
                if (res.success) {
                  if (res.datas.length > 0) {
                    let arr = res.datas.filter(item => item.machineName == this.model.machineName).map(i => i && i.machineName)
                    if (arr.length > 0) {
                      this.show = true
                      let stringTitle = arr.join()
                      this.content = `此储蓄罐还绑定了涂布机[${stringTitle}],需一并解绑，请确认!`
                    } else {
                      this.deAssignCoating(num)
                    }
                  }
                }
              })
              return
            }
            this.deAssignCoating(num)
          }
          if (res.cancel) { }
        },
      })
    },
    // 解绑
    deAssignCoating(num) {
      let params = {
        ...this.model,
        mixingOutQuantity: num
      }
      this.$service.Size.deAssignCoating(params).then(res => {
        if (res.success) {
          this.$Toast('解绑成功')
          this.initModel()
        }
      })
    },
    // 二次确认
    confirm() {
      this.deAssignCoating(this.allCount)
    },

    /* 搅拌机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.Size.getDeassignInfoByMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          if (!this.model.lotName) {
            this.$Toast(`涂布机${this.model.machineName}没有绑定出货牌号!`)
            this.initModel()
            return
          }
          if (!this.model.processOperationName) {
            this.$Toast(`涂布机${this.model.machineName}没有绑定工序!`)
            this.initModel()
            return
          }
          if (!this.model.machineSpecDesc) {
            this.$Toast(`涂布机不存在!`)
            this.initModel()
            return
          }
          // 查询条码信息
          this.getLotNames()
        } else {
          this.model.machineName = ''
          this.$Toast('未找到涂布机信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 储蓄编码 */
    async changesaveNo(value) {
      if (!value) return
      let params = {
        carrierName: value,
        machineName: this.model.machineName,
      }
      try {
        let res = await this.$service.Size.queryPdaSaveNo(params)
        if (res.datas.length > 0) {
          let { lotName, createTime, plannedOutPut } = res.datas[0]
          this.model.lotName = lotName
          this.model.createTime = createTime
          this.model.plannedOutPut = plannedOutPut
        } else {
          this.$Toast('暂无信息！')
        }
      } catch (error) {
        this.model.carrierName = null
        this.model.lotName = null
        this.model.createTime = null
        this.model.plannedOutPut = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.TBJ0.02.01'
          break;
        case 'saveNo':
          this.model.areaName = 'cxgtest5'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'saveNo') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
