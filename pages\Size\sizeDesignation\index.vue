<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="涂布工序-浆料批次指定" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="涂布机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入涂布机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="涂布机描述" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + ' / ' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="配方号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.consumableSpecName ? model.consumableSpecName + ' / ' + model.bomcomponentDesc : '' }}
            <u-icon class="ml2" @click="gotoBatchQuery" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="浆料出货牌号" required borderBottom labelWidth="130">
          <u--input v-model="model.lotName" border="none" :focus="focusObj.lotName" placeholder="请扫描或输入出货牌号" @focus="focusEvent('lotName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>

        <u-form-item label="储蓄罐" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.carrierName }} </view> 
        </u-form-item>

        <u-form-item label="完工时间" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.createTime }} </view>
        </u-form-item>

        <u-form-item label="数量(kg)" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.quantity }} </view>
        </u-form-item>

        <!-- <u-form-item label="气动泵" borderBottom required labelWidth="100">
          <u--input v-model="model.portName" border="none" placeholder="请扫描或输入气动泵" @focus="focusEvent('portName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('portName')"></view>
        </u-form-item> -->

        <u-form-item label="指定时间" borderBottom labelWidth="130">
          <view class="w100x flex right"></view>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view v-if="!disabledBtn" class="btnContainer" @click="submit">确认</view>
    <view v-else class="btnContainer disabled">确认</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changesaveNo = this.$debounce(this.changesaveNo, 1000)
    return {
      rulesTip: {
        machineName: '涂布机编号不能为空',
        lotName: '浆料出货牌号不能为空',
        // portName: '气动泵不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        carrierName: false,
        materialPosition: false
      },
      disabledBtn: false
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.lotName': {
      handler(val) {
        this.changesaveNo(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 涂布机
        machineSpecDesc: null, // 涂布机描述
        productOrderName: null, // 工单号
        productSpecName: null, // 产品编码
        consumableSpecName: null, // 配方号
        carrierName: '', // 储蓄编码
        lotName: null, // 浆料出货牌号
        createTime: null, // 完工时间
        quantity: null, // 数量
      }
    },
    gotoBatchQuery() {
      if (this.model.consumableSpecName) {
        uni.navigateTo({
          url: `/pages/Size/sizeDesignation/modules/stockBatchQuery?consumableSpecName=${this.model.consumableSpecName}`,
        })
      } else {
        this.$Toast('暂无浆料批次信息！')
      }
    },
    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.carrierName = false
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.carrierName = true
      this.select = false
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.Size.singleCoatingFIFOCheck(params).then(res => {
        if (res.success && res.message) {
          this.show = true
          this.content = res.message
        } else {
          this.$Toast('绑定成功!')
          this.initModel()
        }
      })
    },

    confirm() {
      // 继续上卷
      let params = {
        ...this.model,
      }
      this.$service.Size.assignCoating(params).then(res => {
        if (res.success) {
          this.$Toast('绑定成功')
          this.initModel()
          this.show = false
        }
      })
    },

    /* 涂布机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.Size.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
        } else {
          this.model.machineName = ''
          this.$Toast('未找到涂布机信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 浆料出货牌 */
    async changesaveNo(value) {
      if (!value) return
      let params = {
        lotName: value,
        machineName: this.model.machineName,
      }
      try {
        let res = await this.$service.Size.queryPdaSaveNo(params)
        if (res.datas.length > 0) {
          let { carrierName, createTime, quantity } = res.datas[0]
          this.model.carrierName = carrierName
          this.model.createTime = createTime
          this.model.quantity = quantity
          const data = {
            lotName: value,
            machineName: this.model.machineName,
            productOrderName: this.model.productOrderName
          }
          this.$service.Size.singleCoatingCheck(data).then(res => {
            this.disabledBtn = false
          }).catch(() => {
            this.disabledBtn = true
          })

        } else {
          this.$Toast('浆料配方号与涂布机工单配方号不一致！')
          this.model.carrierName = null
          this.model.lotName = null
          this.model.createTime = null
          this.model.quantity = null
        }
      } catch (error) {
        this.model.carrierName = null
        this.model.lotName = null
        this.model.createTime = null
        this.model.quantity = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA01'
          break;
        case 'carrierName':
          this.model.areaName = 'cxgtest5'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'carrierName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
