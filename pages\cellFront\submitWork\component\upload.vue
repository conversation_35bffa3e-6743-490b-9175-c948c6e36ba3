<template>
  <view>
    <u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple :maxCount="10"></u-upload>
  </view>
</template>

<script>
export default {
  name: 'MesTworkPdaUpload',
  props: {
    fileList: { // 默认显示的图片列表，数组元素为对象，必须提供url属性
      type: String,
      default: '',
    }
  },
  data() {
    return {
      fileList1: [],
    };
  },
  methods: {
    // 删除图片
    deletePic(event) {
      console.log('event',event);
      this[`fileList${event.name}`].splice(event.index, 1)
    },
    // 新增图片
    async afterRead(event) {
      console.log('event',event);
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let fileListLen = this[`fileList${event.name}`].length
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中'
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url)
        let item = this[`fileList${event.name}`][fileListLen]
        this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
          status: 'success',
          message: '',
          url: result
        }))
        fileListLen++
      }
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: 'http://************:7001/upload', // 仅为示例，非真实的接口地址
          filePath: url,
          name: 'file',
          formData: {
            user: 'test'
          },
          success: (res) => {
            setTimeout(() => {
              resolve(res.data.data)
            }, 1000)
          }
        });
      })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>