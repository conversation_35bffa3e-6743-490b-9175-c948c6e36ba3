<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="搅拌工序-产出确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="搅拌机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入搅拌机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="搅拌机描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDescription" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.machinePolarity }}
          </view>
        </u-form-item>

        <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.lotName }}
          </view>
        </u-form-item>
        <div class="fs16 fb ml22 mt10" style="color: #02a7f0">
          <span>产品要求</span>
        </div>
        <!-- 产品要求 -->
        <u-form-item label="浆料粘度(Mpa.s)" borderBottom labelWidth="150">
          <view class="w100x flex right"> {{ model.viscosityRequest }} </view>
        </u-form-item>

        <u-form-item label="粘度测量说明" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.viscosityRequestDesc }} </view>
        </u-form-item>

        <u-form-item label="颗粒度(um)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.particleSizeRequest }} </view>
        </u-form-item>

        <u-form-item label="颗粒度测量(量程50μm)" borderBottom labelWidth="200">
          <view class="w100x flex right"> {{ model.particleSizeRequestDesc }} </view>
        </u-form-item>

        <u-form-item label="固含量(%)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.solidContentRequest }} </view>
        </u-form-item>

        <u-form-item label="固含量测量说明" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.solidContentRequestDesc }} </view>
        </u-form-item>

        <div class="fs16 fb ml22 mt10" style="color: #02a7f0">
          <span>产出确认</span>
        </div>
        <u-form-item required label="良品数量(kg)" labelWidth="150">
          <view class="w100x flex right"> {{ model.outPutQuantity }} </view>
        </u-form-item>
        <u-form-item required label="浆料粘度(Mpa.s)" labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.viscosity" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>
        <u-form-item required label="颗粒度(um)" labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.particleSize" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>
        <u-form-item required label="固含量(%)" labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.solidContent" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
    </view>

    <!-- <view class="btnContainer2">
      <view @click="submit">产出确认</view>
      <view @click="printPackage">条码打印</view>
    </view> -->
    <view class="btnContainer" @click="submit">产出确认</view>
  </view>
</template>

<script>
import PrintPackageMixin from "@/mixins/printPackage";
export default {
  mixins: [PrintPackageMixin],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '搅拌机编号不能为空',
        outPutQuantity: '良品数量不能为空',
        viscosity: '浆料粘度不能为空',
        particleSize: '颗粒度不能为空',
        solidContent: '固含量不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        lotName: null,	// 出货牌号	string	
        machineDescription: null,	// 	设备描述	string	
        machineName: null,	// 	设备	string	
        machinePolarity: null,	// 设备极性	string	
        outPutQuantity: null,	// 	良品数量	number(double)	
        particleSize: null,// 	颗粒度	number(double)	
        particleSizeRequest: null,// 产品要求-颗粒度	string	
        particleSizeRequestDesc: null,// 	产品要求-颗粒度测量说明	string	
        solidContent: null,	// 固含量	number(double)	
        solidContentRequest: null,// 	产品要求-固含量	string	
        solidContentRequestDesc: null,// 	产品要求-固含量测量说明	string	
        viscosity: null,// 	浆料粘度	number(double)	
        viscosityRequest: null,	// 	产品要求-浆料粘度	string	
        viscosityRequestDesc: null	// 产品要求-粘度测量说明
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      uni.showModal({
        title: '提示',
        content: `设备${this.model.machineName}，现产出${this.model.outPutQuantity}千克, 请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              ...this.model
            }
            this.$service.stirProduce.endMixing(params).then(res => {
              this.$Toast('操作成功！')
              this.initModel()
              setTimeout(() => {
                this.printPackage(res.datas[0], 'Out') // 打印
              }, 800);
            })
          }
          if (res.cancel) { }
        },
      })
    },

    /* 搅拌机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.stirProduce.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
        } else {
          this.model.machineName = ''
          this.$Toast('未找到搅拌机信息!')
        }
      } catch (error) {
        this.model.machineName = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'GF20-01-JB01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
