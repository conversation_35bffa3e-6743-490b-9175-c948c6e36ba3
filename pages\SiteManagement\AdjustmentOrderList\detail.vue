<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>

    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="调出仓库" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.prevStockCode, model.prevStockText) }}
          </view>
        </u-form-item>

        <u-form-item label="调出库位" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.prevStockLocationCode, model.prevStockLocationText) }}
          </view>
        </u-form-item>

        <u-form-item label="调入仓库" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockCode, model.stockText) }}
          </view>
        </u-form-item>

        <u-form-item label="调入库位" labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockLocationCode, model.stockLocationText) }}
          </view>
        </u-form-item>
      </u--form>
      <view class="mt10">
        <view class="mb10" style="color: #409eff"> 调拨明细 </view>
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">库存数量</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.quantity }}</view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>

        <!-- <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h30 hcenter c_999">
              <view> 物料条码</view>
              <view> {{ ele.consumableName }}</view>
            </view>
            <view class="flex between h30 hcenter c_999">
              <view> 库存数量</view>
              <view>{{ ele.quantity }}</view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view> -->
      </view>
    </view>

    <view class="flex pa5" v-if="paramsState == 'Submit'">
      <u-button type="error" text="驳回" @click="rejectHandle"></u-button>
      <u-button type="success" text="确认" @click="finishHandle"></u-button>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },

      paramsNo: '',
      paramsState: '',
      model: {},
      list: [],
      orginList: [],

    }
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    console.log('nlsMap', nlsMap);
    this.pageTitle = nlsMap.detailTitle // 标题

    this.paramsNo = options.adjustmentOrderNo
    this.paramsState = options.state
    this.nlsMap = nlsMap

    this.initModel()
    this.getDetail()
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('returnRefresh')
    },
    initModel() {
      this.model = {
        // 调出
        prevStockCode: '',// 
        prevStockText: '', // 
        prevStockLocationCode: '', // 库位
        prevStockLocationText: '', // 库位
        // 调入
        stockText: '', // 
        stockLocationCode: '', // 库位
        stockLocationText: '', // 库位
        trayReturnTypeText: '', // 退库类型
      }
    },
    async getDetail() {
      let parmas = {
        adjustmentOrderNo: this.paramsNo
      }
      let res = await this.$service.AdjustmentOrderList.listAdjustmentOrder(parmas)
      this.model.prevStockCode = res.datas[0].prevStockCode
      this.model.prevStockText = res.datas[0].prevStockText
      this.model.prevStockLocationCode = res.datas[0].prevStockLocationCode
      this.model.prevStockLocationText = res.datas[0].prevStockLocationText

      this.model.stockCode = res.datas[0].stockCode
      this.model.stockText = res.datas[0].stockText
      this.model.stockLocationCode = res.datas[0].stockLocationCode
      this.model.stockLocationText = res.datas[0].stockLocationText
      this.list = res.datas[0].adjustmentOrderDetailList
      // this.orginList = res.datas[0].returnOrderItemDetailList
      // let grouped = list.reduce((acc, obj) => {
      //   const groupKey = obj.trayNo;
      //   if (!acc[groupKey]) {
      //     acc[groupKey] = { trayNo: groupKey, returnItemConsumableList: [] };
      //   }
      //   acc[groupKey].returnItemConsumableList.push({
      //     returnQuantity: obj.returnQuantity,
      //     consumableName: obj.consumableName
      //   });
      //   return acc;
      // }, {});
      // let result = Object.values(grouped);
      // this.list = result
    },

    finishHandle() {
      let params = {
        adjustmentOrderItemList: this.list,
        adjustmentOrderNo: this.paramsNo,
        ...this.model,

      }
      this.$service.AdjustmentOrderList.finish(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.leftClick()
        }, 1500);
      })
    },
    rejectHandle() {

      uni.showModal({
        title: '提示',
        content: `是否确认驳回？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              adjustmentOrderItemList: this.list,
              adjustmentOrderNo: this.paramsNo,
              ...this.model,

            }
            this.$service.AdjustmentOrderList.reject(params).then(res => {
              this.$Toast('操作成功!')
              setTimeout(() => {
                this.leftClick()
              }, 1500);
            })
          }
          if (res.cancel) { }
        },
      })


    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
