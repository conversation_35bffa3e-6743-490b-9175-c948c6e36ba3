<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="匀浆胶料->已指定胶料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="myContainer ma10">
      <view>
        <view class="mb10 br10 bc_fff pa10" v-for="(item, index) in model" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>浆料批次</view>
            <view>{{ item.lotName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>工单</view>
            <view>{{ item.productRequestName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>数量</view>
            <view class="flex hcenter">
              <view class="mr20">{{ item.productQuantity }}{{ item.productQuantityUnit }}</view>
              <view>{{ item.lotWeight }}{{ item.lotWeightUnit }}</view>
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>批次指定时间</view>
            <view>{{ item.lastLoggedInTime }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>批次完成时间</view>
            <view>{{ item.releaseTime }}</view>
          </view>
        </view>
      </view>
      <NoData v-if="!model || model.length === 0"></NoData>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
export default {
  name: 'MesTworkPdaDetail',
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      model: [],
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.getData()
  },
  methods: {
    getData() {
      // setTimeout(() => {
      //   let res = Array.from({length:20},(v, i) => i)
      //   this.model = res
      // }, 1000)
      // return
      let params = {
        machineName: this.machineName,
      }
      this.$service.homogenateRubber.queryAssignLot(params).then((res) => {
        this.model = res.data
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import "../../../styles/publicStyle.scss";

.ele_consumable {
  &:not(:last-child) {
    border-bottom: 1px dashed #999999;
  }
}
</style>