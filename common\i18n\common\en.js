export default {
	common: {
		button: {
			confirm: 'Confirm',
			cancel: 'Cancel'
		},
		form: {
			pleaseSelect: 'PleaseSelect'
		}
	},
	menu: {
		MaterialManage: ' material Management',
		InStorageSearch: ' scan code into the database',
		OutStorageSearch: ' scan the code out of the library',

		EquipmentManagement: "Equipment Management",
		SetProductionModel: "Device Type Change Settings",
		EquipmentCleaning: "Equipment Cleaning",
		EquipmentCheck: "Equipment Calibration",
		printSearch: "Re print Query",
		Tuning: "Adjusting the machine",
		MaterialLoading: "Material loading",
		LoadingMaterial: "Material loading",
		UnloadingMaterial: "Material unloading",
		MaterialReceipt: "Material Receipt",
		Polar: "Polar roll up",
		PolarRollUp: "PolarRollUp",
		PolarCombined: "Wrap up roll up roll up",
		PolarUnload: "PolarUnload",
		Production: "Mixing process",
		stirStart: "Stir_start",
		stirFeeding: "Stirring feeding",
		stirStir: "Stir Stir",
		stirProduce: "Stir Output",
		stirExceptionHandling: "StirException Handling",
		Size: "Coating process",
		sizeDesignation: "Slurry Specific",
		sizeUnbinding: "Slurry Unbinding",
		coatingStart: "Single sided start",
		coatingSingleDown: "Single sided completion",
		coatingDoubleStart: "Double sided start",
		coatingDoubleDown: "Double sided completion",
		roll_In: "Rolling process",
		roll_InStart: "Roll Press Start",
		roll_InDown: "RollerCompletion",
		roll_InStartSecond: "Second roll press start",
		roll_InDownSecond: "Second roll press completion",
		Slitting: "Slitting process",
		slittingStart: "Split start",
		slittingDown: "Splitting completion",
		bake: "Baking process",
		bakeStart: "Polar ScrollStart",
		bakeEnd: "Polar ScrollEnd",
		bakeStartCell: "Cell Start",
		bakeEndCell: "Cell End",
		DieCutting: "Die cutting process",
		DieCutting_RollUp: "DieCutting RollUp",
		DieCutting_Begin: "DieCutting Start",
		DieCutting_Blanking: "DieCutting Blanking",
		DieCutting_Unroll: "DieCutting Unroll",
		stand: "Static process",
		standStart: "StandStart",
		standEnd: "StandEnd",
		semiManufacturesBindingAndUnbinding: "Binding and Unbinding Vehicles for Work in Progress",
		BindingAndUnbinding: "Bind_Unbind",
		punchingShell: "Punching and unloading on semi-finished product carriers",
		shellOnoadMaterial: "Punching ShellLoading",
		unloadMaterial: "Punching Shell Unloading",
		LoadingSemiFinished: "Semi finished product loading",
		UnloadingSemiFinished: "Semi finished product unloading",
		QualityControl: "Quality Management",
		MaterialRecord: "Material Record",
		MaterialDetermination: "Material Determination",
		SemiRecord: "Work in Progress Record",
		SemiDetermination: "Work in Progress Determination",
		FirstInspectionTask: "First Inspection Task",
		FirstInspectionDone: "First Inspection Pending",
		FirstInspectionSample: "First Inspection Sampling",
		FirstInspectionParams: "First Inspection Parameters",
		FirstInspectionDecision: "First Inspection Decision",
		InspectionTask: "Inspection Task",
		InspectionDone: "Inspection pending",
		InspectionSample: "Inspection Sampling",
		InspectionParams: "Inspection Parameters",
		InspectionDecision: "Inspection Decision",
		ToolingAndInspection: "Tools",
		ToolingClean: "Tool Cleaning",
		ToolingCheck: "Fixture Calibration",
		ToolingInstall: "Tool Installation",
		ToolingUnload: "Tool Unloading",
		ProcessAdjustment: "Process Adjustment",
		WorkingProcessAdjustment: "Under Production Adjustment",
		SingleVolumeStart_END: "Single Volume Start_Lower Volume",
		SingleVolumeStart: "Single Volume Start",
		coatingVolume: "Completing Volume 2",
	}
}