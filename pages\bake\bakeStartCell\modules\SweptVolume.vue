<template>
  <view class="bc_fff listPage">
    <u-navbar title="已扫载具查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>烘烤机:</view>
        <view class="ml6">{{ machineName }}</view>
      </view>
      <view class="flex h40 hcenter c_999">
        <view>烘洞号:</view>
        <view class="ml6">{{ portName }}</view>
      </view>
      <!-- <view class="flex h40 hcenter c_999">
        <view>烘烤标准:</view>
        <view class="ml6">{{ portName }}</view>
      </view> -->
      <view class="flex h40 hcenter c_999">
        <view>已扫载具数:</view>
        <view class="ml6">{{ lotList.length }}</view>
      </view>
    </view>
    <view class="lin40 fs16 pl10 c_00b17b bb_f5f5f5">已扫描载具信息</view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view>
          <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index) in lotList" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>载具条码:</view>
              <view>{{ ele.carrierName  || '-' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>产品编码:</view>
              <view>{{ ele.productSpecName  || '-' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>产品名称:</view>
              <view>{{ ele.productSpecDesc  || '-' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>规格型号:</view>
              <view>{{ ele.specModel || '-'  }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>扫码时间:</view>
              <view>{{ ele.eventTime ? moment(ele.eventTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}</view>
            </view>
          </view>
          <NoData v-if="!lotList || lotList.length === 0"></NoData>
        </view>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      portName: '',
      lotList: [],
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.portName = e && e.portName
    this.lotList = e && JSON.parse(e.lotList)
  },
  methods: {
    moment,
  }
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>