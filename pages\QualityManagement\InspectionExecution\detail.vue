<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="myContainer ma5">
      <!-- {{ InspectionTypes }} -->
      <view class="ml5 flex between flex_shrink0" style="color: #409eff">
        <view> 检验类型: {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }}</view>
      </view>
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <!-- <u-form-item label="检验类型" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ InspectionTypes }}/ {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }} </view>
        </u-form-item> -->

        <u-form-item label="任务编码" required borderBottom labelWidth="120">
          <view class="w100x flex right" @click="checkSelect('taskNo')">
            <!-- {{model.taskNo}} -->
            <view v-if="model.taskNo">{{ $utils.filterObjLabel(dicts.taskNoList, model.taskNo) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" v-if="lodash.isEmpty(options_Params)" :style="{ transform: select && selectType === 'taskNo' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <template v-if="InspectionTypes != 'ProdcutWarehouseInspection'">
          <u-form-item label="设备" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ $utils.optionShowConfig(model.machineName, model.machineDesc) }}
            </view>
          </u-form-item>

          <u-form-item label="工序" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
            </view>
          </u-form-item>
        </template>

        <template v-if="InspectionTypes == 'ProdcutWarehouseInspection'"> </template>

        <u-form-item label="样品条码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('lotName')">
            <view v-if="model.lotName">{{ $utils.filterObjLabel(dicts.lotNameList, model.lotName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'lotName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.productOrderName }}
          </view>
        </u-form-item>
        <u-form-item label="产品编码" labelWidth="130">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="ml5 mt10 mb10 flex between flex_shrink0" style="color: #409eff">
        <view> 参数录入明细</view>
      </view>
      <view class="">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <!-- <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">参数编码</view> -->
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">参数名称</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">下限值</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">上限值</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">标准值</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验值</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验结果</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">操作</view>
        </view>

        <view class="table_content">
          <view v-for="(item, index) in list" :key="index">
            <!-- <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view> -->
            <!-- <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.itemNo }}</view> -->
            <view class="flex bl_e1e1e1" style="min-height: 100rpx">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.itemName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.lowerLimit }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.upperLimit }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.standardValue }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                <view v-if="item.paramValueType == 'Boolean' || item.paramValueType == 'String'" class="w100x flex around" @click="checkSelect('jgz', index)">
                  <view>{{ item.jgz }}</view>
                  <u-icon name="arrow-down" color="black" size="18"></u-icon>
                </view>

                <view v-else class="w100x flex right">
                  <view class="w100x flex right">
                    <view class="flex w100x hcenter pl10">
                      <u--input v-model="item.jgz" border="none" placeholder="请输入"></u--input>
                    </view>
                  </view>
                </view>
              </view>

              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                {{ getResultText(item, item, index) }}
              </view>

              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">
                <!-- <u-button v-if="item.paramValueType != 'Boolean' && item.paramValueType != 'String'" type="success" text="新增" @click="addAction(item, index)" style="height: 30px; margin: 0 5px"></u-button> -->
                <u-icon class="" v-if="item.paramValueType != 'Boolean' && item.paramValueType != 'String'" @click="addAction(item, index)" name="plus-circle" color="#2979ff" size="28"></u-icon>
              </view>
            </view>

            <view class="">
              <view v-for="(item2, index2) in item.children" :key="index2 + 999" class="flex bl_e1e1e1" style="min-height: 100rpx">
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1"></view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"></view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"></view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"></view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                  <view class="w100x flex right">
                    <view class="w100x flex right">
                      <view class="flex w100x hcenter pl10">
                        <u--input v-model="item2.jgz" border="none" placeholder="请输入"></u--input>
                      </view>
                    </view>
                  </view>
                </view>

                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                  {{ getResultText2(item, item2, index) }}
                </view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">
                  <u-icon @click="delAction(index, index2)" name="minus-circle" color="#2979ff" size="28"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>
<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      lodash: _,
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      rulesTip: {
        taskNo: '任务编码不能为空',
        lotName: '样品条码不能为空',
        // scenceDesc: '场景说明不能为空'
      },
      model: {},
      list: [
        // {
        //   "attributeRef": null,
        //   "createTime": null,
        //   "eventTime": null,
        //   "createUser": null,
        //   "eventUser": null,
        //   "eventName": null,
        //   "eventComment": null,
        //   "trxId": null,
        //   "description": null,
        //   "factoryName": null,
        //   "inspectType": "FirstInspection",
        //   "itemName": "操作侧压力",
        //   "inspectStandard": "",
        //   "standardValue": "50",
        //   "paramValueType": "BigDecimal",
        //   "collectType": "Manua_Collection",
        //   "upperLimit": 65.0,
        //   "lowerLimit": 35.0,
        //   "dataType": "质量",
        //   "inspectResult": null,
        //   "inspectMeans": null,
        //   "serialNo": null,
        //   "unit": null,
        //   "inputNum": null,
        //   "actualValue": null,
        //   "singleResult": null,
        //   "templateNo": "GF-FI202410220010",
        //   "templateNoVersion": "A33",
        //   "itemNo": "RB2-206-02",
        //   "children": []
        // },
      ],
      // list: Array.from({ length: 10 }, (i, x) => ({
      //   paramValueType: 'Integer',
      //   lowerLimit: '1',
      //   upperLimit: '10',
      //   x: x,
      // })),
      dicts: {
        InspectionTypesList: [], // 检验类型
        taskNoList: [],// 任务编码
        lotNameList: [], // 样品条码
        resultList: [
          { label: 'Y', value: 'Y', },
          { label: 'N', value: 'N', }
        ],
      },
      columns: [],
      select: false,
      selectType: '',
      chooseIndex: 0, // 选择下拉框的索引
      InspectionTypes: '',
      options_Params: {},
    }
  },
  computed: {},
  watch: {},
  async onLoad(options) {
    this.initModel()
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.InspectionExecutionTitle // 标题
    this.nlsMap = nlsMap

    this.InspectionTypes = options.InspectionTypes
    this.options_Params = options.params && JSON.parse(options.params) || {}
    console.log('options', options, this.options_Params);

    this.getEnumValue('InspectionTypes', 'InspectionTypesList') // 单据类型
    await this.init_taskNoList()

    if (!_.isEmpty(this.options_Params)) {
      let { taskNo, machineName, processOperationName, lotName, workOrderName, productSpecName, productSpecDesc } = this.options_Params
      this.model.machineName = machineName
      this.model.processOperationName = processOperationName
      this.model.taskNo = taskNo
      // 获取条码
      this.init_LotNameList()
      // 获取参数
      this.init_Inspectiontemplatedetails()
    }
  },
  methods: {
    addAction(item, index) {
      this.list[index].children.push({
        jgz: '',
      })
    },
    delAction(index, index2) {
      this.list[index].children.splice(index2, 1)

    },
    leftClick() {
      this.$utils.backAndUpdata('getInspectionTaskList')
    },
    initModel() {
      this.model = {
        InspectionTypes: '', //检验类型
        taskNo: '', // 任务编码
        machineName: '', // 设备编码
        machineDesc: '', // 设备描述
        processOperationName: '', // 工序编码
        processOperationDesc: '', // 工序描述
        lotName: '', // 样品条码
        productOrderName: '', // 工单号
        productSpecName: '', //  产品编码
        productSpecDesc: '', //  产品描述
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let list = _.cloneDeep(this.list)
      let newarr = []
      for (let index = 0; index < list.length; index++) {
        const ele = list[index];
        if (ele.children.length > 0) {
          let str = ele.jgz
          for (let index = 0; index < ele.children.length; index++) {
            const elec = ele.children[index];
            if (_.isEmpty(elec.jgz)) {
              return this.$Toast('检验结果值不能为空!')
            }
            str += `;${elec.jgz}`
            elec.singleResult = this.getResultText(ele, elec)
          }
          ele.jgz = str
          let flag = ele.children.every(item => item.singleResult == '合格')
          ele.singleResult = flag ? '合格' : '不合格'
        } else {
          ele.singleResult = this.getResultText(ele, ele)
        }
        ele.actualValue = ele.jgz
        newarr.push(ele)
      }

      uni.showModal({
        title: '提示',
        content: `请确认无误后再提交?`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // 取样完成
            let params = {
              inspectType: this.InspectionTypes,
              machineName: this.model.machineName,
              processOperationName: this.model.processOperationName,
              taskNo: this.model.taskNo,
              lotName: this.model.lotName,
              workOrderName: this.model.productOrderName,
              productSpecName: this.model.productSpecName,
              inspectionTaskDetailList: newarr
            }
            this.$service.QualityManagement.paramInputSave(params).then((res) => {
              this.$Toast('参数录入保存成功！')
              // 样品条码清空，参数录入明细清空
              if (_.isEmpty(this.options_Params)) {
                setTimeout(() => {
                  this.model.lotName = ''
                  this.model.productOrderName = ''
                  this.model.productSpecName = ''
                  this.model.productSpecDesc = ''
                  this.list = []
                  // 重新获取条码
                  this.init_LotNameList()
                  // 重新获取参数
                  this.init_Inspectiontemplatedetails()
                }, 500)
              } else {
                //  检验单/待办 进来则返回 有可能有多个  替换无需要放回上一级
                setTimeout(() => {
                  this.leftClick()
                }, 1500);
              }
            })
          }
          if (res.cancel) { }
        },
      })
    },
    getResultText2(item, item2, index) {
      let text = this.getResultText(item, item2)
      return text
    },
    getResultText(item, item2, index) {
      if (item.paramValueType == 'Boolean' || item.paramValueType == 'String') {
        if (item2.jgz == 'Y') {
          return '合格'
        } else if (item.jgz == 'N') {
          return '不合格'
        } else {
          return '不合格'
        }
      } else if (item.paramValueType == 'Integer' || item.paramValueType == 'BigDecimal') {
        let lowerLimit = item.lowerLimit + ''
        let upperLimit = item.upperLimit + ' '
        let standardValue = item.standardValue
        let jgz = Number(item2.jgz)
        if (!_.isEmpty(lowerLimit) && !_.isEmpty(upperLimit)) {
          if (!jgz && jgz != '0') return '不合格'
          if (standardValue == jgz || (jgz >= Number(lowerLimit) && jgz <= Number(upperLimit))) {
            return '合格'
          } else {
            return '不合格'
          }
        }
        // 有最大值
        if (_.isEmpty(lowerLimit) && !_.isEmpty(upperLimit)) {
          if (!jgz && jgz != '0') return '不合格'
          if (jgz <= Number(upperLimit)) {
            return '合格'
          } else {
            return '不合格'
          }
        }
        // 有最小值
        if (!_.isEmpty(lowerLimit) && _.isEmpty(upperLimit)) {
          if (!jgz) return '不合格'
          if (jgz >= Number(item.lowerLimit)) {
            return '合格'
          } else {
            return '不合格'
          }
        }

      } else {
        return '不合格'
      }
    },
    checkSelect(type, index) {
      // 条状页面补给下拉
      if (!_.isEmpty(this.options_Params)) {
        if (type == 'taskNo') return
      }
      this.select = true
      this.selectType = type
      switch (type) {
        case 'taskNo':
          this.columns = this.dicts.taskNoList
          break;
        case 'jgz':
          this.columns = this.dicts.resultList
          this.chooseIndex = index
          break;
        case 'lotName':
          this.columns = this.dicts.lotNameList
          break;
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;

        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'taskNo') {
        this.model.machineName = e.value[0].machineName
        this.model.machineDesc = e.value[0].machineDesc
        this.model.processOperationName = e.value[0].processOperationName
        this.model.processOperationDesc = e.value[0].processOperationDesc

        this.init_LotNameList()
        this.init_Inspectiontemplatedetails()
      }
      if (this.selectType == 'lotName') {
        this.model.productOrderName = e.value[0].workOrderName
        this.model.productSpecName = e.value[0].productSpecName
        this.model.productSpecDesc = e.value[0].productSpecDesc
      }
      if (this.selectType == 'jgz') {
        this.$set(this.list[this.chooseIndex], 'jgz', e.value[0].label)
      }
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    // 任务编码
    init_taskNoList() {
      let params = {
        inspectType: this.InspectionTypes,
      }
      this.$service.QualityManagement.getTaskNoList(params).then(res => {
        this.dicts.taskNoList = res.datas.map((item) => ({
          ...item,
          label: item.taskNo,
          value: item.taskNo
        }))
      })
    },
    //  样品条码
    init_LotNameList() {
      this.model.lotName = ''
      this.model.productOrderName = ''
      this.model.productSpecName = ''
      this.model.productSpecDesc = ''
      this.dicts.lotNameList = []
      let params = {
        taskNo: this.model.taskNo
      }
      this.$service.QualityManagement.getLotNameList(params).then(res => {
        this.dicts.lotNameList = res.datas.map((item) => ({
          ...item,
          label: item.lotName,
          value: item.lotName
        }))
      })
    },
    // 参数明细
    init_Inspectiontemplatedetails() {
      let params = {
        inspectType: this.InspectionTypes,
        taskNo: this.model.taskNo,
        lotName: this.model.lotName
      }
      this.$service.QualityManagement.getInspectiontemplatedetails(params).then(res => {
        this.list = res.datas.map((ele, index) => ({
          children: [],
          ...ele,
          jgz: ele.actualValue,
        }))
      })

    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'NEX-LK1-1F-013' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>

