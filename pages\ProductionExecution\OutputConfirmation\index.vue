<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.description }}
          </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.productOrderName }}
          </view>
        </u-form-item>
        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="出货牌号" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('lotName')">
            <view v-if="model.lotName">{{ $utils.filterObjLabel(dicts.lotNameList, model.lotName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'lotName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="设备上报OK数量" borderBottom labelWidth="160">
          <view class="w100x flex right">
            {{ model.productQuantity }}
          </view>
        </u-form-item>

        <u-form-item label="设备上报NG数量" borderBottom labelWidth="160">
          <view class="w100x flex right">
            {{ model.lossProductQuantity }}
          </view>
        </u-form-item>

        <u-form-item label="NG原因" borderBottom labelWidth="160">
          <view class="w100x flex right">
            {{ model.reasonCode }}
          </view>
        </u-form-item>
      </u--form>
      <!-- <view class="mt20"></view> -->
      <!-- {{ model2 }} -->
      <u--form labelPosition="left" :model="model2" labelWidth="100">
        <view class="fs16 fb ml20 h40 lin40" style="color: #409eff"> 产出确认 </view>

        <u-form-item label="良品数量" borderBottom required labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model2.productQuantity" border="bottom" type="number" placeholder="请输入"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="不良品数量" borderBottom required labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model2.ngQuantity" border="bottom" type="number" placeholder="请输入"></u--input>
            </view>
          </view>
        </u-form-item>

        <view v-for="(item, index) in frontEndParamters" :key="index">
          <u-form-item :label="optionUnit(item.description, item.unit)" borderBottom :required="item.requiredFlag == 'Y'" labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w50x hcenter">
                <u--input v-model="model2[item.paramId]" border="bottom" type="number" placeholder="请输入"></u--input>
              </view>
            </view>
          </u-form-item>
        </view>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import moment from 'moment'
import _ from "lodash";
import useNls from "@/mixins/useNls";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, PrintPackageMixin],
  data() {
    this.changemachineName = this.$debounce(this.changemachineName, 1000)
    return {
      moment,
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      rulesTip: {
        machineName: '设备编码不能为空',
        lotName: '出货牌号不能为空',
      },
      rulesTip2: {
        productQuantity: '良品数量不能为空',
        ngQuantity: '不良品数量不能为空',
      },

      model: {
        machineName: '', //	设备编码
        description: '', // 设备描述
        productOrderName: '', // 工单号
        productSpecName: '',  //产品编码	
        productSpecDesc: '',  // 产品编码描述	
        processOperationName: '',  //工序编码	
        processOperationDesc: '',  // 工序描述		
        lotName: '', // 条码号
        productQuantity: '', // 设备上报产出OK数量	
        lossProductQuantity: '', // 设备上报产出NG数量	
        reasonCode: '', // 设备上报产出NG数量	
      },
      model2: {
        productQuantity: '', // 良品数量	
        ngQuantity: '', // 不良品数量	
      },
      frontEndParamters: [],
      columns: [],
      select: false,
      selectType: '',
      dicts: {
        lotNameList: [],
      },
    }
  },
  computed: {},
  watch: {
    'model.machineName': {
      handler(val) {
        this.changemachineName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
  },
  methods: {
    optionUnit(value1, value2) {
      if (value1 || value2) {
        return (value1 || '') + (value1 && value2 ? `(${value2})` : '')
      }
    },
    async changemachineName(value) {
      this.model.description = ''
      this.model.productOrderName = ''
      this.model.productSpecName = ''
      this.model.productSpecDesc = ''
      this.model.processOperationName = ''
      this.model.processOperationDesc = ''
      this.model.lotName = ''
      this.model.productQuantity = ''
      this.model.lossProductQuantity = ''
      this.model.reasonCode = ''
      this.model2 = {
        productQuantity: '', // 良品数量	
        ngQuantity: '', // 不良品数量	
      }
      this.frontEndParamters = []
      if (!value) return
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.OutputConfirmation.getMachineDataForFinish(params)
        if (res.datas.length > 0) {
          let data = res.datas[0]
          this.model.description = data.description
          this.model.productOrderName = data.productOrderName
          this.model.productSpecName = data.productSpecName
          this.model.productSpecDesc = data.productSpecDesc
          this.model.processOperationName = data.processOperationName
          this.model.processOperationDesc = data.processOperationDesc
          this.dicts.lotNameList = data.lotInfos.map((item) => ({
            label: item.lotName,
            value: item.lotName,
            lossProductQuantity: item.lossProductQuantity,
            productQuantity: item.productQuantity,
            reasonCode: item.reasonCode,
          }))
          // 出牌货号
          this.frontEndParamters = data.frontEndParamters
          //  动态产出参数
          for (const item of data.frontEndParamters) {
            this.$set(this.model2, item.paramId, '')
          }
        }
      } catch (error) {
        this.model.machineName = null
      }
    },



    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'lotName':
          this.columns = this.dicts.lotNameList
          break;
        default:
          break;
      }
    },

    async selectFirm(e) {
      console.log('e', e);
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'lotName') {
        this.model.lossProductQuantity = e.value[0].lossProductQuantity
        this.model.productQuantity = e.value[0].productQuantity
        this.model.reasonCode = e.value[0].reasonCode

      }
    },
    GetAreaNameList() {
      this.$service.common.getDictByQueryId('MachineTask_GetAreaName').then(res => {
        this.dicts.areaNameList = res.datas.map(item => ({
          label: item.areaText,
          value: item.areaName,
        }))
      })
    },

    initModel() {
      this.model = {
        machineName: '', //	设备编码
        description: '', // 设备描述
        productOrderName: '', // 工单号
        productSpecName: '',  //产品编码	
        productSpecDesc: '',  // 产品编码描述	
        processOperationName: '',  //工序编码	
        processOperationDesc: '',  // 工序描述		
        lotName: '', // 条码号
        productQuantity: '', // 设备上报产出OK数量	
        lossProductQuantity: '', // 设备上报产出NG数量	
        reasonCode: '', // 设备上报产出NG数量	
      }
      this.model2 = {
        productQuantity: '', // 良品数量	
        ngQuantity: '', // 不良品数量	
      }
      this.frontEndParamters = []
    },
    // 设置
    submit() {
      // let datas = [
        // {
        //   "ip": "127.0.0.1",
        //   "port": "12345",
        //   "printData": {
        //     a:'111',
        //     b:'222',
        //   },
        //   "printMachineName": "TCL国际E城-G1-3F-南区打印机",
        //   "temeptName": "fenQie.grf"
        // },
        // {
        //   "ip": "***********",
        //   "port": "12346",
        //   "printData": {},
        //   "printMachineName": "",
        //   "temeptName": ""
        // }
      // ]
      // setTimeout(() => {
      //   this.myprintPackage(datas) // 打印
      // }, 800);
      // return
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      for (let key in this.rulesTip2) {
        if (_.isEmpty(this.model2[key])) {
          this.$Toast(this.rulesTip2[key])
          return
        }
      }
      for (const item of this.frontEndParamters) {
        if (item.requiredFlag == 'Y') {
          if (_.isEmpty(this.model2[item.paramId])) {
            this.$Toast(`${item.description}不能为空`)
            return
          }
        }
      }
      let arr = this.frontEndParamters.map(item => ({
        ...item,
        result: this.model2[item.paramId]
      }))
      let params = {
        ...this.model,
        productQuantity: this.model2.productQuantity,
        ngQuantity: this.model2.ngQuantity,
        frontEndParamters: arr,
        eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      }
      console.log('params', params);
      this.$service.OutputConfirmation.Finish(params).then(res => {
        this.$Toast('操作成功!')
        this.initModel()
        if (res.datas.length > 0) {
          setTimeout(() => {
            this.myprintPackage(res.datas) // 打印
          }, 800);
        }
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'NEX-LK1-1F-013' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
