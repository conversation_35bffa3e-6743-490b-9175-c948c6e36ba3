import App from './App'
import Vue from 'vue'
import uView from '@/uni_modules/uview-ui'
import { setLocalStore, getLocalStore, removeStore } from './utils/common/localStore.js'
import Toast from './utils/common/toast.js'
// import control from './utils/common/control.js'
import utils from './utils/common/utils.js'
import service from './http/service'
import debounce from 'lodash/debounce'
import dayjs from 'dayjs'
import store from './store';

import myContainer from '@/components/myContainer/myContainer';
Vue.use(uView)

Vue.prototype.$setLocal = setLocalStore
Vue.prototype.$getLocal = getLocalStore
Vue.prototype.$removeLocal = removeStore
Vue.prototype.$Toast = Toast
Vue.prototype.$debounce = debounce
// Vue.prototype.$control = control
Vue.prototype.$utils = utils
Vue.prototype.$store = store;
Vue.prototype.$dayjs = dayjs;
Vue.prototype.$service = service;

Vue.component('myContainer', myContainer);

// 多语言  
// 方案变更  目前只用在登录界面，其他采用web国际化配置
import { langMessage } from '@/locale/index'
import VueI18n from 'vue-i18n'
Vue.use(VueI18n)
/**
 * en
 * zh-Hans
 * zh-Hant
 */
const DEFAULT_LANGUAGE = 'default_language'
let _lang = uni.getStorageSync(DEFAULT_LANGUAGE)
if (!_lang) {
  uni.setStorageSync(DEFAULT_LANGUAGE, 'zh-Hans')
  _lang = 'zh-Hans'
}
let i18nConfig = {
  locale: _lang,
  fallbackLocale: "zh-Hans",
  silentTranslationWarn: true, // 去除国际化警告
  messages: langMessage()
}
Vue.use(VueI18n)
const i18n = new VueI18n(i18nConfig)
const LANGUAGE_ARRAY = [
  { label: '繁體中文', value: 'zh-Hant' },
  { label: '简体中文', value: 'zh-Hans' },
  { label: 'English', value: 'en' }
]
Vue.prototype.$LANGUAGE_ARRAY = LANGUAGE_ARRAY
// Vue.directive('int', {
//   update: function(el, binding, vnode) {
//     let input = el.querySelector('.uni-input-input')
//     let beforeValue = input.value
//     if(beforeValue) {
//       input.value = beforeValue.match(/^\d*/g)[0]
//       input.dispatchEvent(new Event('input'))
//     }
// let beforeValue = input.value
// let afterValue = input.value.replace(/\D+/, '')
// if(beforeValue !== afterValue){
//   input.value = afterValue
// }
//   }
// })

Vue.directive('decimal3', {
  update: function (el, binding, vnode) {
    let input = el.querySelector('.uni-input-input')
    let beforeValue = input.value
    if (beforeValue) {
      input.value = beforeValue.match(/^\d*(\.?\d{0,3})/g)[0]
      input.dispatchEvent(new Event('input'))
    }
  }
})

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  i18n,
  ...App,
})
app.$mount()
