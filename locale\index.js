import en from './en.json'
import zhHans from './zh-Hans.json'
import zhHant from './zh-Hant.json'
import { messages } from '@/common/i18n/index.js'

/**
 * 多语言数据格式转换
 * @param {Object} messages
 * @param {Object} key
 */
function dataFormatting(messages, key) {
	let map = {};

	function objParse(holder, key, lastKey) {
		if (holder && typeof holder === "object") {
			if (key !== '') {
				lastKey += key + "."
			}
			// 对象遍历
			for (let k in holder) {
				objParse(holder[k], k, lastKey);
			}
		} else {
			lastKey += key
			map[lastKey] = holder
		}
	}

	objParse(messages[key], "", "")

	return map;
}

let i18nMessage = {}
export function langMessage() {
	// 缓存，防止多次转化
	if (Object.keys(i18nMessage).length === 0) {
		i18nMessage = {
			'en': {
				...en,
				
				...dataFormatting(messages, "en")
			},
			'zh-Hans': {
				...zhHans,
				...dataFormatting(messages, "zh-Hans")
			},
			'zh-Hant': {
				...zhHant,
				...dataFormatting(messages, "zh-Hant")
			}
		}

		// compareDiff(i18nMessage)
	}

	return i18nMessage
}

// 比较英文与中文的不同，方便找到英文遗留配置的问题
function compareDiff(i18n) {
	if (Object.keys(i18n).length > 0) {
		let enMap = i18n['en']
		let zhMap = i18n['zh-Hans']
		console.log(enMap)
		console.log(zhMap)
		let array = []
		for (let key in zhMap) {
			if (!enMap[key]) {
				array.push(key)
			}
		}
		console.log(array)
	}
}