image: registry.getech.cn/poros/cifront:1.0.13
#更新到最新镜像 ↑

stages:
  - build
  - docker-build

build:
  stage: build
  only:
    - develop
    - sit
    - master
  except:
    - gd_ui_improve
  script:
    - echo  $CI_PROJECT_NAME
    - node --version
    - export NODE_OPTIONS=--max-old-space-size=8096
    - npm config set registry=https://registry.npm.taobao.org
    - npm install
    - cnpm run build
    - echo "build complete..."
  cache:
    paths:
      - node_modules/
  artifacts:
    name: $CI_PROJECT_NAME
    paths:
      - dist/*


#构建docker镜像
docker-build:
  stage: docker-build
  only:
    - develop
    - sit
    - master
  except:
    - gd_ui_improve
  script:
    - docker_build
    - chart_build


.auto_devops: &auto_devops |
  curl -o .auto_devops.sh \
      "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"
  if [ $? -ne 0 ];then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh
  function docker_build(){
    docker build --pull -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} -f docker/Dockerfile .
    docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  }

before_script:
  - *auto_devops
