<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切上卷" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.productSpecDesc : '' }}</view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="170">
          <u--input v-model="model.poleRollName" border="none" placeholder="请扫描极卷条码" @focus="focusEvent('poleRollName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('poleRollName')"></view>
        </u-form-item>

        <u-form-item label="上卷数量(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.quantity }} </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">已上极卷</view>

        <u-form-item label="极卷条码" borderBottom required labelWidth="130">
          <view class="w100x flex right"> {{ model.poleRoll.consumableName }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.poleRoll.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(片)" borderBottom required labelWidth="130">
          <view class="w100x flex right"> {{ model.poleRoll.quantity }} </view>
        </u-form-item>

        <u-form-item label="上卷作业员" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.poleRoll.eventUser }} </view>
        </u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.poleRoll.eventTime }} </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">下料弹夹</view>

        <u-form-item label="弹夹" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.dieCuttingTankInfoDto.tankName }} </view>
        </u-form-item>

        <u-form-item label="批次ID" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.dieCuttingTankInfoDto.lotName }} </view>
        </u-form-item>

        <u-form-item label="弹夹容量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.dieCuttingTankInfoDto.tankCapacity }} </view>
        </u-form-item>

        <u-form-item label="数量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.dieCuttingTankInfoDto.lotQuantity }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.dieCuttingTankInfoDto.productOrderName }} </view>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">上卷</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        poleRollName: '极卷号不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.poleRollName': {
      handler(res) {
        this.changeDurableName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/Polar/modules/Upgradeable`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        description: null, //	设备描述	string	
        dieCuttingTankInfoDto: {
          lotName: null,//	批次号	string	
          lotQuantity: null,//	批次数量	number	
          productOrderName: null,//	工单名	string	
          tankCapacity: null,//	弹夹容量	integer(int32)	
          tankName: null,//弹夹名
        }, //	下料弹夹	下料弹夹	下料弹夹
        factoryName: null, //	工厂	string	
        machineName: null, //设备名	string	
        poleRoll: {
          productOrderName: null, // 工单号
          quantity: null, // 上卷数量
          eventUser: null, // 上卷作业员
          eventTime: null, // 上卷时间
        }, //已上极卷	FeedingMaterial	FeedingMaterial
        processOperationName: null, //	工序	string	
        productSpecDesc: null, //	产品编码描述	string	
        productSpecName: null, //
        poleRollName: '' // 极卷条码
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.DieCutting.DieCuttingUpRollingMsgProcessor(params).then(res => {
        this.$Toast('模切上卷成功')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.DieCutting.getStartDataByMachineName(params)
        if (res.datas.length > 0) {
          let { description, processOperationName, productSpecDesc, productSpecName, dieCuttingTankInfoDto, poleRoll, factoryName } = res.datas[0]
          this.model = {
            description, //	设备描述	string	
            dieCuttingTankInfoDto, //	下料弹夹	下料弹夹	下料弹夹
            factoryName, //	工厂	string	
            machineName: value, //设备名	string	
            poleRoll, //已上极卷	FeedingMaterial	FeedingMaterial
            processOperationName, //	工序	string	
            productSpecDesc, //	产品编码描述	string	
            productSpecName, //
            poleRollName: '', // 极卷条码
            quantity: ''
          }
          if (!this.model.dieCuttingTankInfoDto) {
            this.model.dieCuttingTankInfoDto = {
              lotName: null,//	批次号	string	
              lotQuantity: null,//	批次数量	number	
              productOrderName: null,//	工单名	string	
              tankCapacity: null,//	弹夹容量	integer(int32)	
              tankName: null,//弹夹名
            }
          }
          if (!this.model.poleRoll) {
            this.model.poleRoll = {
              productOrderName: null, // 工单号
              quantity: null, // 上卷数量
              eventUser: null, // 上卷作业员
              eventTime: null, // 上卷时间
              consumableName: '', // 极卷条码
            }
          }
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 极卷编码 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        consumableName: value,
      }
      try {
        let res = await this.$service.DieCutting.getConsumableData(params)
        if (res.datas && res.datas.length > 0) {
          let { quantity } = res.datas[0]
          this.model.quantity = quantity
        } else {
          this.$Toast('暂无信息！')
        }
      } catch (error) {
        this.model.durableName = null
        this.model.quantity = quantity
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA02'
          break;
        case 'poleRollName':
          this.model.poleRollName = 'GF00019'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'poleRollName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
