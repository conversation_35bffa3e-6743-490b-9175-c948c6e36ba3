/*---------添加以下2句，引入https和fs---------*/
// const https = require('https')
// const path = require('path')
// const fs = require('fs')
/*---------添加以上2句，引入https和fs---------*/
// const process = require('process')



// let devServerObj = {
//   gc: {
//     proxy: {
//       '/gcapi': {
//         target: "http://10.74.162.19:30416/",
//         changeOrigin: true,//解决跨域
//         pathRewrite: {
//           '^/gcapi': ''
//         },
//       },
//     }
//   },
//   yn: {
//     proxy: {
//       '/gcapi': {
//         target: "http://10.113.160.231:7890/",
//         changeOrigin: true,
//         pathRewrite: {
//           "^/gcapi": ""
//         }
//       },
//       "/ynapi": {
//         target: "http://10.113.160.231:10110/",
//         changeOrigin: true,
//         pathRewrite: {
//           "^/ynapi": ""
//         }
//       }
//     },
//   }
// }

// let devServer = devServerObj.hbuilder
// let myArg = process.argv.slice(-1)[0]
// console.log('myArg', myArg);
// //  开发环境配置
// if (process.env.NODE_ENV === 'development') {
//   switch (myArg) {
//     case 'dev:test':
//       devServer = devServerObj.test 
//       break;
//     default:
//       devServer = devServerObj[myArg]
//   }
//   console.warn(`
//     ===================== 配置文件开始 =============================
//     使用配置 -> hbuilder: ${JSON.stringify(devServer)}
//     ===================== 配置文件结束 ==========================
//     `)
// }

module.exports = {
  // transpileDependencies: ['uview-ui'],
  // publicPath: '/',
  // devServer: {
  //   host: 'localhost',
  //   port: devServer.port || 9004,
  //   proxy: devServer.proxy,
  // },
}
