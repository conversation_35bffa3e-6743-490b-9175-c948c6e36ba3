<template>
  <view class="listPage pl5 pr5 pb10">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="listContainer ma10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list1" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>箱号</view>
            <view>{{ ele.boxCode }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>坐标</view>
            <view> {{ getCoordinate(index) }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view> 客户码</view>
            <view> {{ ele.custCode }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>PACK码</view>
            <view>{{ ele.packCode }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>工单编码</view>
            <view>{{ ele.productOrderName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>绑定时间</view>
            <view>{{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>操作人</view>
            <view>{{ ele.createUser }}</view>
          </view>
        </view>
        <NoData v-if="!list1 || list1.length === 0"></NoData>

      </scroll-view>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import useNls from "@/mixins/useNls";
import moment from 'moment';
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      status: 'nomore',
      model: {},
      list1: [],
      orginList: [],
      paramsOption: {
      },
    }
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题

    this.params_carrierName = options.carrierName
    this.layerCount = options.layerCount
    this.Quantity = options.Quantity
    this.nlsMap = nlsMap

    this.getDetail()
  },
  methods: {
    moment,
    leftClick() {
      this.$utils.backAndUpdata()
    },
    getCoordinate(index) {
      if (!this.layerCount || this.layerCount === 0) return '-';
      const divisor = this.Quantity - index;
      const quotient = Math.floor(divisor / this.layerCount); // 商
      const remainder = divisor % this.layerCount;            // 余数

      let layer, count;
      if (remainder === 0) {
        // 整除
        layer = quotient;
        count = this.layerCount;
      } else {
        // 不整除
        layer = quotient + 1;
        count = remainder;
      }

      return `第${layer}层${count}个`;
    },
    async getDetail() {
      let parmas = {
        boxCode: this.params_carrierName
      }

      let res = await this.$service.ThreeCodeToOne.getPackingInfoList(parmas)

      if (res.success && res.datas.length > 0) {
        this.list1 = res.datas
      }



    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .table_header {
      flex-shrink: 0;
    }

    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>
