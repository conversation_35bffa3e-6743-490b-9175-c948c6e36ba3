<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="工装安装" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="120">
        <u-form-item label="设备号" borderBottom required labelWidth="120">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="120">
          <u--input readonly v-model="model.description" border="none"></u--input>
        </u-form-item>

        <u-form-item label="设备位置" borderBottom labelWidth="120">
          <view class="w100x flex right" @click="selectPortName('portName')">
            <view>{{ model.portName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="工装号" required borderBottom labelWidth="120">
          <u--input v-model="model.durableName" border="none" :focus="focusObj.durableName" placeholder="扫描工装" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <u-form-item label="工装描述" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.durableNameDesc }}
          </view>
        </u-form-item>

        <u-form-item label="额定使用寿命" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.usedCountTotal }}
          </view>
        </u-form-item>

        <u-form-item label="警戒使用寿命" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.warningUsedCount }}
          </view>
        </u-form-item>

        <u-form-item label="已使用寿命" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.usedCount }}
          </view>
        </u-form-item>

        <u-form-item label="剩余使用寿命" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.residueUsedCount }}
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="text" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>
    <view class="btnContainer2">
      <view @click="submit">安装</view>
      <view @click="gotoQuery">查看已安装明细</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        durableName: '标签条码不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        durableName: false,
        materialPosition: false
      },
      show: false,
      content: '',
      modelKey: '',
      durableDataList: [],
      productOrderNameList: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productOrderName) {
        obj = this.columns.find(item => item.productOrderName === this.model.productOrderName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.durableName': {
      handler(res) {
        this.changeDurableName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/ToolingAndInspection/ToolingInstall/modules/ToolingInstallDetails?machineName=${this.model.machineName}`,
      })
    },
    focusEvent(type) {
      this.model[type] = ''
    },
    initModel() {
      this.model = {
        areaName: null, //	区域
        durableName: null, //	工装编码	
        durableNameDesc: null, // 工装描述	
        eventUser: null, //
        factoryName: null, //材料描述	string	
        machineName: null, //	材料编码	string
        description: null, //	设备描述	string
        portName: null, // 设备位置
        residueUsedCount: null, // 剩余使用寿命
        usedCount: null, // 实际用的数量
        warningUsedCount: null, // 	警戒使用寿命	
        usedCountTotal: null, //	总的使用数量(额定使用寿命
      }
    },
    // 选择
    selectFirm(e) {
      this.model.portName = e.value[0].portName
      this.select = false
    },
    // 安装
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.ToolingAndInspection.install(params).then(res => {
        this.$Toast('安装成功！')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.ToolingAndInspection.getMachineData(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, description, processOperationName } = res.datas[0]
          this.model.description = description
          this.model.machineName = machineName
          this.model.processOperationName = processOperationName
          // 获取设备位置
          let data = {
            machineName
          }
          let resData = await this.$service.ToolingAndInspection.getMachinePort(data)
          this.durableDataList = resData.datas
          if (this.durableDataList.length > 0) {
            this.durableDataList.forEach(item => {
              item.text = item.portName + '/' + item.description
            })
            console.log(this.durableDataList, '================');
          }
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 工装条码 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        durableName: value
      }
      this.$service.ToolingAndInspection.getDurableData(params).then(res => {
        if (res.datas.length > 0) {
          let { usedCountTotal, durableNameDesc, warningUsedCount, usedCount, residueUsedCount } = res.datas[0]
          this.model.usedCountTotal = usedCountTotal
          this.model.durableNameDesc = durableNameDesc
          this.model.warningUsedCount = warningUsedCount
          this.model.usedCount = usedCount
          this.model.residueUsedCount = residueUsedCount
          this.$service.ToolingAndInspection.checkUsedCount(this.model).then(check => {
            if (!check.datas[0]) {
              this.show = true
              this.content = '工装已经达到预警（警戒）使用寿命，请确认是否继续使用！'
            }
          })
        }

      }).catch(res => {
        this.model.durableName = ''
      })
    },

    // 选择安装位置
    selectPortName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.durableDataList
    },

    // 选择工单编号
    selectProductOrderName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.productOrderNameList
    },

    async confirm() {
      this.show = false
      let params = {
        ...this.model
      }
      this.$service.ToolingAndInspection.install(params).then(res => {
        this.$Toast('安装成功！')
        this.initModel()
      })
    },

    cancel() {
      this.show = false
      this.model.durableName = ''
      this.model.consumableSpec = ''
      this.model.quantity = ''
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.DCUT03.01'
          break;
        case 'durableName':
          this.model.durableName = 'DJ00001'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'durableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import "../../../styles/uform.scss";
@import "../../../styles/publicStyle.scss";
</style>
