<template>
  <view class="listPage bc_f3f3f7 pl5 pr5 pb10">
    <view class="listContainer">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view class="mb10 br10 bc_fff pa10 fs12" v-for="(ele, index) in model" :key="index">
          <view class="flex flex_wrap">
            <view class="flex w100x lin24 c_999">
              <view class="w60">条码号:</view>
              <view class="c_00b17b"> {{ ele.lotName }} </view>
            </view>
            <view class="flex w50x lin24 c_999">
              <view class="w60">设备编码:</view>
              <view> {{ ele.machineName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">设备名称:</view>
              <view> {{ ele.machineText }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">工序编码:</view>
              <view> {{ ele.processOperationName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">工序名称:</view>
              <view> {{ ele.processOperationText }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">载具编码:</view>
              <view> {{ ele.carrierName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">槽位位置:</view>
              <view> {{ ele.slot }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">电芯档位:</view>
              <view> {{ ele.cellGear }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">条码状态:</view>
			  <view> {{ ele.lotStateText }} </view>
<!--              <view v-if=" ele.lotState == 'Created'"> 创建 </view>
              <view v-else-if=" ele.lotState == 'Released'"> 过站中 </view>
              <view v-else-if=" ele.lotState == 'Completed'"> 已完工 </view>
              <view v-else-if=" ele.lotState == 'Terminated'"> 中断 </view>
              <view v-else-if=" ele.lotState == 'Emptied'"> Emptied </view>
              <view v-else-if=" ele.lotState == 'Scrapped'"> 报废 </view>
              <view v-else-if=" ele.lotState == 'Shipped'"> 搬运中 </view>
              <view v-else> {{ ele.lotState }} </view> -->
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">品质状态:</view>
			  <view> {{ ele.lotGradeText }} </view>
		<!-- 	  <view v-if=" ele.lotGrade == 'G'"> 良品 </view>
			  <view v-else-if=" ele.lotGrade == 'N'"> 不良品 </view>
			  <view v-else-if=" ele.lotGrade == 'S'"> 报废 </view>
			  <view v-else> {{ ele.lotGrade }} </view> -->
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">操作名称:</view>
			  <view> {{ ele.eventNameText }} </view>
<!-- 			  <view v-if=" ele.eventName == 'AddDisposalOrder'"> 不合格处置单-创建 </view>
			  <view v-else-if=" ele.eventName == 'Submit'"> 不合格处置单-提交 </view>
			  <view v-else-if=" ele.eventName == 'assignCarrier'"> 绑定载具 </view>
			  <view v-else-if=" ele.eventName == 'CellBindContainer'"> PDA-电芯绑盘 </view>
			  <view v-else-if=" ele.eventName == 'CellUnBindContainer'"> PDA-电芯拆盘 </view>
			  <view v-else-if=" ele.eventName == 'ContainerUnBindCell'"> PDA-整托拆盘 </view>
			  <view v-else-if=" ele.eventName == 'IotCellBindContainer'"> 设备集成-电芯绑盘 </view>
			  <view v-else-if=" ele.eventName == 'IotCellUnBindContainer'"> 设备集成-电芯拆盘 </view>
			  <view v-else-if=" ele.eventName == 'IotContainerUnBindCell'"> 设备集成-电芯拆盘 </view>
			  <view v-else-if=" ele.eventName == 'bindCell'"> 蓝胶码堆成电芯 </view>
			  <view v-else-if=" ele.eventName == 'bindMod'"> 电芯堆成模组 </view>
			  <view v-else-if=" ele.eventName == 'bindPack'"> 模组堆成Pack </view>
			  <view v-else-if=" ele.eventName == 'bindCustomer'"> Pack堆成客户码 </view>
			  <view v-else-if=" ele.eventName == 'CreateLot'"> 创建条码 </view>
			  <view v-else-if=" ele.eventName == 'ChangeLotName'"> 条码变更 </view>
			  <view v-else-if=" ele.eventName == 'TrackIn'"> 进站 </view>
			  <view v-else-if=" ele.eventName == 'TrackOut'"> 出站 </view>
			  <view v-else-if=" ele.eventName == 'WaitTrackOut'"> 待出站 </view>
			  <view v-else-if=" ele.eventName == 'LotProcessOperationChange'"> 工序调整 </view>
			  <view v-else-if=" ele.eventName == 'FirstInspection'"> 首检 </view>
			  <view v-else-if=" ele.eventName == 'Marking'"> Marking </view>
			  <view v-else> {{ ele.eventName }} </view> -->
            </view>

			<view class="flex w50x lin24 c_999">
              <view class="w60">操作时间:</view>
              <view class="fs10 nowrap"> {{ ele.eventTime }} </view>
            </view>

<!--            <view class="flex w50x lin24 c_999">
              <view class="w60">进站时间:</view>
              <view class="fs10 nowrap"> {{ ele.loggedInTime }} </view>
            </view>
            <view class="flex w50x lin24 c_999">
              <view class="w60">出站时间:</view>
              <view class="fs10 nowrap"> {{ ele.loggedOutTime }} </view>
            </view> -->
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>

      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'RetrospectiveLot',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },

  props: {
    paramsOption: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      model: [],
      // model: Array.from({ length: 30 }, (v, i) => i),
    };
  },
  watch: {

  },
  created() {
    console.log('12312312', this.paramsOption);
    this.initSearchModel()
    this.getData()
  },

  methods: {
    initSearchModel() {
      this.searchModel = {
        pageNo: this.pageNumber,
        limit: 10,
      }
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      params.lotName = this.paramsOption.batchId
      // setTimeout(() => {
      //   // let res = Array.from({ length: 20 }, (v, i) => i)
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return
      await this.$service.PDARetrospectiveReport.retrospectLotHist(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.datas[0].records : [...this.model, ...res.datas[0].records]
          if (this.searchModel.pageNo == res.datas[0].pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;
  // .topContainer {
  //   flex-shrink: 0;
  // }
  .listContainer {
    flex: 1;
  }
}
</style>