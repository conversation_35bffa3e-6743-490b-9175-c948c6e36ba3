<template>
  <view class="bc_fff myContainerPage">
    <u-navbar title="调机记录" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <!-- <view class="myContainer ma5"> -->
      <view class="myContainer ml10 mr10 mb10">
        <u--form class="ml10 mr10" labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="设备号:" required labelWidth="100">
            <u--input readonly v-model="form.sbh" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('sbh')"></u--input>
            <!-- <view class="iconfont icon-saoma" @click="scan('sbh')"></view> -->
          </u-form-item>
          <u-form-item label="设备描述:" labelWidth="100">
            <view class="w100x flex">
              {{ form.sbms }}
            </view>
          </u-form-item>
          <!-- <u-form-item label="工序:" required labelWidth="130">
            <view class="w100x flex right" @click="selectReasonCodeType('gx')">
              <view>{{ form.gx }}</view>
              <u-icon name="arrow-down" color="black" size="18"></u-icon>
            </view>
          </u-form-item> -->
        </u--form>
        <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c flex hcenter mt5">
          <view class="mr5">调机记录</view>
        </view>
        <view v-for="(ele, index) in records"  :key="index">
          <view class="bb_e1e1e1 h30 lin30 fb pl10 mb4c flex hcenter mt5">
            <view class="mr5">提交时间：{{ele.debugTime}}</view>
          </view>
          <view class=" h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
            <view class="mr5">人工-录入:</view>
          </view>
          <template v-if="ele.inputList.length > 0">
            <view v-for="(ele2, index2) in ele.inputList" :key="index2">
              <view class="flex between ma10 mb10 bb_eee hcenter">
                <view class="flex1 mr10 pb10">
                  <view class="flex h40 hcenter c_999">
                    <view class="mr10 w200 c_000">{{ ele2.paramName }}:</view>
                    <view class="flex right">
                      <view class="flex hcenter">
                        <u--input readonly v-model="ele2.debugResult" border="none"></u--input>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <NoData v-else></NoData>
          <view class=" h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
            <view class="mr5">人工-勾选:</view>
          </view>
          <template v-if="ele.selectList.length > 0">
            <view v-for="(ele3, index3) in ele.selectList" :key="index3">
              <view class="flex between ma10 mb10 bb_eee hcenter">
                <view class="flex1 mr10 pb10">
                  <view class="flex h40 hcenter c_999">
                    <view class="mr10 w200 c_000">{{ ele3.paramName }}:</view>
                    <view class="flex right">
                      <view class="flex hcenter">
                        <view>通过</view>
                        <!-- <u-checkbox-group>
                          <u-checkbox
                            @change="(e) => checkboxChange(e, index)"
                            v-model="ele.value" 
                            v-for="(item, index2) in radiolist1" :key="index2" 
                            :name="item.name"
                          >{{item.name}}</u-checkbox>
                        </u-checkbox-group> -->
                        <!-- <u--input placeholder="请输入" v-model="ele.value" border="none"></u--input> -->
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <NoData v-else></NoData>
        </view>
        <!-- <scroll-view scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        </scroll-view> -->
        <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <!-- <view class="btnContainer" @click="submit">创建任务</view> -->
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.sbh': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    // 'form.gx': {
    //   handler(val) {
    //     this.getInspectionTaskList(val)
    //   }
    // },
  },
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      form: {
        sbh: '',
        sbms: ''
      },
      simpleTrackProduct: [
        {
          paramName: 'E001环境参数-温度',
          paramsType: 'input'
        },
        {
          paramName: 'E001环境参数-温度2',
          paramsType: 'input'
        },
        {
          paramName: 'E001环境参数-温度3',
          paramsType: 'select'
        },
        {
          paramName: 'E001环境参数-温度4',
          paramsType: 'select'
        }
      ],
      columns: [
        []
      ],
      select: false,
      GetReasonCodeTypeList: [],
      radiolist1: [
        {
          name: '通过'
        }
      ],
      records: [
        // {
        //   subTime: '2023-03-24 10：00'
        // }
      ]
    };
  },
  computed: {
    inputList() {
      let arr = this.simpleTrackProduct.filter((item) => item.paramsType == 'input')
      return arr
    },
    selectList() {
      let arr = this.simpleTrackProduct.filter((item) => item.paramsType == 'select')
      return arr
    },
  },
  onLoad(options) {
    let { machineName, machineNameDesc } = options
    this.form.sbh = machineName
    this.form.sbms = machineNameDesc
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        inspectType: 'ProcessInspection',
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return item.reasonCodeType + '/' + item.typeDesc
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if(type == 'gx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if(this.selectType == 'gx') {
        this.form.gx = e.value[0].label
        this.form.gxType = e.value[0].value
      }
      this.select = false
    },
    initModel() {
      this.form = {
        sbh: '',
        sbms: '',
        gx: '',
        gxType: '',
      }
    },
    focusEvent(type) {
      // this.form[type] = ''
    },
    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        // inspectType: 'ProcessInspection',
        machineName: value
      }
      try {
        let res = await this.$service.Tuning.getHist(params)
        console.log('getHist', res);
        if (res.datas.length > 0) {
          let arr = []
          let data =  res.datas
          data.forEach(item => {
            if(arr.indexOf(item.debugTime) == -1) {
              arr.push(item.debugTime)
            }
          })
          let list = []
          arr.forEach(item => {
            list.push({
              debugTime: item,
              list: []
            })
          })
          data.forEach(item => {
            list.forEach((item2, index2) => {
              if(item.debugTime == item2.debugTime) {
                list[index2].list = [
                  ...list[index2].list,
                  {
                    ...item
                  }
                ]
              }
            })
          })
          console.log('arr', arr, list);
          this.records = list
          this.records.forEach(item => {
            let arr = []
            let arr2 = []
            item.list.forEach(item2 => {
              if(item2.debugType == 'input')arr.push(item2)
              if(item2.debugType == 'select')arr2.push(item2)
            })
            item.inputList = arr
            item.selectList = arr2
          })
          // this.form = {
          //   ...res.datas[0],
          //   sbh: this.form.sbh,
          //   sbms: data.description,
          //   gx: ''
          // }
          // this.GetReasonCodeTypeList = data.machineGroupName.split(',').map(item => {
          //   return {
          //     label: item,
          //     value: item
          //   }
          // })
        } else {
          this.form.sbh = ''
          this.form.sbms = ''
          this.records = []
          this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        // this.initModel()
        this.form.sbh = ''
        this.form.sbms = ''
        this.records = []
      }
    },
    /* 任务列表 */
    async getInspectionTaskList(value) {
      if (!value) return
      let params = {
        inspectType: 'ProcessInspection',
        machineName: this.form.machineName,
        processOperationName: this.form.gx
      }
      try {
        let res = await this.$service.QualityControl.getInspectionTaskList(params)
        console.log('getInspectionTaskList', res);
        if (res.datas.length > 0) {
          // let data =  res.datas[0]
          this.simpleTrackProduct = res.datas
        } else {
          this.simpleTrackProduct = []
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        // this.initModel()
      }
    },
    checkboxChange(e, index) {
      console.log('checkboxChange', e);
      this.selectList[index].value = e
    },
    submit(item) {
      console.log('submit', this.inputList, this.selectList);
      return
      // 创建巡检任务
      let params = {
        inspectType: 'ProcessInspection',
        machineName: this.form.machineName,
        processOperationName: this.form.gx
      }
      this.$service.QualityControl.startInspectionTask(params).then((res) => {
        console.log('startInspectionTask', res);
        if (res.datas.length > 0) {
          this.$Toast('创建巡检任务成功！')
          setTimeout(() => {
            // 刷新任务列表
            this.getInspectionTaskList(this.form.gx)
          }, 500)
        } else {
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      })
    },
    delAction(item) {
      if(item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return
      }
      uni.showModal({
          title: '提示',
          content: `是否确认删除巡检任务？`,
          cancelText: '取消',
          confirmText: '确认',
          cancelColor: '#666',
          confirmColor: '#409eff',
          success: (res) => {
            if (res.confirm) {
              // 删除巡检任务
              let params = {
                inspectType: 'ProcessInspection',
                taskNo: item.taskNo
              }
              this.$service.QualityControl.inspectionTaskDelete(params).then((res) => {
                console.log('inspectionTaskDelete', res);
                this.$Toast('删除任务成功！')
                // 刷新任务列表
                this.getInspectionTaskList(this.form.gx)
              })
            }
            if (res.cancel) {}
          },
        })
      
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.form, key, res.result)
        },
      })
    },
    canJunmpAction(type, item) {
      if(item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return true
      } else {
        return false
      }
    }
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
.btn {
  margin: 0 auto;
  height: 34px;
  line-height: 34px;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 13px;
  text-align: center;
  border-radius: 11px;
}
.cantBtn {
  background-color: #eee;
}
</style>