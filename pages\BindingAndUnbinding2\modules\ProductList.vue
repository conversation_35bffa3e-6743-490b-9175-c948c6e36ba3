<template>
  <view class="myContainerPage">
    <u-navbar title="载具已装在制品" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="载具" borderBottom required labelWidth="100">
          <u--input v-model="model.durableName" border="none" readonly></u--input>
        </u-form-item>

        <u-form-item label="载具类型" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.durableType }} </view>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + ' / ' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="后序工序" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.processOperationName ? model.processOperationName + ' / ' + model.processOperationDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="已装数量" labelWidth="100">
          <view class="w100x flex right"> {{ model.lotQuantity }} </view>
        </u-form-item>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">已装在制品信息</view>
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">槽位号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1 pa2">在制品条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">工单</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">绑定时间</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">操作</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in list" :key="index" class="flex bl_e1e1e1" style="min-height: 60rpx">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.slot }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 pa2 txt_c">{{ item.lotName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.productOrderName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ moment(item.eventTime).format('YYYY-MM-DD') }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2"><view class="btn" @click="submit(item)">移除</view></view>
            </view>
            <view class="pt100" v-if="!list || list.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
      </view>
    </view>
    <view class="btnContainer" @click="allClearAction">一键解绑</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'

import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
import { set } from 'vue';
export default {
  mixins: [ScrollMixin],
  data() {
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {},
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      list: []
      // list: Array.from({ length: 20 }, (v, i) => i),
    }
  },
  watch: {
    'model.durableName': {
      handler(val) {
        this.changeDurableName(val)
        this.GetProductListByDurableName(val)
      }
    }
  },
  onLoad(e) {
    let option = e && JSON.parse(e.params)
    this.initModel()
    this.model.productSpecDesc = option && option.productSpecDesc
    this.model.productSpecName = option && option.productSpecName
    this.model.durableName = option && option.durableName
    this.model.processOperationName = option && option.processOperationName
    this.model.processOperationDesc = option && option.processOperationDesc
    this.model.lotQuantity = option && option.lotQuantity
  },
  methods: {
    moment,
    leftClick() {
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('acceptDataFromOpenedPage', '');
      uni.navigateBack();
    },
    allClearAction() {
      uni.showModal({
        title: '提示',
        content: `是否一键解绑?`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // let lotIDList = this.list.map(item => {
            //   return {
            //     lotID: item.lotName
            //   }
            // })
            // let params = {
            //   factoryName: 'GFAJ07',
            //   lotIDList: lotIDList,
            //   stack: this.model.durableName
            // }
            let params = {}
            let formattedTimestamp = moment().format('YYYYMMDDHHmmssSSS');
            let randomNum = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10)).join('')
            let transId = `${formattedTimestamp}.${randomNum}`
            let eventTime = moment().format('YYYY-MM-DD HH:mm:ss')
            params = {
              container: this.model.durableName,
              containerType: "1",
              eventTime: eventTime,
              eventType: "E",
              eventUser: this.$getLocal(USER_ID),
              factoryNo: "10011",
              flag: '3',
              lineName: "",
              serialNoList: [],
              transId: transId,
            }
            this.$service.carrierIsBind.ContainerBindOrUnBind(params, { handleSucess: true }).then(res => {
              if (res.resultCode == 0) {
                this.$Toast('一键解绑成功！')
                this.list = []
                this.model = {}
                setTimeout(() => {
                  const eventChannel = this.getOpenerEventChannel();
                  eventChannel.emit('acceptDataFromOpenedPage', '');
                  uni.navigateBack();
                }, 2000);
              } else {
                this.$Toast(res.resultMsg)
              }
            })
          }
          if (res.cancel) { }
        },
      })


    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        durableName: null, //	载具	string	
        durableType: null, //	载具类型	string	
        productSpecDesc: null, //产品描述	string	
        productSpecName: null, //产品编码	string	
        capacity: null, //	满载数量	number	
        lotName: null, // 在制品条码	string(date-time)	
        lotQuantity: null, // 已绑数量	number	
        processOperationName: null, //	工序	string	
        processOperationDesc: null, //	工序描述	string	
        lotGrade: null // 载具状态
      }
    },

    /* 载具 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        durableName: value
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByDurableName(params)
        if (res.datas.length > 0) {
          this.model = res.datas[0]
        }
      } catch (error) {
      }
    },
    async GetProductListByDurableName(value) {
      if (!value) return
      let params = {
        durableName: value
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductListByDurableName(params)
        if (res.datas.length > 0) {
          this.list = res.datas
        } else {
          this.list = []
        }
      } catch (error) {
      }
    },
    submit(item) {
      // let params = {
      //   factoryName: 'GFAJ07',
      //   lotIDList: [{ lotID: item.lotName }],
      //   stack: this.model.durableName
      // }
      let params = {}
      let formattedTimestamp = moment().format('YYYYMMDDHHmmssSSS');
      let randomNum = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10)).join('')
      let transId = `${formattedTimestamp}.${randomNum}`
      let eventTime = moment().format('YYYY-MM-DD HH:mm:ss')
      params = {
        container: this.model.durableName,
        containerType: "1",
        eventTime: eventTime,
        eventType: "E",
        eventUser: this.$getLocal(USER_ID),
        factoryNo: "10011",
        flag: '2',
        lineName: "",
        serialNoList: [
          {
            "serialno": item.lotName,
            "slot": item.slot
          }
        ],
        transId: transId,
      }
      this.$service.carrierIsBind.ContainerBindOrUnBind(params,{ handleSucess: true }).then(res => {
        if (res.resultCode == 0) {
          this.$Toast('解绑成功！')
          setTimeout(() => {
            this.changeDurableName(this.model.durableName)
            this.GetProductListByDurableName(this.model.durableName)
          }, 800);
        }
        else {
          this.$Toast(res.resultMsg)
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
.bottomBtn {
  position: fixed;
  bottom: -28rpx;
  left: 50%;
  transform: translate(-50%, -50%);
}
.table_header {
  flex-shrink: 0;
}
.table_content {
  flex: 1;
  overflow-y: scroll;
}
.btn {
  width: 120rpx;
  height: 50rpx;
  border-radius: 10%;
  background-color: #0285be;
  border: 2rpx solid #b1c2db;
  text-align: center;
  line-height: 50rpx;
  font-size: 24rpx;
  color: #fff;
}
</style>
