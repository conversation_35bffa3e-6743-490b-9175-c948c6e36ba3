<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>

    <view class="topContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="检验类型" borderBottom labelWidth="100">
          <!-- {{ InspectionTypes }}/ -->
          <view class="w100x flex right"> {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }} </view>
        </u-form-item>
        <u-form-item label="任务编码" labelWidth="100">
          <view class="w100x flex right">{{ taskNo }} </view>
        </u-form-item>
      </u--form>
    </view>
    <view class="ml15" style="color: #409eff"> 已取样明细</view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
          <view class="flex between h30 hcenter c_999">
            <view>样品条码</view>
            <view>{{ ele.lotName || '-' }}</view>
          </view>
          <view class="flex between h30 hcenter c_999">
            <view>取样时间</view>
            <view>{{ ele.createTime }}</view>
          </view>
          <view class="flex between h30 hcenter c_999">
            <view> 检验进度</view>
            <view>{{ $utils.filterObjLabel(dicts.inspectTaskLotStateList, ele.lotState) }}</view>
          </view>
          <view class="flex between h30 hcenter c_999">
            <view> 检验结果</view>
            <view>{{ ele.inspectResult }}</view>
          </view>
          <view class="flex between h30 hcenter c_999">
            <view> 检验完成时间</view>
            <view>{{ ele.inspectionFinishime }}</view>
          </view>
          <view class="mt10">
            <u-button type="error" text="删除" @click="deleteItem(ele, index)"></u-button>
          </view>
        </view>
        <NoData v-if="!list || list.length === 0"></NoData>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      dicts: {
        InspectionTypesList: [],
        inspectTaskLotStateList: [] // 状态
      },
      model: {},
      // list: [],
      // list: Array.from({ length: 10 }, (ele, i) => i),
      InspectionTypes: '',
      taskNo: '',
    };
  },
  computed: {
    list: {
      get() {
        return this.$store.state.InspectionSampleList;
      },
      set(value) {
        this.$store.commit('setInspectionSampleList', value);
      }
    },
  },
  onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.InspectionDetialTitle // 标题
    this.nlsMap = nlsMap
    this.taskNo = options.taskNo
    this.InspectionTypes = options.InspectionTypes
    this.getEnumValue('InspectionTypes', 'InspectionTypesList') // 单据类型
    this.getEnumValue('InspectTaskLotState', 'inspectTaskLotStateList') // 任务状态
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('getFinshSampleList')
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },

  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>