<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="出库查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="单据类型" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('deliveryType')">
            <view v-if="model.deliveryType">{{ $utils.filterObjLabel(dicts.deliveryTypeList, model.deliveryType) }}</view>
            <view class="c_c0c4cc" v-else>请选择单据类型</view>
            <view class="ml5" :style="{ transform: select && selectType === 'deliveryType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="来源方式" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('deliveryMode')">
            <view v-if="model.deliveryMode">{{ $utils.filterObjLabel(dicts.deliveryModeList, model.deliveryMode) }}</view>
            <view class="c_c0c4cc" v-else>请选择来源方式</view>
            <view class="ml5" :style="{ transform: select && selectType === 'deliveryMode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="出库单号" borderBottom labelWidth="100">
          <u--input v-model="model.deliveryOrderNo" border="none" focus placeholder="请扫描或输入出库单号"></u--input>
          <view class="iconfont icon-saoma" @click="scan('deliveryOrderNo')"></view>
        </u-form-item>

        <u-form-item label="	工单编码" borderBottom labelWidth="100">
          <u--input v-model="model.productOrderName" border="none" placeholder="请扫描或输入工单编码"></u--input>
          <view class="iconfont icon-saoma" @click="scan('productOrderName')"></view>
        </u-form-item>

        <u-form-item label="仓库编码" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockCode')">
            <view v-if="model.stockCode">{{ $utils.filterObjLabel(dicts.stockCodeList, model.stockCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择仓库编码</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <!-- <u-form-item label="状态" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('state')">
            <view v-if="model.state">{{ $utils.filterObjLabel(dicts.stateList, model.state) }}</view>
            <view class="c_c0c4cc" v-else>请选择状态</view>
            <view class="ml5" :style="{ transform: select && selectType === 'state' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item> -->

        <u-form-item label="开始日期" labelWidth="150" @click="startTimeShow = true" borderBottom>
          <u-input v-model="model.startTime" disabled disabledColor="#fff" placeholder="请选择开始日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: startTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>

        <u-form-item label="结束日期" labelWidth="150" @click="endTimeShow = true">
          <u-input v-model="model.endTime" disabled disabledColor="#fff" placeholder="请选择结束日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: endTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>
      </u--form>

      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="startTimeShow = false"
        :show="startTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('startTime', $event)"
        @cancel="startTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>
      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="endTimeShow = false"
        :show="endTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('endTime', $event)"
        @cancel="endTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">查询</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      nowDate: Number(new Date()),
      selfRegisterTime: Number(new Date()),
      startTimeShow: false,
      endTimeShow: false,

      columns: [],
      select: false,
      selectType: '',
      model: {},
      dicts: {
        deliveryTypeList: [], // 单据类型
        deliveryModeList: [], // 来源方式
        stockCodeList: [], // 	仓库
      }
    }
  },
  onLoad() {
    this.getEnumValue('DeliveryType', 'deliveryTypeList') // 单据类型
    this.getEnumValue('DeliveryMode', 'deliveryModeList') // 来源方式
    this.getEnumValue('DeliveryOrderStatus', 'stateList')  // 状态
    this.getStockCodeList() // 仓库编码
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        deliveryType: null, // 单据类型
        deliveryMode: null, // 来源方式
        deliveryOrderNo: null,  // 出库单号
        productOrderName: null, // 工单编码
        state: 'WaitCheck',  // 状态
        stockCode: null,// 仓库
        startTime: null, // 开始时间
        endTime: null, // 结束时间
      }
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'deliveryType':
          this.columns = this.dicts.deliveryTypeList
          break;
        case 'deliveryMode':
          this.columns = this.dicts.deliveryModeList
          break;
        case 'state':
          this.columns = this.dicts.stateList
          break;
        case 'stockCode':
          this.columns = this.dicts.stockCodeList
          break;
        default:
          break;
      }
    },

    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },

    pickConfirm(type, e) {
      switch (type) {
        case 'startTime':
          this.model.startTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.startTimeShow = false
          break;
        case 'endTime':
          this.model.endTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.endTimeShow = false
          break;
        default:
          break;
      }
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },

    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    getStockCodeList() {
      this.$service.common.getDictByQueryId('GetStockCodeList').then(res => {
        this.dicts.stockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
      })
    },

    submit() {
      let startTime = new Date(this.model.startTime).getTime()
      let endTime = new Date(this.model.endTime).getTime()
      if (startTime && endTime && startTime > endTime) {
        return this.$Toast('开始时间不能大于结束时间')
      }
      let obj = {
        ...this.model,
        startTime: this.model.startTime && `${this.model.startTime} 00:00:00`,
        endTime: this.model.endTime && `${this.model.endTime} 00:00:00`,
      }
      uni.navigateTo({
        url: `/pages/MaterialManage/OutStorageSearch/list?params=${JSON.stringify(obj)}&dicts=${encodeURIComponent(JSON.stringify(this.dicts))}`,
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'printMachineName':
          this.model.printMachineName = 'C1Z001002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
