<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="搅拌工序-开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="搅拌机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入搅拌机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="搅拌机描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.description" border="none"></u--input>
        </u-form-item>

        <u-form-item label="储蓄罐" required borderBottom labelWidth="100">
          <u--input v-model="model.durableName" border="none" :focus="focusObj.durableName" placeholder="请扫描或输入储蓄罐" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="select = true">
            <view>{{ model.productSpecName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="配方号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view>{{ model.recipeName }}</view>
          </view>
        </u-form-item>

        <u-form-item label="混料方式" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.mixingMode }}
          </view>
        </u-form-item>

        <u-form-item label="工艺卡编号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.cardName }}
          </view>
        </u-form-item>

        <u-form-item label="工艺卡版本" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.cardRevision }}
          </view>
        </u-form-item>

        <u-form-item label="工艺卡标准产出(kg)" borderBottom labelWidth="180">
          <view class="w100x flex right">
            {{ model.standardOutPut }}
          </view>
        </u-form-item>

        <u-form-item label="计划浆料产出(kg)" borderBottom labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.plannedOutPut" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="投料比" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.plannedOutPut == 0 ? 0 : (model.plannedOutPut / model.standardOutPut).toFixed(3) }}
          </view>
        </u-form-item>

        <u-form-item label="出货牌号" labelWidth="100">
          <view class="w100x flex right">
            <!-- {{ selectProduct.createTime }} -->
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import PrintPackageMixin from "@/mixins/printPackage";
export default {
  mixins: [PrintPackageMixin],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productSpecName: '产品编码不能为空',
        durableName: '储存罐不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        durableName: false,
        materialPosition: false
      },
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productSpecName) {
        obj = this.columns.find(item => item.productSpecName === this.model.productSpecName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },

    'model.durableName': {
      handler(res) {
        this.changeDurableName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 搅拌机
        description: null, // 搅拌机描述
        durableName: '', // 储蓄编码
        productSpecName: null, // 产品编码
        recipeName: null, // 配方号
        mixingMode: null, // 混料方式
        cardName: null, // 工艺卡编号
        cardRevision: null, // 工艺卡版本
        standardOutPut: 0, // 工艺卡标准产出
        plannedOutPut: 0, // 计划浆料产出
        feedRatio: 0 // 投料比
      }
    },
    // 选择
    selectFirm(e) {
      this.model.productSpecName = e.value[0].productSpecName
      this.select = false
      let { machineName, processOperationName, productSpecName } = this.model
      const params = {
        machineName,
        processOperationName,
        productSpecName
      }
      this.$service.materialFeeding.validateIsExistOperationCard(params).then(res => {
        if (res) {
          let { cardName, cardRevision, mixingMode, recipeName, recipeVersion, standardOutPut } = res.datas[0]
          this.model.cardName = cardName
          this.model.cardRevision = cardRevision
          this.model.mixingMode = mixingMode
          this.model.recipeName = recipeName
          this.model.recipeVersion = recipeVersion
          this.model.standardOutPut = standardOutPut
          this.model.plannedOutPut = standardOutPut
        }
      })
    },
    // 搅拌开始
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      this.model.feedRatio = (this.model.plannedOutPut / this.model.standardOutPut).toFixed(3)
      let params = {
        ...this.model
      }
      this.$service.materialFeeding.startMixingMsgProcessor(params).then(res => {
        this.$Toast('操作成功！')
        this.initModel()
        setTimeout(() => {
          this.printPackage(res.datas[0], 'Label') // 打印
        }, 800);
      })
    },

    /* 搅拌机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.materialFeeding.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, description, processOperationName, factoryName } = res.datas[0]
          this.model.description = description
          this.model.machineName = machineName
          this.model.processOperationName = processOperationName
          this.model.factoryName = factoryName
          // 获取产品列表
          let data = {
            machineName
          }
          let resData = await this.$service.materialFeeding.getProductSpecByMachineName(data)
          resData.datas.forEach(item => {
            item.labelText = `${item.productSpecName} / ${item.description}`
          });
          this.columns = resData.datas
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 储蓄编码 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        durableName: value
      }
      try {
        let res = await this.$service.materialFeeding.queryDurableName(params)
      } catch (error) {
        // this.model.durableName = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA01'
          break;
        case 'durableName':
          this.model.durableName = 'TEST007'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'durableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
