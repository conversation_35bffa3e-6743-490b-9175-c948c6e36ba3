<template>
  <view class="minePage">
    <view class="bg"></view>
    <view class="mybox">
      <view class="fs20 fw_600 flex center h50">{{ uid }}</view>
      <view class="pl10 pr10">
        <view class="flex between hcenter bb_eee pa15">
          <view class="flex hcenter h30">
            <view class="iconfont icon-jurassic_version fs30 c_00b17b mr5"></view>
            <view class="fs15">{{ globalMap.currentVersion }}</view>
          </view>
          <view class="fs15">{{ nowversion }}</view>
        </view>
        <view class="flex between hcenter bb_eee pa15">
          <view class="flex hcenter h30">
            <view class="iconfont icon-yunshangchuan_o fs30 c_00b17b mr5"></view>
            <view class="fs15">{{ globalMap.latestVersion }}</view>
          </view>
          <view class="fs15">{{ version }}</view>
        </view>
  
        <view class="flex between hcenter bb_eee pa15">
          <view class="flex hcenter h30">
            <view class="iconfont icon-jurassic_version fs30 c_00b17b mr5"></view>
            <view class="fs15">{{ globalMap.languageSwitching }}</view>
          </view>
          <view class="flex hcenter" @click="select = true">
            <!-- {{ $utils.filterObjLabel(columns, applicationLocale) }} -->
            <view v-if="applicationLocale"> {{ applicationLocale }}</view>
            <view class="ml5" :style="{ transform: select ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </view>
        <!-- {{columns}} -->
        <view class="flex between hcenter bb_eee pa15" @click="getout">
          <view class="flex hcenter h30">
            <view class="iconfont icon-tuichu1 fs30 c_00b17b mr5"></view>
            <view class="fs15">{{ globalMap.logout }}</view>
          </view>
          <u-icon name="arrow-right" color="#999" size="15"></u-icon>
        </view>
      </view>
    </view>
    <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="NewSelectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
  </view>
</template>

<script>
import nlsStore from "@/mixins/nlsStore";
import { USER_ID, TOKEN, REFRESHTOKEN, DEFAULT_LANGUAGE, CURRENT_LOCALE, POROS_SYSTEMID, MENUS } from '@/utils/common/evtName.js'

export default {
  mixins: [nlsStore],
  name: 'mine',
  data() {
    return {
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      select: false,
      // columns: this.$LANGUAGE_ARRAY,
      columns: [],
      applicationLocale: '',
      uid: '',
      version: '',
      nowversion: '',
    };
  },
  created() {
    this.getapp()
    this.uid = this.$getLocal(USER_ID)
    // console.log('this.$getLocal(DEFAULT_LANGUAGE)', this.$getLocal(DEFAULT_LANGUAGE));
    // if (this.$getLocal(DEFAULT_LANGUAGE)) {
    //   this.applicationLocale = this.$getLocal(DEFAULT_LANGUAGE)
    // }
    this.getLocaleList()
    if (this.$getLocal(CURRENT_LOCALE)) {
      this.applicationLocale = this.$getLocal(CURRENT_LOCALE)
    }
  },
  methods: {
    async getLocaleList() {
      let res = await this.$service.nls.locale({})
      console.log('res', res);
      let localeList = []
      res.datas.forEach((item) => {
        localeList.push({
          label: item.description,
          value: item.localeName
        })
      })
      this.columns = localeList
    },
    async NewSelectFirm(e) {
      this.select = false
      if (this.applicationLocale !== e.value[0].value) {
        let local = e.value[0].value
        let res = await this.$service.nls.changeUserLocale(local)
        let currentLocale = res.datas[0].locale
        this.$setLocal(CURRENT_LOCALE, currentLocale)
        this.applicationLocale = this.$getLocal(CURRENT_LOCALE)
        this.setGlobalNls()

        let systemId = this.$getLocal(POROS_SYSTEMID)
        if (systemId) {
          let params = {
            systemId
          }
          //  国际化菜单
          let findNlsPorosMenuRes = await this.$service.nls.findNlsPorosMenuTree(params)
          let PDAMenu = findNlsPorosMenuRes.data.find((item) => item.mask === 'PDA_menus').children
          this.$setLocal(MENUS, PDAMenu)
        }
        // #ifdef APP-PLUS
        // if (!this.isAndroid) {
        //   plus.runtime.restart();
        // }
        // #endif
      }
    },
    // selectFirm(e) {
    //   this.select = false
    //   this.$i18n.locale = e.value[0].value;
    //   if (this.applicationLocale !== e.value[0].value) {
    //     setTimeout(() => {
    //       this.applicationLocale = e.value[0].value
    //       this.$i18n.locale = e.value[0].value;
    //       this.$setLocal(DEFAULT_LANGUAGE, e.value[0].value)
    //       // #ifdef APP-PLUS
    //       // if (!this.isAndroid) {
    //       //   plus.runtime.restart();
    //       // }
    //       // #endif
    //     }, 0)
    //   }
    // },
   
    async getapp() {
      let _this = this
      let res = await this.$service.common.getLastApp({}, { isFormData: true, showLoading: false })
      this.version = res.data.version
      //#ifdef APP-PLUS
      await plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        _this.nowversion = widgetInfo.version
      })
      // #endif
    },
    getout() {
      this.$Toast(this.globalMap.msExitSuccess)
      this.$store.commit('setIsLogin', false)
      uni.$emit('user-logout')
      setTimeout(() => {
        this.$removeLocal(TOKEN)
        this.$removeLocal(REFRESHTOKEN)
        uni.navigateTo({
          url: '/pages/login/login',
        })
      }, 1000)
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../styles/uform.scss';
.minePage {
  height: 100%;
  position: relative;
  .mybox {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: calc(100vh - 500rpx);
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
  }
}
.bg {
  width: 100%;
  height: 600rpx;
  overflow: hidden;
  background: url('../../static/login.png') center center no-repeat;
  background-size: 100% 100%;
}
</style>