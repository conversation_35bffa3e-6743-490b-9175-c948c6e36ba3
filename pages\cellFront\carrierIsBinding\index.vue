<template>
  <view class="myContainerPage bc_f3f3f7">
    <u-navbar title="产品载具绑定与解绑" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <u-tabs
      ref="uTabs"
      :list="tabList"
      :current="current"
      lineWidth="80"
      lineColor="#01bfbf"
      :scrollable="false"
      :activeStyle="{ color: '#333', fontWeight: 'bold' }"
      :inactiveStyle="{ color: '#ccc', transform: 'scale(1)' }"
      :itemStyle="{ height: '80rpx', width: `calc(100% / ${tabList.length})` }"
      @change="tabsChange"
    >
    </u-tabs>
    <view class="mt10 myContainer">
      <binding v-if="current === 0"></binding>
      <unbind v-if="current == 1"></unbind>
      <replace v-if="current == 2"></replace>
      <!-- <swiper class="h100x" :current="swiperCurrent" @animationfinish="animationfinish">
        <swiper-item class="h100x"> 1 </swiper-item>
        <swiper-item class="h100x"> 2 </swiper-item>
        <swiper-item class="h100x"> 3 </swiper-item>
      </swiper> -->
    </view>
  </view>
</template>

<script>
import binding from './component/binding.vue'
import unbind from './component/unbind.vue'
import replace from './component/replace.vue'
export default {
  name: 'rollerStart',
  components: {
    binding,
    unbind,
    replace
  },
  data() {
    return {
      tabList: [
        {
          name: '绑定',
        },
        {
          name: '解绑',
        },
        {
          name: '一对一替换',
        },
      ],
      current: 0, // tab
      // swiperCurrent: 0, // 滑块swipe
    };
  },

  mounted() {
  },

  methods: {
    tabsChange(e) {
      this.current = e.index
    },


  },
};
</script>

<style lang="scss" scoped>
// @import "../../../styles/uform.scss";
@import "../../../styles/publicStyle.scss";
</style>