<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="打印查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom labelWidth="100">
          <u--input v-model="model.printMachineName" border="none" focus placeholder="请扫描或输入设备编号"></u--input>
          <view class="iconfont icon-saoma" @click="scan('printMachineName')"></view>
        </u-form-item>

        <u-form-item label="批次编码" borderBottom labelWidth="100">
          <u--input v-model="model.printLotName" border="none" placeholder="请扫描或输入批次编码"></u--input>
          <view class="iconfont icon-saoma" @click="scan('printLotName')"></view>
        </u-form-item>

        <!-- <u-form-item label="生产工单" borderBottom labelWidth="100">
          <u--input v-model="model.printProductRequestName" border="none" placeholder="请输入生产工单"></u--input>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <u--input v-model="model.printProductSpecName" border="none" placeholder="请输入产品编码"></u--input>
        </u-form-item> -->

        <u-form-item label="开始日期" labelWidth="150" @click="startTimeShow = true" borderBottom>
          <u-input v-model="model.startTime" disabled disabledColor="#fff" placeholder="请选择开始日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: startTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>

        <u-form-item label="结束日期" labelWidth="150" @click="endTimeShow = true">
          <u-input v-model="model.endTime" disabled disabledColor="#fff" placeholder="请选择结束日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: endTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>
      </u--form>
      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="startTimeShow = false"
        :show="startTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('startTime', $event)"
        @cancel="startTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>
      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="endTimeShow = false"
        :show="endTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('endTime', $event)"
        @cancel="endTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>
    </view>
    <view class="btnContainer" @click="submit">查询</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      nowDate: Number(new Date()),
      selfRegisterTime: Number(new Date()),
      startTimeShow: false,
      endTimeShow: false,
      model: {},
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        printMachineName: null, // 设备编号
        printLotName: null, // 批次条码
        printProductRequestName: null, // 生产工单
        printProductSpecName: null, // 产品编码
        startTime: null, // 开始日期
        endTime: null, // 结束日期
      }
    },
    pickConfirm(type, e) {
      switch (type) {
        case 'startTime':
          this.model.startTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.startTimeShow = false
          break;
        case 'endTime':
          this.model.endTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.endTimeShow = false
          break;
        default:
          break;
      }
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },

    submit() {
      let startTime = new Date(this.model.startTime).getTime()
      let endTime = new Date(this.model.endTime).getTime()
      if (startTime && endTime && startTime > endTime) {
        return this.$Toast('开始时间不能大于结束时间')
      }
      let obj = {
        machineName: this.model.printMachineName,
        lotNames: this.model.printLotName,
        startTime: this.model.startTime && `${this.model.startTime} 00:00:00`,
        endTime: this.model.endTime && `${this.model.endTime} 23:59:59`,
      }
      uni.navigateTo({
        url: `/pages/cellFront/printSearch/detail?params=${JSON.stringify(obj)}`,
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'printMachineName':
          this.model.printMachineName = 'C1Z001002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import "../../../styles/uform.scss";
@import "../../../styles/publicStyle.scss";
</style>
