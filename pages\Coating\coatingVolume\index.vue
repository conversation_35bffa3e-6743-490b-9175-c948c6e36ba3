<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="完工-完工" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" prop="machineName" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.description }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processOperationName }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="开始时间" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processStartTime }} </view>
        </u-form-item>

        <u-form-item label="分切条数" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.slittingQuantity }} </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">产出确认</view>
        <view v-if="singlerRollFinishParames.length > 0">
          <view v-for="(item, index) in singlerRollFinishParames" :key="index">
            <u-form-item label="出货牌号" borderBottom labelWidth="120"> <u--input readonly v-model="item.lotName" border="none"></u--input></u-form-item>

            <u-form-item label="产出数量(设备)" borderBottom labelWidth="140">
              <u--input readonly v-model="item.outPutByMachine.outPutQuantityM" border="none"></u--input><span class="lin1 ml2">m</span> <u--input readonly v-model="item.outPutByMachine.outPutQuantityEA" border="none"></u--input
              ><span class="lin1 ml2">ea</span>
            </u-form-item>

            <u-form-item required label="产出数量(人工)" borderBottom labelWidth="140">
              <u--input v-decimal3 type="number" v-model="item.outPutByUser.outPutQuantityM" border="none"></u--input><span class="lin1 ml2">m</span>
              <u--input v-decimal3 type="number" v-model="item.outPutByUser.outPutQuantityEA" border="none"></u--input><span class="lin1 ml2">ea</span>
            </u-form-item>

            <u-form-item required label="面密度(g/10cm2)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.surfaceDensity" border="none" type="number"></u--input></u-form-item>

            <u-form-item required label="失重率(≤%)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.weightLossRatio" border="none" type="number"></u--input></u-form-item>
          </view>
        </view>
      </u--form>
    </view>

    <view class="btnContainer2">
      <view @click="submit">完工</view>
      <view class="disabled">条码打印</view>
    </view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID, LOCALHOST_PRINT } from '@/utils/common/evtName.js'
export default {
  mixins: [RedScan],
  name: 'stirFeeding',
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备号不能为空',
      },
      model: {},
      machineNameFlag: false,
      checkMachineOutput: false, // 是否需要校验IOT设备上报产出数量
      ratio: null,  // 单位转换
      ratioFlag: false,
      singlerRollFinishParames: [{
        lotName: '', // 出货牌号
        outPutByMachine: {
          outPutQuantityEA: 0, // 产出数量(ea)
          outPutQuantityM: 0 // 产出数量(m)
        },
        outPutByUser: { // 	产出数量(人工)
          outPutQuantityEA: 0, // 产出数量(ea)
          outPutQuantityM: 0 // 产出数量(m)
        },
        surfaceDensity: 0, // 面密度
        weightLossRatio: 0 // 失重率
      }]
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        description: null, // 设备描述:
        processOperationName: null, // 工序
        productOrderName: null, // 工单号
        productSpecName: null, // 产品编码:
        processStartTime: null, // 开始时间
        slittingQuantity: null, // 分切条数
      }
      this.singlerRollFinishParames = []
      setTimeout(() => {
        this.singlerRollFinishParames.push({
          lotName: '', // 出货牌号
          outPutByMachine: {
            outPutQuantityEA: 0, // 产出数量(ea)
            outPutQuantityM: 0 // 产出数量(m)
          },
          outPutByUser: { // 	产出数量(人工)
            outPutQuantityEA: 0, // 产出数量(ea)
            outPutQuantityM: 0 // 产出数量(m)
          },
          surfaceDensity: 0, // 面密度
          weightLossRatio: 0 // 失重率
        })
      }, 100);
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.Polar.getMachineDataForFinish(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
          if (res.datas[0].singlerRollFinishParames.length > 0) {
            this.singlerRollFinishParames = res.datas[0].singlerRollFinishParames
            this.singlerRollFinishParames.forEach(item => {
              if (!item.outPutByMachine) {
                item.outPutByMachine = {
                  outPutQuantityEA: 0, // 产出数量(ea)
                  outPutQuantityM: 0 // 产出数量(m)
                }
              }
              if (!item.outPutByUser) {
                item.outPutByUser = {
                  outPutQuantityEA: 0, // 产出数量(ea)
                  outPutQuantityM: 0 // 产出数量(m)
                }
              }
            })
          } else {
            this.singlerRollFinishParames = [{
              lotName: '', // 出货牌号
              outPutByMachine: {
                outPutQuantityEA: 0, // 产出数量(ea)
                outPutQuantityM: 0 // 产出数量(m)
              },
              outPutByUser: { // 	产出数量(人工)
                outPutQuantityEA: 0, // 产出数量(ea)
                outPutQuantityM: 0 // 产出数量(m)
              },
              surfaceDensity: 0, // 面密度
              weightLossRatio: 0 // 失重率
            }]
          }
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    // 完工
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let flag = false
      let msg = ''
      this.model.singlerRollFinishParames = JSON.parse(JSON.stringify(this.singlerRollFinishParames))
      // 判断是否有空值
      this.model.singlerRollFinishParames.map(item => {
        if (!Number(item.outPutByUser.outPutQuantityEA) || !Number(item.outPutByUser.outPutQuantityM)) {
          msg = '请填写产出数量!'
          flag = true
        }
        // if (!Number(item.surfaceDensity)) {
        //   msg = '请填写面密度!'
        //   flag = true
        // }
        // if (!Number(item.weightLossRatio)) {
        //   msg = '请填写失重率!'
        //   flag = true
        // }
      })
      if (flag) return this.$Toast(msg)
      let params = {
        ...this.model
      }
      this.$service.Polar.singlerRollFinishMsgProcessor(params).then(res => {
        this.$Toast('操作成功！')
        this.initModel()
      }).catch(() => {
        // this.initModel()
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA02'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.model.machineName = res.result
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>