<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="涂布产出确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" prop="machineName" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备编号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" prop="description" borderBottom labelWidth="100">
          <u--input readonly v-model="model.palletDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="工单" borderBottom labelWidth="100">
          <u--input readonly v-model="model.productRequestName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产出数量(设备)" borderBottom labelWidth="150">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input border="none" type="number" v-model="model.device_productQuantity" readonly></u--input>m</view>
            <view class="flex hcenter flex1 ml10"> <u--input border="none" type="number" v-model="model.device_lotWeight" readonly></u--input>ea</view>
          </view>
        </u-form-item>

        <u-form-item label="产出数量(人工)" borderBottom required labelWidth="150">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input border="bottom" type="number" v-model="model.productQuantity" @input="inputChange($event, 'productQuantity')"></u--input>m </view>
            <view class="flex hcenter flex1 ml10"> <u--input border="bottom" type="number" v-model="model.lotWeight" @input="intChange($event,'lotWeight')" :readonly="ratioFlag"></u--input>ea</view>
          </view>
        </u-form-item>

        <u-form-item label="亏损数量" borderBottom required labelWidth="150">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input border="bottom" type="number" v-model="model.lossProductQuantity" @input="inputChange($event, 'lossProductQuantity')"></u--input>m </view>
            <view class="flex hcenter flex1 ml10"> <u--input border="bottom" type="number" v-model="model.lossLotWeight" @input="intChange($event,'lossLotWeight')" :readonly="ratioFlag"></u--input>ea</view>
          </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b" @click="onSkip(`/pages/cellFront/coatingStart/detail?machineName=${model.machineName}`)">浆料信息</view>

        <view class="lin40 fs16 pl20 c_00b17b" @click="onSkip(`/pages/cellFront/materialFeeding/detail?machineName=${model.machineName}`)">箔材信息</view>
      </u--form>
    </view>

    <!-- <view class="btnContainer" @click="submit">确认</view> -->
    <view class="btnContainer2">
      <view @click="readData">读取设备数据</view>
      <view @click="submit">确认</view>
    </view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID, LOCALHOST_PRINT } from '@/utils/common/evtName.js'
export default {
  mixins: [RedScan],
  name: 'coatingConfirmIndex',
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productQuantity: '产出数量不能为空',
        lotWeight: '产出电芯计量数量不能为空',
        lossProductQuantity: '亏损数量不能为空',
        lossLotWeight: '亏损电芯计量数量不能为空',
      },
      model: {},
      machineNameFlag: false,
      checkMachineOutput: false, // 是否需要校验IOT设备上报产出数量
      ratio: null,  // 单位转换
      ratioFlag: false,
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        palletDesc: null, // 极性
        palletName: null,
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述,

        productRequestName: null,// 工单
        productSpecName: null, // 产品单号
        productSpecDesc: null, // 产品描述


        productQuantity: null, // 生产数量 kg/ m
        lotWeight: null, //极卷计量数量 pcs
        device_productQuantity: null, // 设备生产数量 
        device_lotWeight: null, // 设备电芯计量数量 

        productQuantityUnit: 'm',
        lotWeightUnit: 'ea',

        lossProductQuantity: null, // 亏损
        lossLotWeight: null, // 亏损
        lossProductQuantityUnit: 'm',
        lossLotWeightUnit: 'ea',
      }
    },
    onSkip(url) {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: url
      })
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.model.machineDesc = null
      this.model.palletDesc = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null
      this.model.productRequestName = null
      this.model.productSpecName = null
      this.model.productSpecDesc = null

      this.model.device_productQuantity = null
      this.model.device_lotWeight = null

      this.model.productQuantity = null
      this.model.lotWeight = null
      this.model.lossProductQuantity = null
      this.model.lossLotWeight = null

      this.ratio = null
      this.ratioFlag = false

      let params = {
        operateNo: '20', // 后台区分固定字段
        machineName: value,
      }
      try {
        let res = await this.$service.coating.trackOut(params)
        this.machineNameFlag = true
        this.model.machineName = res.data.machineName
        this.model.palletName = res.data.palletName
        this.model.machineDesc = res.data.machineDesc
        this.model.palletDesc = res.data.palletDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc
        this.model.productRequestName = res.data.trackOutProductRequest.productRequestName
        this.model.productSpecName = res.data.trackOutProductRequest.productSpecName
        this.model.productSpecDesc = res.data.trackOutProductRequest.productSpecDesc

        this.checkMachineOutput = res.data.checkMachineOutput
        this.ratio = res.data.unitconversionDto && res.data.unitconversionDto.ratio
        this.ratioFlag = res.data.unitconversionDto && !!res.data.unitconversionDto.ratio || false
      } catch (error) {
        this.model.machineName = null
      }
    },
    dataHandle(orginData, ratioData) {
      if (this.model[orginData] || this.model[orginData] == '0') {
        this.model[ratioData] = parseInt(this.$utils.calculate_mul(this.model[orginData], this.ratio))
      } else {
        this.model[ratioData] = null
      }
    },
    
    intChange(e, type) {
      if(e) {
        let str = e &&  e + ""
        let result = str && (str.match(/^\d*/g)[0])
        this.$nextTick(() => {
          this.$set(this.model, type, result)
        })
      }
    },

    inputChange(e, type) {
      e = e && (e.match(/^\d*(\.?\d{0,2})/g)[0])
      if (this.ratioFlag) {
        if (type === 'productQuantity') {
          this.dataHandle('productQuantity', 'lotWeight')
        }
        if (type === 'lossProductQuantity') {
          this.dataHandle('lossProductQuantity', 'lossLotWeight')
        }
      }
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },
    scan() {
      // #ifdef H5
      this.model.machineName = 'C1Z001004'
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.model.machineName = res.result
        },
      })
      // #endif
    },

    async submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
  
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.checkMachineOutput) {
        if (Number(this.model.device_productQuantity) <= 0) {
          this.$Toast(`未采集到设备[${this.model.machineName}]的产出数量`)
          return
        }
      }
      let randomNum = '';
      for (var i = 0; i < 3; i++) {
        randomNum += Math.floor(Math.random() * 10);
      }
      let time = new Date().getTime()
      let printTimeStr = this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
      let timeKey = `${this.model.machineName}_${time}_${randomNum}`
      let dto = {
        actionId: 'TrackOutCoating', // 消息名称
        menuId: 'TrackOutCoating', // 消息名称
        requestId: timeKey, // 时间戳
        reqMap: {
          userId: this.$getLocal(USER_ID), // 登陆人
          DS_TIBCO_REPLY: [],
          // 暂定写死
          DS_TIBCO_MAIN: [
            {
              DEST_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_VALUE: 'GET_RV_XMLDATA',
              MAX_TIME: '60',
            },
          ],
          langCd: '',
          DS_TIBCO_MSG: [
            {
              COMMAND_ID: 'TrackOutCoating', // 消息名称
              FILE_ID: 'TrackOutCoating', // 消息名称
              MESSAGE_NAME: 'TrackOutCoating', // 消息名称
              TRANSACTION_ID: timeKey, // 时间戳
              EVENT_USER: this.$getLocal(USER_ID), // 登陆人
              EVENT_COMMENT: '', // 功能名称
              REPLYSUBJECT_NAME: 'TrackOutCoating', // 界面名称
              TERMINAL: 'PDA', // PDA请求，WEB请求
              // FACTORYNAME:'',

              MACHINENAME: this.model.machineName,
              POLARITYTYPE: this.model.palletName,

              PROCESSOPERATIONNAME: this.model.processOperationName,
              productRequestName: this.model.productRequestName,
              PRODUCTSPECNAME: this.model.productSpecName,

              PRODUCTQUANTITY: this.model.productQuantity,
              PRODUCTQUANTITYUNIT: this.model.productQuantityUnit,
              LOTWEIGHT: this.model.lotWeight,
              LOTWEIGHTUNIT: this.model.lotWeightUnit,
              LOSSPRODUCTQUANTITY: this.model.lossProductQuantity,
              LOSSPRODUCTQUANTITYUNIT: this.model.lossProductQuantityUnit,
              LOSSLOTWEIGHT: this.model.lossLotWeight,
              LOSSLOTWEIGHTUNIT: this.model.lossLotWeightUnit
            },
          ],
          DS_TIBCO_REPEAT: [],
        },
      }
      try {
        let res = await this.$service.common.commonExec(dto)
        if (res.success) {
          this.$Toast('操作成功')
        }
        let params = {
          machineName: this.model.machineName,
          operateNo: '20',
          printTimeKey: timeKey,
          printTimeStr: printTimeStr,
          userId: this.$getLocal(USER_ID)
        }
        let pres = await this.$service.common.printLot(params, { showLoading: false })
        this.initModel()
        for (let i = 0; i < pres.data.basicLotLabelList.length; i++) {
          const ele = pres.data.basicLotLabelList[i]
          setTimeout(() => {
            let option = {
              ip: pres.data.aoiPrintIp,
              port: pres.data.aoiPrintPort,
              printName: pres.data.printName,
              priParameterntKey: [
                { type: "", name: "machineName", value: ele.machineName || '', required: false }, // 设备
                { type: "", name: "processOperationDesc", value: ele.processOperationDesc || '', required: false }, // 工序名称
                { type: "", name: "productRequestName", value: ele.productRequestName || '', required: false }, // 工单编号
                { type: "", name: "productSpecDesc", value: ele.productSpecDesc || '', required: false },// 型号
                { type: "", name: "originalLotName", value: ele.originalLotName || '', required: false }, // 批次
                { type: "", name: "lotName", value: ele.lotName || '', required: false }, // 卷号，二位码
                { type: "", name: "specification", value: ele.specification || '', required: false }, // '规格
                { type: "", name: "productQuantity", value: ele.productQuantity || '', required: false },
                { type: "", name: "productQuantityUnit", value: ele.productQuantityUnit || '', required: false },
                { type: "", name: "lotWeight", value: ele.lotWeight || '', required: false },
                { type: "", name: "lotWeightUnit", value: ele.lotWeightUnit || '', required: false },
                { type: "", name: "createUserValue", value: ele.createUserValue || '', required: false }, // 操作
                { type: "", name: "releaseUserValue", value: ele.releaseUserValue || '', required: false },// 确认
                { type: "", name: "releaseTime", value: ele.releaseTime || '', required: false },// 生产日期
                { type: "", name: "dueDate", value: ele.dueDate || '', required: false },// 有效期限
              ]
            }
            this.printPackage(option)
          }, 1000 * i)
        }
      } catch (error) {
        console.log('error', error);
      }
    },
    printPackage(options = {}) {
      let obj = {
        ReportType: "gridreport",     /*报表类型 gridreport fastreport reportmachine 为空 将默认为gridreport  */
        ReportName: "tuBu.grf",     /*报表文件名 条形码 */
        // ReportVersion: 1,              /*可选。报表版本, 为空则默认1  如果本地报表的版本过低 将从 ReportUrl 地址进行下载更新*/
        // ReportUrl: "",                  /*可选。为空 将不更新本地报表 , 如果本地报表不存在可以从该地址自动下载*/
        // ReportUrl: "",                  /*可选。为空 将不更新本地报表 , 如果本地报表不存在可以从该地址自动下载*/
        // Copies: 1,                  /*可选。打印份数，支持指定打印份数。默认1份,如果为零,不打印,只返回报表生成的pdf,jpg等文件*/
        PrinterName: `${options.printName}`,      /*可选。指定打印机，为空的话 使用默认打印机, 请在 控制面板 -> 设备和打印机 中查看您的打印机的名称 */
        // PrintOffsetX: 0,                 /*可选。打印右偏移，单位厘米。报表的水平方向上的偏移量，向右为正，向左为负。*/
        // PrintOffsetY: 0,                /*可选。打印下偏移，单位厘米。 报表的垂直方向上的偏移量，向下为正，向上为负。*/
        // token: "aa",      /*可选。只要token值在列表中 方可打印*/
        // taskId: "1234567",     /*可选。多个打印任务同时打印时，根据该id确定返回的是哪个打印任务。 */
        // exportfilename: "",      /*可选。自定义 导出 文件名称 为空 或者 自定义名称 如 test */
        // exportfiletype: "",      /*可选。自定义 导出 文件格式 为空 或者 自定义名称 如 pdf  */
        ///*字段， type ftBlob (base64格式) ,ftString ftInteger ftBoolean, ftFloat, ftCurrency,ftDateTime,  size (ftString 设置为实际长度,其他的设置为0,例如 ftInteger ftBlob 等设置为0 ) 
        Parameter: JSON.stringify(options.priParameterntKey),
      }
      if (!options.ip || !options.port) {
        return
      }
      let printerUrl = `http://${options.ip}:${options.port}/printreport`
      if (LOCALHOST_PRINT) {
        printerUrl = `http://${LOCALHOST_PRINT}:${options.port}/printreport`
      }
      uni.request({
        url: printerUrl,
        data: obj,
        header: {
          'content-type': 'application/x-www-form-urlencoded', //自定义请求头信息
        },
        method: 'POST',
        success: (res) => { },
      })

    },
    async readData() {
      if (!this.model.machineName) {
        this.$Toast('设备编号不能为空')
        return
      }
      if (!this.machineNameFlag) {
        return this.$Toast('请重新输入或扫描设备编号')
      }
      this.model.device_productQuantity = null
      this.model.device_lotWeight = null
      let params = {
        machineName: this.model.machineName,
        operateNo: '20',
      }
      let res = await this.$service.common.queryMachineOutput(params)
      if (res.data && res.data[0]) {
        this.model.device_productQuantity = res.data[0].machineProductQuantity
        this.model.device_lotWeight = res.data[0].machineLotWeight
        this.model.productQuantity = res.data[0].machineProductQuantity
        this.model.lotWeight = res.data[0].machineLotWeight
      }
    }

  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>