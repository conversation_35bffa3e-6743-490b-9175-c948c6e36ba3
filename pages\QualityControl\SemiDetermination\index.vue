<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="在制品不良判定" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="在制品条码" borderBottom required labelWidth="100">
          <u--input v-model="model.zzptm" border="none" focus placeholder="请扫描在制品条码" @focus="focusEvent('zzptm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('zzptm')"></view>
        </u-form-item>

        <u-form-item label="不良现象" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.blxx }} </view>
        </u-form-item>

        <u-form-item label="设备" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.sb }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.scgd }} </view>
        </u-form-item>

        <u-form-item label="品种名" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.pzm }} </view>
        </u-form-item>

        <u-form-item label="处置方式" required  borderBottom labelWidth="100">
          <!-- <view class="w100x flex right"> {{ model.durableType }} </view> -->
          <view class="w100x flex" :style="'flex-direction: row-reverse'">
            <u-radio-group
                v-model="model.czfs"
                placement="column"
                style="width: 100%;"
              >
              <view style="display: flex;flex-direction: row-reverse">
                <u-radio
                  v-for="(item, index) in radiolist1"
                  :key="index"
                  :customStyle="{ marginRight: '10rpx' }"
                  :label="item.name"
                  :name="item.name"
                >
                </u-radio>
              </view>
            </u-radio-group>
          </view>
        </u-form-item>

        <!-- <u-form-item label="不良原因" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType('blyy')">
            <view>{{ model.blyy }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item> -->

        <template v-if="model.czfs == '拆解'">
          <u-form-item label="正极片弹夹" borderBottom required labelWidth="100">
            <u--input v-model="model.zjpdj" border="none" focus placeholder="请扫描正极片弹夹" @focus="focusEvent('zjpdj')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('zjpdj')"></view>
          </u-form-item>
  
          <u-form-item label="正极片良数" borderBottom required labelWidth="100">
            <view class="w100x flex right">
              <view class="flex w50x hcenter">
                <u--input v-model="model.zjpls" border="none" placeholder="请输入正极片良数"></u--input>
              </view>
            </view>
          </u-form-item>
  
          <u-form-item label="正极片弹夹数量" borderBottom labelWidth="150">
            <view class="w100x flex right"> {{ model.zjpdjsl }} </view>
          </u-form-item>

          <u-form-item label="负极片弹夹" borderBottom required labelWidth="100">
            <u--input v-model="model.fjpdj" border="none" focus placeholder="请扫描负极片弹夹" @focus="focusEvent('fjpdj')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('fjpdj')"></view>
          </u-form-item>
  
          <u-form-item label="负极片良数" borderBottom required labelWidth="100">
            <view class="w100x flex right">
              <view class="flex w50x hcenter">
                <u--input v-model="model.fjpls" border="none" placeholder="请输入负极片良数"></u--input>
              </view>
            </view>
          </u-form-item>
  
          <u-form-item label="负极片弹夹数量" borderBottom labelWidth="150">
            <view class="w100x flex right"> {{ model.fjpdjsl }} </view>
          </u-form-item>
        </template>

        <u-form-item label="备注" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.bz" border="none" placeholder="请输入备注"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="不良原因" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType('blyy')">
            <view>{{ model.blyy }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <!-- <u-form-item label="备注" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.bz" border="none" placeholder="请输入备注"></u--input>
            </view>
          </view>
        </u-form-item> -->

        <u-form-item label="判定数量" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="SemiRecordNum" border="none" readonly></u--input>
            </view>
          </view>
        </u-form-item>

        <!-- <u-form-item label="待提交不良明细" borderBottom labelWidth="200">
          <view class="w100x flex right"> {{ model.lotQuantity }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item> -->
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
        <view class="mr5">待确认不良判定明细</view>
        <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
      </view>
      <view class="pl10">{{ tipContent }}</view>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    // this.changeBz = this.$debounce(this.changeBz, 4000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        zzptm: '',
        sb: '',
        sbType: '',
        gx: '',
        gxType: '',
        scgd: '',
        pzm: '',
        blxx: '',
        blxxType: '',
        reasonCodeType: '',
        blyy: '',
        blyyType: '',
        blyyText: '',
        ngReasonCodeType: '',
        bz: '',
        czfs: '复投',
        zjpdj: '',
        zjpls: '',
        zjpdjsl: '',
        fjpdj: '',
        fjpls: '',
        fjpdjsl: '',
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      selectType: '',
      searchData: [],
      oldGxType: '',
      tipContent: '',
      radiolist1: [
        {
          name: '放行'
        },
        {
          name: '报废'
        },
        {
          name: '拆解'
        },
        {
          name: '复投'
        }
      ],
      zjpdjObj: {},
      fjpdjObj: {}
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.SemiDeterminationList;
      },
      set(value) {
        this.$store.commit('changeSemiDeterminationList', value);
      }
    },
    SemiRecordNum: {
      get() {
        // let arr = []
        // this.list.forEach((item) => {
        //   if(arr.indexOf(item.zzptm) == -1) {
        //     arr.push(item.zzptm)
        //   }
        // })
        return this.list.length;
      },
      set(value) {
        // this.$store.commit('setCount', value);
      }
    }
  },
  watch: {
    'model.zzptm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    // 'model.bz': {
    //   handler(val) {
    //     this.changeBz(val)
    //   }
    // },
    'model.zjpdj': {
      handler(val) {
        if(val && this.model.czfs == '拆解') {
          this.changezjpdj(val, 'C')
        }
      }
    },
    'model.fjpdj': {
      handler(val) {
        if(val && this.model.czfs == '拆解') {
          this.changezjpdj(val, 'A')
        }
      }
    },
    'model.zjpls': {
      handler(val) {
        if(val && this.model.czfs == '拆解') {
          console.log('model.zjpls', (Number(val) + Number(this.zjpdjObj.lotQuantity)), this.zjpdjObj);
          if((Number(val) + Number(this.zjpdjObj.lotQuantity)) > Number(this.zjpdjObj.capacity)) {
            this.$Toast('输入数量+正极片弹夹数量不能大于该弹夹的装载最大数量')
            this.model.zjpls = ''
            return
          }
        }
      }
    },
    'model.fjpls': {
      handler(val) {
        if(val && this.model.czfs == '拆解') {
          console.log('model.fjpls', (Number(val) + Number(this.fjpdjObj.lotQuantity)), this.fjpdjObj);
          if((Number(val) + Number(this.fjpdjObj.lotQuantity)) > Number(this.fjpdjObj.capacity)) {
            this.$Toast('输入数量+负极片弹夹数量不能大于该弹夹的装载最大数量')
            this.model.fjpls = ''
            return
          }
        }
      }
    },
    // 'model.blxxType': {
    //   handler(val) {
    //     // 切换不良现象，录入明细中
    //     // this.changeDurableName(val)
    //     if(this.SemiRecordNum == 0) {
    //       // 
    //     }
    //   }
    // },
    // 'model.lotName': {
    //   handler(res) {
    //     this.changeLotName(res)
    //   }
    // }
  },
  onLoad() {
    this.list = []
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        processOperationName: this.model.gxType
      }
      this.$service.QualityControl.getReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            label: item.reasonCode + '/' + item.reasonCodeDesc,
            value: item.reasonCode,
            reasonCodeDesc: item.reasonCodeDesc,
            reasonCodeType: item.reasonCodeType
          }
          // return item.reasonCode
        })
        // this.GetReasonCodeTypeList.forEach(item => {
        //   item = item.reasonCodeType + '/' + item.typeDesc
        // })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if(type == 'blyy') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if(this.selectType == 'blyy') {
        // 选完不良原因清空备注
        // this.model.bz = ''
        this.model.blyy = e.value[0].label
        this.model.blyyType = e.value[0].value
        this.model.blyyText = e.value[0].reasonCodeDesc
        this.model.ngReasonCodeType = e.value[0].reasonCodeType
        // 选完不良原因自动提交
        // 如果有备注、在制品条码、不良原因，则直接加入明细中
        if(this.model.zzptm && this.model.blyy) {
          this.searchData.forEach(item => {
            let obj = {
              ...this.model,
              ...item,
              // blxx: this.model.blxx ? this.model.blxx : '',
              // blxxType: this.model.blxxType ? this.model.blxxType : '',
              // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
            }
            this.oldGxType = this.model.gxType
            this.list.push(obj)
          })
          this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
          this.initModel()
        }
      }
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/QualityControl/SemiDetermination/modules/SemiDeterminationList`
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        zzptm: '',
        sb: '',
        sbType: '',
        gx: '',
        gxType: '',
        scgd: '',
        pzm: '',
        blxx: '',
        // blxxType: this.model.blxxType ? this.model.blxxType : '',
        // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : '',
        blyy: this.model.blyy ? this.model.blyy : '',
        blyyType: this.model.blyyType ? this.model.blyyType : '',
        blyyText: this.model.blyyText ? this.model.blyyText : '',
        ngReasonCodeType: this.model.ngReasonCodeType ? this.model.ngReasonCodeType : '',
        // bz: this.model.bz ? this.model.bz : '',
        czfs: this.model.czfs ? this.model.czfs : '',
        bz: '',
        zjpdj: '',
        zjpls: '',
        zjpdjsl: '',
        fjpdj: '',
        fjpls: '',
        fjpdjsl: '',
      }
    },
    submit() {
      if(this.SemiRecordNum == 0) {
        this.$Toast('暂无待确认不良判定明细！')
        return
      }
      let arr = this.list.map(item => {
        return {
          ...item,
          ngReasonCode: item.blyyType,
          ngReasonCodeDesc: item.blyyText,
          ngReasonCodeType: item.ngReasonCodeType,
          eventComment: item.bz,
          ngState: item.czfs == '复投' ? 'R' : (item.czfs == '报废' ? 'S' : (item.czfs == '拆解' ? 'D' : 'G')), // 复投是R，报废是S，拆解D，放行G
          durableNameC: item.zjpdj,
          goodQuantityC: item.zjpls,
          durableNameA: item.fjpdj,
          goodQuantityA: item.fjpls,
        }
      })
      console.log('this.arr', arr);
      let params = {
        functionType: 'Judge',
        ngLotRecordList: arr
      }
      this.$service.QualityControl.NgLotRecordAndJudgeSubmit(params).then(res => {
        this.$Toast('在制品不良判定提交成功！')
        // 提交成功初始化
        this.model = {
          zzptm: '',
          sb: '',
          sbType: '',
          gx: '',
          gxType: '',
          scgd: '',
          pzm: '',
          blxx: '',
          blxxType: '',
          reasonCodeType: '',
          blyy: '',
          blyyType: '',
          blyyText: '',
          ngReasonCodeType: '',
          bz: '',
          czfs: '复投',
          zjpdj: '',
          zjpls: '',
          zjpdjsl: '',
          fjpdj: '',
          fjpls: '',
          fjpdjsl: '',
        }
        // 提交成功清空明细
        this.list = []
        this.tipContent = ''
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 在制品条码 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        functionType: 'Judge',
        lotName: value
      }
      try {
        let res = await this.$service.QualityControl.getLotListData(params)
        this.searchData = res.datas ? res.datas : []
        if (res.datas.length > 0) {
          let data =  res.datas[0]

          // 在制品条码是否存在明细中
          let flag = false
          this.list.forEach(item => {
            this.searchData.forEach(item2 => {
              if(item.lotName == item2.lotName) {
                // 在制品条码有一条已存在待提交不良明细中，不允许添加
                flag = true
              }
            })
          })
          if(flag) {
            this.$Toast('在制品条码已存在待提交不良明细中！')
            this.initModel()
            return
          }

          // // 不存在明细中，判断是否有明细
          // if(this.SemiRecordNum == 0) {
          //   // 明细中没有，则获取不良现象下拉
          //   this.GetReasonCodeType()
          // } else {
          //   // 明细中有数据，则判断工序是否一致，不一致则清空数据提示工序不一致，保留之前的不良现象下拉
          //   this.GetReasonCodeType()
          // }

          this.model = {
            ...res.datas[0],
            // zzptm: data.lotName,
            zzptm: this.model.zzptm,
            sb: data.machineName + '/' + data.machineDesc,
            sbType: data.machineName,
            gx: data.processOperationName + '/' + data.processOperationDesc,
            gxType: data.processOperationName,
            scgd: data.productOrderName,
            pzm: data.productSpecName + '/' + data.productSpecDesc,
            pzmType: data.productSpecName,
            blxx: data.reasonCodeDesc,
            // blxxType: this.model.blxxType ? this.model.blxxType : '',
            // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
            blyy: this.model.blyy ? this.model.blyy : '',
            blyyType: this.model.blyyType ? this.model.blyyType : '',
            blyyText: this.model.blyyText ? this.model.blyyText : '',
            ngReasonCodeType: this.model.ngReasonCodeType ? this.model.ngReasonCodeType : '',
            // bz: this.model.bz ? this.model.bz : '',
            czfs: this.model.czfs ? this.model.czfs : '',
            bz: '',
            zjpdj: '',
            zjpls: '',
            zjpdjsl: '',
            fjpdj: '',
            fjpls: '',
            fjpdjsl: '',
          }

          if(this.SemiRecordNum == 0) {
            // if(this.model.blyyType && this.model.bz) {
            if(this.model.blyyType) {
              // 无明细，有不良原因和备注，判断老工序：同工序加入明细中，否则清空
              if(this.oldGxType == data.processOperationName) {
                // 工序一致，并且有不良现象选择，添加进明细中
                this.searchData.forEach(item => {
                  let obj = {
                    ...this.model,
                    ...item,
                    // blxx: this.model.blxx ? this.model.blxx : '',
                    // blxxType: this.model.blxxType ? this.model.blxxType : '',
                    // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
                  }
                  this.oldGxType = this.model.gxType
                  this.list.push(obj)
                })
                this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
                this.initModel()
              }
            } else {
              // 无明细，无不良原因，获取下拉，清空备注
              this.GetReasonCodeType()
              this.model.bz = ''
            }
          } else {
            let flag2 = false
            this.list.forEach(item => {
              if(item.gxType != data.processOperationName) {
                // 工序不一致，清空数据，保留之前的不良现象
                flag2 = true
              }
            })
            if(flag2) {
              this.$Toast('当前条码工序不一致，请重新扫描！')
              this.tipContent = `在制品条码${this.model.zzptm}提交明细失败`
              this.initModel()
              return
            }
            // if(this.model.blyyType && this.model.bz) {
            if(this.model.blyyType) {
              // 工序一致，并且有不良原因和备注，添加进明细中
              this.searchData.forEach(item => {
                let obj = {
                  ...this.model,
                  ...item,
                  // blxx: this.model.blxx ? this.model.blxx : '',
                  // blxxType: this.model.blxxType ? this.model.blxxType : '',
                  // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
                }
                this.oldGxType = this.model.gxType
                this.list.push(obj)
              })
              this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
              this.initModel()
            }
          }
        } else {
          this.model.zzptm = ''
          this.$Toast('在制品不存在！')
        }
      } catch (error) {
        console.log(error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
    // changeBz(value) {
    //   if (!value) return
    //   // 如果有备注、在制品条码、不良原因，则直接加入明细中
    //   if(this.model.zzptm && this.model.blyy) {
    //       this.searchData.forEach(item => {
    //         let obj = {
    //           ...this.model,
    //           ...item,
    //           // blxx: this.model.blxx ? this.model.blxx : '',
    //           // blxxType: this.model.blxxType ? this.model.blxxType : '',
    //           // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
    //         }
    //         this.oldGxType = this.model.gxType
    //         this.list.push(obj)
    //       })
    //       this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
    //       this.initModel()
    //     }
    // },
    async changezjpdj(value, type) {
      if (!value) return
      let params = {
        durableName: value,
        polarity: type
      }
      try {
        let res = await this.$service.QualityControl.getDurableInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.zjpdj = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if(type == 'C') {
            this.model.zjpdjsl = productData.lotQuantity // 正极片弹夹数量
            this.zjpdjObj= productData // 正极片详情
          } else {
            this.model.fjpdjsl = productData.lotQuantity // 正极片弹夹数量
            this.fjpdjObj= productData // 正极片详情
          }
        }
      } catch (error) {
        if(type == 'C') {
          this.model.zjpdj = ''
        } else {
          this.model.fjpdj = ''
        }
        
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
