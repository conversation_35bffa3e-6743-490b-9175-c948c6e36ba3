<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="碾压明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10" v-if="!$utils.isEmpty(simpleTrackProduct)">
      <view class="flex between h40 hcenter c_999">
        <view>工单</view>
        <view>{{ simpleTrackProduct.productRequestName }}</view>
      </view>
      <view class="flex between h40 hcenter c_999">
        <view>产品</view>
        <view class="flex right">
          {{ $utils.optionShowConfig(simpleTrackProduct.productSpecName, simpleTrackProduct.productSpecDesc) }}
        </view>
      </view>
      <view class="flex between h40 hcenter c_999">
        <view>开始时间</view>
        <view>{{ simpleTrackProduct.lotCreateTime }}</view>
      </view>
    </view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>极卷条码</view>
              <view>{{ ele.lotName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上卷数量</view>
              <view class="flex hcenter">
                <view class="mr20">{{ ele.productQuantity }} {{ ele.productQuantityUnit }}</view>
                <view>{{ ele.lotWeight }} {{ ele.lotWeightUnit }}</view>
              </view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上卷时间</view>
              <view>{{ ele.lastLoggedInTime }}</view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.initSearchModel()
    this.getData()
  },
  methods: {
    initSearchModel(val) {
      this.searchModel = {
        machineName: this.machineName,
        pageNo: this.pageNumber,
        limit: this.pageSize,
      }
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      // setTimeout(() => {
      //   // let res = Array.from({ length: 20 }, (v, i) => i)
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return
      let params = JSON.parse(JSON.stringify(this.searchModel))
      this.$service.roller.outQueryPageList(params).then((res) => {
        if (res && res.success) {
          let { simpleTrackProductRequest, trackProductRequestPage } = res.data
          this.simpleTrackProduct = simpleTrackProductRequest

          this.model = clearOldData ? trackProductRequestPage.records : [...this.model, ...trackProductRequestPage.records]
          if (this.searchModel.pageNo == trackProductRequestPage.pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import "../../../styles/publicStyle.scss";
</style>