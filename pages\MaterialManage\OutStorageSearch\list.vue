<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="出库单列表" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="myContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>出库单号</view>
            <view>
              <view>{{ ele.deliveryOrderNo }}</view>
              <!-- {{ $utils.optionShowConfig(ele.printMachineName, ele.printMachineDesc) }} -->
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>单据类型</view>
            <view>
              {{ $utils.filterObjLabel(dicts.deliveryTypeList, ele.deliveryType) }}
              <!-- {{ $utils.optionShowConfig(ele.printProductSpecName, ele.printProductSpecDesc) }} -->
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view> 工单编码</view>
            <view>{{ ele.productOrderName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>仓库</view>
            <view>
              {{ $utils.optionShowConfig(ele.stockCode, ele.stockName) }}
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>已扫描批次数量/总数量</view>
            <view> {{ ele.scanQty }}/{{ ele.totalQtq }} </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>创建人</view>
            <view>{{ ele.createUser }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>创建时间</view>
            <view> {{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
          </view>
          <view>
            <u-button type="success" text="出库" @click="summit(ele)"></u-button>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      paramsOption: {},
      dicts: {
        deliveryTypeList:[],
      },
    };
  },

  onLoad(e) {
    this.paramsOption = e && JSON.parse(e.params)
    this.dicts = JSON.parse(decodeURIComponent(e.dicts))
    console.log('detail', this.dicts, this.paramsOption);
    this.initSearchModel()
    this.getData()
  },
  methods: {
    moment,
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
        console.log(this.dicts, '88')
      })
    },
    summit(ele) {
      let obj = {
        ...ele,
      }
      uni.navigateTo({
        url: `/pages/MaterialManage/OutStorageSearch/detail?params=${JSON.stringify(obj)}`,
      })
    },
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },
    returnRefresh() {
      this.getData(true, true)
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      this.$service.DeliveryOrderController.queryPageList(params).then((res) => {
        if (res && res.success) {
          // this.model = res.datas
          // this.status = 'nomore'
          this.model = clearOldData ? res.datas[0].records : [...this.model, ...res.datas[0].records]
          if (this.searchModel.pageNo == res.datas.pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>