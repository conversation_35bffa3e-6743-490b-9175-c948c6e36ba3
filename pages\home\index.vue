<template>
  <view class="indexPage">
    <u-navbar :title="globalMap.lbHome" height="50px" :titleStyle="{ color: '#fff' }" placeholder>
      <view slot="left"></view>
    </u-navbar>
    <!-- {{ globalMap }}==== -->
    <!-- <button @click="test">test</button> -->
    <view class="content">
      <view v-for="(item, index) in PDAMenu" :key="index" class="bc_fff mb10">
        <view v-if="!item.hidden">
          <view class="fb fs16 ml12 pt12">
            {{ item.name }}
            <!-- {{ $t(`menu.${item.mask}`) }}  -->
          </view>
          <view v-if="!item.children.hidden" class="flex flex_wrap">
            <view v-for="(children, index) in item.children" :key="index" class="w25x flex flex_column center h110" @click="tonext(children)">
              <view class="w60 h60">
                <image :src="concatsrc(children.mask)" />
              </view>
              <view>{{ children.name }}</view>
              <!-- <view>{{ $t(`menu.${children.mask}`) ? $t(`menu.${children.mask}`) : children.name }}</view> -->
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="content">
      <view class="flex flex_wrap bc_fff mb20">
        <view v-for="(item, index) in PDAMenu" :key="index" class="w33_3x flex center h120">
          <view v-if="!item.hideMenu" class="flex flex_column center" @click="tonext(item.path)">
            <view class="w40 h40 mb15">
              <image src="../../static/home/<USER>" />
            </view>
            <view>{{ item.meta.title }}</view>
          </view>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
import { MENUS } from '@/utils/common/evtName.js'
export default {
  name: 'MesTworkPdaIndex1',
  data() {
    return {
      PDAMenu: [],
      result: [],
      nlsMap: {},
      globalMap: getApp().globalData.globalMap // 获取全局数据
    };
  },
  mounted() {
    this.PDAMenu = this.$getLocal(MENUS)
    this.tree(this.PDAMenu)
  },

  methods: {
    test() {
      this.globalMap.btCloseAllTab = Math.random() * 100
    },
    tree(menuArr) {
      menuArr.forEach(ele => {
        this.result.push(`${ele.mask}:${ele.name}`)
        if (ele.children && ele.children.length > 0) {
          this.tree(ele.children)
        }
      });
    },
    async tonext(item) {
      await this.setGlobalNls(item.id)
      let pageParams = {
        pageTitle: item.name,
        menuId: item.id,
        nlsMap: this.nlsMap,
      }
      uni.navigateTo({
        url: `${item.url}?&pageParams=${encodeURIComponent(JSON.stringify(pageParams))}`,
      })
    },

    async setGlobalNls(menuId) {
      let params = {
        appName: 'GFM',
        porosMenuId: menuId,
      };
      let res = await this.$service.nls.getGlobalNls(params)
      let nlsMap = {}
      if (res.datas.length > 0) {
        res.datas.forEach((item) => {
          nlsMap[item.labelKey] = item.labelText;
        })
      }
      this.nlsMap = nlsMap
    },

    concatsrc(mask) {
      return `../../static/home/<USER>
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../styles/publicStyle.scss';
image {
  width: 100%;
  height: 100%;
}
.indexPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  // height: calc(100vh - 100rpx);
  .content {
    flex: 1;
    // overflow-y: scroll;
    padding-bottom: 100rpx;
  }
}
</style>