/**
 * @Description 用于NFC功能
 * <AUTHOR>
 * @Date 2022-03-25 14:58:12
 * @Company 格创东智（深圳）科技有限公司
 */
const NFC = {
  data() {
    return {
      NfcAdapter: '',
    }
  },
  created() {
    // #ifndef H5
    this.NFCInit()
    // #endif
  },
  methods: {
    // nfc入口
    NFCInit() {
      try {
        var main = plus.android.runtimeMainActivity()
        //console.log(main);
        var Intent = plus.android.importClass('android.content.Intent')
        // console.log(Intent);
        var Activity = plus.android.importClass('android.app.Activity')
        //console.log(Activity);
        var PendingIntent = plus.android.importClass(
          'android.app.PendingIntent'
        )
        // console.log(PendingIntent);
        var IntentFilter = plus.android.importClass(
          'android.content.IntentFilter'
        )
        // console.log(IntentFilter);
        // var Uri = plus.android.importClass('android.net.Uri');
        // var Bundle = plus.android.importClass('android.os.Bundle');
        // var Handler = plus.android.importClass('android.os.Handler');
        //console.log(Handler);
        this.NfcAdapter = plus.android.importClass('android.nfc.NfcAdapter')
        // console.log(this.NfcAdapter)
        var _nfcAdapter = this.NfcAdapter.getDefaultAdapter(main)
        // console.log(_nfcAdapter)

        var ndef = new IntentFilter('android.nfc.action.NDEF_DISCOVERED') //NfcAdapter.ACTION_NDEF_DISCOVERED
        // console.log(ndef);
        var tag = new IntentFilter('android.nfc.action.TAG_DISCOVERED') //NfcAdapter.ACTION_TECH_DISCOVERED
        // console.log(tag);
        var tech = new IntentFilter('android.nfc.action.TECH_DISCOVERED')
        // console.log(tech);
        var intentFiltersArray = [ndef, tag, tech]

        var techListsArray = [
          ['android.nfc.tech.Ndef'],
          ['android.nfc.tech.IsoDep'],
          ['android.nfc.tech.NfcA'],
          ['android.nfc.tech.NfcB'],
          ['android.nfc.tech.NfcF'],
          ['android.nfc.tech.Nfcf'],
          ['android.nfc.tech.NfcV'],
          ['android.nfc.tech.NdefFormatable'],
          ['android.nfc.tech.MifareClassic'],
          ['android.nfc.tech.MifareUltralight'],
        ]

        var _intent = new Intent(main, main.getClass())
        // console.log(_intent);
        _intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)

        var pendingIntent = PendingIntent.getActivity(main, 0, _intent, 0)
        // console.log(pendingIntent);

        if (_nfcAdapter == null) {
        } else if (_nfcAdapter.isEnabled() == false) {
        } else {
          _nfcAdapter.enableForegroundDispatch(
            main,
            pendingIntent,
            IntentFilter,
            techListsArray
          )
        }
      } catch (e) {
        //TODO handle the exception
      }
    },
    NFCReadUID() {
      var main = plus.android.runtimeMainActivity()
      var _intent = main.getIntent()
      let Intent = plus.android.importClass('android.content.Intent')
      // var intent = new Intent(main, main.getClass());
      // console.log(intent, 'intent')
      var _action = _intent.getAction()
      var MifareClassic = plus.android.importClass(
        'android.nfc.tech.MifareClassic'
      )
      if (
        this.NfcAdapter.ACTION_NDEF_DISCOVERED == _action ||
        this.NfcAdapter.ACTION_TAG_DISCOVERED == _action ||
        this.NfcAdapter.ACTION_TECH_DISCOVERED == _action
      ) {
        var Tag = plus.android.importClass('android.nfc.Tag')
        var bind_codes
        var tagFromIntent = _intent.getParcelableExtra(
          this.NfcAdapter.EXTRA_TAG
        )
        var Ndef = plus.android.importClass('android.nfc.tech.Ndef')
        var ndef = Ndef.get(tagFromIntent)
        var NfcC = plus.android.importClass('android.nfc.tech.NfcV')
        var test_A = NfcC.get(tagFromIntent)
        test_A.close()
        test_A.connect()

        var tagUid = tagFromIntent.getId()
        // 使用NfcV扇区读取
        var shanquFlag = true
        var cmd = []
        cmd[0] = 0x22
        cmd[1] = 0x20
        for (var i in tagUid) {
          cmd.push(tagUid[i])
        }
        let res = []
        this.$Toast('hereone')
        for (var i = 0; shanquFlag; i++) {
          cmd[10] = i
          var responses = test_A.transceive(cmd)
          this.$Toast('读取i扇区数据：', responses)
          console.log('读取i扇区数据：', responses)
          if (responses == null) {
            shanquFlag = false
          }
          for (var j in responses) {
            if (j == 0) continue
            if (responses[j] == 0) {
              shanquFlag = false
              break
            }
            res.push(responses[j])
          }
        }

        // // 0扇区

        // cmd[10] = 0x00;
        // var responses0 = test_A.transceive(cmd);
        // console.log("读取0扇区数据：", responses0)
        // let res = []

        // res.push(responses0[1]);res.push(responses0[2]);
        // res.push(responses0[3]);res.push(responses0[4]);
        // // 读取扇区只需要改这个cmd[10]改成对应扇区 彬哥考虑抽出方法
        // cmd[10] = 0x01;
        // var responses1 = test_A.transceive(cmd);
        // console.log("读取1扇区数据：", responses1)
        // res.push(responses1[1]);res.push(responses1[2]);
        // res.push(responses1[3]);res.push(responses1[4]);

        // cmd[10] = 0x02;
        // var responses2 = test_A.transceive(cmd);
        // console.log("读取2扇区数据：", responses2)
        // res.push(responses2[1]);res.push(responses2[2]);
        // res.push(responses2[3]);res.push(responses2[4]);
        let code = this.byteToString(res)
        this.$Toast(code)

        return code
      }
    },
    byteToString(arr) {
      if (typeof arr === 'string') {
        return arr
      }
      var str = '',
        _arr = arr
      for (var i = 0; i < _arr.length; i++) {
        var one = _arr[i].toString(2),
          v = one.match(/^1+?(?=0)/)
        if (v && one.length == 8) {
          var bytesLength = v[0].length
          var store = _arr[i].toString(2).slice(7 - bytesLength)
          for (var st = 1; st < bytesLength; st++) {
            store += _arr[st + i].toString(2).slice(2)
          }
          str += String.fromCharCode(parseInt(store, 2))
          i += bytesLength - 1
        } else {
          str += String.fromCharCode(_arr[i])
        }
      }
      return str
    },
  },
}

export default NFC
