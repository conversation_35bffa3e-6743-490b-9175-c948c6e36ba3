<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="在制品工序调整" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="在制品条码" borderBottom required labelWidth="120">
          <u--input v-model="model.zzptm" border="none" focus placeholder="请扫描在制品条码" @focus="focusEvent('zzptm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('zzptm')"></view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item>

        <u-form-item label="工单编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gdbm }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.cpbm }} </view>
        </u-form-item>

        <u-form-item label="产品名称" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.cpmc }} </view>
        </u-form-item>

        <u-form-item label="工艺路线" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gylx }} </view>
        </u-form-item>
        
        <u-form-item label="调整工序" required borderBottom labelWidth="130">
          <view class="w100x flex right" @click="selectReasonCodeType('tzgx')">
            <view>{{ model.tzgx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'

export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        zzptm: '',
        gx: '',
        gdbm: '',
        cpbm: '',
        cpmc: '',
        gylx: '',
        tzgx: '',
        tzgxType: '',
        nodePosition: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      ProcessOperationNameList: [
        {
          label: 'aa',
          value: '1'
        },
        {
          label: 'bb',
          value: '2'
        }
      ],
      radiolist1: [
        {
          name: '退货'
        },
        {
          name: '放行'
        }
      ],
    }
  },
  watch: {
    'model.zzptm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    // 'model.lotName': {
    //   handler(res) {
    //     this.changeLotName(res)
    //   }
    // }
  },
  onLoad() {
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        processFlowName: this.model.processFlowName,
        processFlowRevision: this.model.processFlowRevision,
      }
      this.$service.ProcessAdjustment.getProcessOpData(params).then(res => {
        this.ProcessOperationNameList = res.datas.map((item) => {
          return {
            label: item.processOperationName + '/' + item.description,
            value: item.processOperationName,
            nodePosition: item.nodePosition
          }
        })
      })
    },
    selectReasonCodeType(type) {
      if(type == 'tzgx') {
        this.columns[0] = [].concat(this.ProcessOperationNameList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      this.model.tzgx = e.value[0].label
      this.model.tzgxType = e.value[0].value
      this.model.nodePosition = e.value[0].nodePosition
      // this.focusObj.saveNo = true
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/BindingAndUnbinding/modules/ProductList?durableName=${this.model.durableName}`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        zzptm: '',
        gx: '',
        gdbm: '',
        cpbm: '',
        cpmc: '',
        gylx: '',
        tzgx: '',
        tzgxType: '',
        nodePosition: ''
      }
    },
    submit() {
      if(!this.model.zzptm) {
        this.$Toast('请输入在制品条码！')
        return
      }
      if(!this.model.tzgx) {
        this.$Toast('请选择调整工序！')
        return
      }
      let params = {
        lotName: this.model.zzptm,
        processFlow: this.model.processFlowName,
        processFlowRevision: this.model.processFlowRevision,
        processOperation: this.model.tzgxType,
        // nodePosition: this.model.tzgxType,
        nodePosition: this.model.nodePosition,
        userId: this.$getLocal(USER_ID)
      }
      this.$service.ProcessAdjustment.changeOperationByLotName(params).then(res => {
        this.$Toast('工序调整提交成功！')
        // 提交成功初始化
        this.initModel()
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 载具 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        lotName: value
      }
      try {
        let res = await this.$service.ProcessAdjustment.getLotData(params)
        console.log('getConsumableData', res);
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...res.datas[0],
            zzptm: value,
            gx: data.processOperationName + '/' + data.opDescription,
            gdbm: data.productOrderName,
            cpbm: data.productSpecName,
            cpmc: data.psDescription,
            gylx: data.processFlowName + '/' + data.pfDescription,
            tzgx: '',
            tzgxType:'',
            nodePosition: ''
          }
          // 获取调整工序
          // 获取不良现象
          this.GetReasonCodeType()
        } else {
          this.model.clbqtm = ''
          this.$Toast('在制品条码不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
