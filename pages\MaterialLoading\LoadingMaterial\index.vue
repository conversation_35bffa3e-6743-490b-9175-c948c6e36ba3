<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>

    <view class="myContainer ma5">
      <!-- {{ nlsMap }} -->
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <!-- 设备号 -->
        <u-form-item :label="nlsMap.lbMachineName" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <!-- 设备描述 -->
        <u-form-item :label="nlsMap.lbmachineDescription" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDescription" border="none"></u--input>
        </u-form-item>

        <!-- 安装点 -->
        <u-form-item :label="nlsMap.lbmaterialLocationName" required borderBottom labelWidth="100">
          <u--input v-model="model.materialLocationName" border="none" placeholder="扫描设备上的材料安装点条码" @focus="focusEvent('materialLocationName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('materialLocationName')"></view>
        </u-form-item>

        <!-- 工序 -->
        <u-form-item :label="nlsMap.lbProcessOperationName" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.processOperationName }}
          </view>
        </u-form-item>

        <!-- 工单号 -->
        <u-form-item :label="nlsMap.lbproductOrderName" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectProductOrderName('productOrderName')">
            <view>{{ model.productOrderName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <!-- 产品编码 -->
        <u-form-item :label="nlsMap.lbproductSpecName" borderBottom required labelWidth="100">
          <!-- <view class="w100x flex right" @click="selectProductSpecName('productSpecName')">
            <view>{{ model.productSpecName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view> -->
          <view class="w100x flex right">
            <view>{{ model.productSpecName }}</view>
          </view>
        </u-form-item>

        <!-- BOM -->
        <u-form-item :label="nlsMap.lbBom" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view>{{ model.bomID }}</view>
          </view>
        </u-form-item>

        <!-- 标签条码 -->
        <u-form-item :label="nlsMap.lbConsumableName" required borderBottom labelWidth="100">
          <u--input v-model="model.consumableName" border="none" :focus="focusObj.consumableName" placeholder="扫描材料最小包装条码" @focus="focusEvent('consumableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>

        <!-- 材料 -->
        <u-form-item :label="nlsMap.lbconsumableSpec" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.consumableSpec }}
          </view>
        </u-form-item>

        <!-- 数量 -->
        <!-- <u-form-item :label="nlsMap.lbquantity" required borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.quantity }}
          </view>
        </u-form-item> -->
        <!-- 上料数量 -->
        <u-form-item :label="nlsMap.lbloadingQuantity" required borderBottom labelWidth="100">
          <view class="w100x flex right">
            <!-- {{ model.loadingQuantity }} -->
            <u--input v-model="model.loadingQuantity" border="none" placeholder="请输入上料数量"></u--input>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>
    <view class="btnContainer2">
      <view @click="submit">{{ nlsMap.lbfeeding }}</view>
      <view @click="gotoQuery">{{ nlsMap.lbviewDetail }}</view>
    </view>
  </view>
</template>

<script>
import useNls from "@/mixins/useNls";
export default {
  mixins: [useNls],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeConsumableName = this.$debounce(this.changeConsumableName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        lbMachineName: '设备号',
        lbmachineDescription: '设备描述',
        lbmaterialLocationName: '安装点',
        lbProcessOperationName: '工序',
        lbproductOrderName: '工单号',
        lbproductSpecName: '产品编码',
        lbBom: 'BOM',
        lbConsumableName: '标签条码',
        lbconsumableSpec: '材料',
        lbquantity: '数量',
        lbloadingQuantity: '上料数量',
        lbfeeding: '上料',
        lbviewDetail: '查看已上料明细',

        // LoadedDetails
        lbSumTitle: '材料上料-已上料明细',
      },

      rulesTip: {
        machineName: '设备编号不能为空',
        productOrderName: '工单编码不能为空',
        loadingQuantity: '上料数量不能为空',
        materialLocationName: '安装点不能为空',
        consumableName: '标签条码不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        durableName: false,
        materialPosition: false
      },
      show: false,
      content: '',
      modelKey: '',
      productSpecNameList: [],
      productOrderNameList: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productOrderName) {
        obj = this.columns.find(item => item.productOrderName === this.model.productOrderName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.consumableName': {
      handler(res) {
        this.changeConsumableName(res)
      }
    },
    'model.materialLocationName': {
      handler(res) {
        this.changeMaterialLocationName(res)
      }
    }
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
  },
  methods: {
    async changeMaterialLocationName(value) {
      if (!value || value == 'NA') return
      let params = {
        portName: value
      }
      try {
        let res = await this.$service.MaterialLoading.getMachineNameByPortName(params)
        if (res.datas.length > 0) {
          this.model.machineName = res.datas[0].machineName
        }
      } catch (error) {
        this.model.materialLocationName = null
      }
    },
    gotoQuery() {
      // uni.navigateTo({
      //   url: `${item.url}?&pageParams=${encodeURIComponent(JSON.stringify(pageParams))}`,
      // })

      uni.navigateTo({
        url: `/pages/MaterialLoading/LoadingMaterial/modules/LoadedDetails?machineName=${this.model.machineName}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        bomID: null, //	BOM号	string	
        bomVersion: null, //	BOM版本	string	
        consumableName: null, //标签条码	string	
        consumableSpec: null, //	材料	string	
        consumableSpecDescription: null, //材料描述	string	
        consumableSpecName: null, //	材料编码	string	
        loadingQuantity: null, //上料数量	number	
        loadingTime: null, //上料时间	string(date-time)	
        machineDescription: null, //	设备描述	string	
        machineName: null, //	设备名	string	
        materialLocationName: 'NA', //安装点	string	
        processOperationName: null, //工序	string	
        productOrderName: null, //工单号	string	
        productSpecDesc: null, //产品描述	string	
        productSpecName: null, //产品编码	string	
        // quantity: null, //	数量
      }
    },
    // 选择
    selectFirm(e) {
      if (this.modelKey == 'productOrderName') {
        // this.$set(this.model, this.selectType, e.value[0].value)
        this.model.productOrderName = e.value[0].value
        this.model.productSpecName = e.value[0].productSpecName
        this.model.bomID = e.value[0].bomIdAndVersion
        this.select = false
        // const params = {
        //   productOrderName: this.model.productOrderName
        // }
        // this.$service.MaterialLoading.getBomData(params).then(res => {
        //   if (res) {
        //     this.model.bomID = res.datas[0]
        //   }
        // })
      } else {
        // this.model.productSpecName = e.value[0]
        // this.select = false
        // const params = {
        //   productSpecName: this.model.productSpecName
        // }
        // this.$service.MaterialLoading.getProductOrderList(params).then(res => {
        //   if (res) {
        //     this.productOrderNameList = res.datas
        //   }
        // })
      }
    },
    // 上料开始
    submit() {
      console.log('this.model,', this.model);
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.MaterialLoading.ConsumableLoading(params).then(res => {
        this.$Toast('上料成功！')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {

        let res = await this.$service.MaterialLoading.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true

          let { machineName, machineDescription, processOperationName } = res.datas[0]
          this.model.machineDescription = machineDescription
          this.model.machineName = machineName
          this.model.processOperationName = processOperationName


          // 获取产品列表
          let data = {
            machineName
          }
          let resData = await this.$service.MaterialLoading.getProductSpecList(data)
          this.productOrderNameList = resData.datas.map((item, index) => ({
            value: item.productOrderName,
            label: item.productOrderName,
            bomIdAndVersion: item.bomIdAndVersion,
            productSpecName: item.productSpecName,
          }))
          // resData.datas.forEach(item => {
          //   item.labelText = item
          // });
          // this.productSpecNameList = resData.datas
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 标签条码 */
    async changeConsumableName(value) {
      if (!value) return
      let params = {
        consumableName: value,
        productOrderName: this.model.productOrderName,
        processOperationName: this.model.processOperationName,
        machineName: this.model.machineName,
        materialLocationName: this.model.materialLocationName
      }
      this.$service.MaterialLoading.getConsumableDataCheck(params).then(res => {
        if (res.success && res.message) {
          this.show = true
          this.content = res.message
        } else {
          if (res.datas.length > 0) {
            // let { consumableSpec, quantity } = res.datas[0]
            // this.model.consumableSpec = consumableSpec
            // this.model.quantity = quantity
            let { consumableSpec, quantity } = res.datas[0]
            this.model.consumableSpec = consumableSpec
            this.model.loadingQuantity = quantity
          }
        }
      }).catch(res => {
        this.model.consumableName = ''
      })
    },

    // 选择产品编码
    selectProductSpecName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.productSpecNameList
    },

    // 选择工单编号
    selectProductOrderName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.productOrderNameList
    },

    async confirm() {
      this.show = false
      let params = {
        consumableName: this.model.consumableName
      }
      this.$service.MaterialLoading.getConsumableData(params).then(res => {
        if (res.datas.length > 0) {
          // let { consumableSpec, quantity } = res.datas[0]
          // this.model.consumableSpec = consumableSpec
          // this.model.quantity = quantity
          let { consumableSpec, quantity } = res.datas[0]
          this.model.consumableSpec = consumableSpec
          this.model.loadingQuantity = quantity
        }
      })
    },

    cancel() {
      this.show = false
      this.model.consumableName = ''
      this.model.consumableSpec = ''
      // this.model.quantity = ''
      this.model.loadingQuantity = ''
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'OP090_01'
          break;
        case 'consumableName':
          this.model.consumableName = 'TN20240428000001'
          break;
        case 'materialLocationName':
          this.model.materialLocationName = 'P02'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'consumableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'materialLocationName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
