<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="浆料批次查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>配方号:</view>
        <view class="ml6">{{ consumableSpecName }}</view>
      </view>
      <view class="fs16 c_00b17b mt10">浆料批次信息</view>
    </view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>浆料出货牌号</view>
              <view>{{ ele.consumableName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>储蓄罐</view>
              <view>{{ ele.carrierName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>完成时间</view>
              <view>{{ ele.createTime ? moment(model.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>数量(kg)</view>
              <view class="flex hcenter">
                <view>{{ ele.plannedOutPut }}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData';
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      consumableSpecName: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.consumableSpecName = e && e.consumableSpecName
    this.getData()
  },
  methods: {
    moment,
    getData() {
      let params = {
        consumableSpecName: this.consumableSpecName,
      }
      this.$service.Size.getConsumableByMachineName(params).then((res) => {
        if (res && res.success) {
          this.model = res.datas
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>