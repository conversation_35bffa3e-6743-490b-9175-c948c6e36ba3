<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="已取样品信息" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10 mb10">
      <view class="myContainer ml10 mr10 mb10">
        <u--form labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="首检任务编码:" required labelWidth="100">
            <view class="w100x flex right">
              {{ taskNo }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10">已取样品信息</view>
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="list.length > 0">
          <view v-for="(ele, index) in list" :key="index">
            <view class="flex between ma10 mb10 bb_eee hcenter">
              <view class="label_circle mr20">{{index + 1}}</view>
              <view class="flex1 mr10 pb10">
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">样品条码:</view>
                  <view>{{ ele.lotName }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">取样时间:</view>
                  <view>{{ ele.createTime }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验进度:</view>
                  <view>{{ ele.lotStateDictText || '-' }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验结果:</view>
                  <view>{{ ele.inspectResult || '-' }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验完成时间:</view>
                  <view>{{ ele.inspectionFinishime || '-' }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
    <!-- <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker> -->
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      form: {
      },
      taskNo: ''
    };
  },
  computed: {
    list: {
      get() {
        return this.$store.state.FinshSampleList;
      },
      set(value) {
        this.$store.commit('changeFinshSampleList', value);
      }
    },
  },
  onLoad(options) {
    let { taskNo } = options
    this.taskNo = taskNo
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return item.reasonCodeType + '/' + item.typeDesc
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType() {
      this.columns = this.GetReasonCodeTypeList
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.saveNo = true
      this.select = false
    },
    initModel() {
      this.form = {
        machineName: null, // 设备号
        machineDescription: null, // 搅拌机描述
        quantity: null // 数量
      }
    },
    focusEvent(type) {
      this.form[type] = ''
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.MaterialLoading.UnLoadingQueryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, machineDescription } = res.datas[0]
          this.form.machineDescription = machineDescription
          this.form.machineName = machineName
          this.getData(value)
        } else {
          this.$Toast('设备不存在！')
        }
      } catch (error) {
        this.form.machineName = null
      }
    },
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      return resArr
    },
    getData(machineName) {
      let params = {
        machineName,
      }
      this.$service.MaterialLoading.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            res.datas.forEach(item => item.qty = 0)
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          } else {
            this.simpleTrackProduct = []
          }
        }
      })
    },
    submit(item) {
      if (item.qty == '') return this.$Toast('数量不能为空！')
      let params = {
        ...item
      }
      params.quantity = params.qty
      this.$service.MaterialLoading.ConsumableUnLoading(params).then((res) => {
        if (res && res.success) {
          this.$Toast('卸料成功！')
          setTimeout(() => {
            this.getData(this.form.machineName)
          }, 800);
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.form.machineName = 'YN-GZ-WG-ZP-401'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.form, key, res.result)
          },
        })
      }
      // #endif
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
  .label_circle {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    border: 1px solid #000;
    text-align: center;
    line-height: 70rpx;
    font-size: 24px;
  }
}
</style>