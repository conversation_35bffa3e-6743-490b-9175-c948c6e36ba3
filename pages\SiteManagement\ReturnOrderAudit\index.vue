<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>

    <view class="myContainer ma10">
      
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>退库单号</view>
            <view>{{ ele.returnOrderNo }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>仓库</view>
            <view>{{ $utils.optionShowConfig(ele.stockCode, ele.stockText) }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>库位</view>
            <view> {{ $utils.optionShowConfig(ele.stockLocationCode, ele.stockLocationText) }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view> 退库类型</view>
            <view> {{ ele.trayReturnText }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>创建人</view>
            <view>{{ ele.createUser }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>创建时间</view>
            <view> {{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
          </view>
          <view>
            <u-button type="success" text="详情" @click="summit(ele)"></u-button>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>

        <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import moment from 'moment'
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin, useNls],
  components: {
    NoData,
  },
  data() {
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {


        detailTitle: '退库审核',
      },
      model: [],
      // model: Array.from({ length: 20 }, (v, i) => i),
      dicts: {
      },
    };
  },

  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.initSearchModel()
    this.getData()
  },
  methods: {
    moment,
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
        console.log(this.dicts, '88')
      })
    },
    summit(ele) {
      uni.navigateTo({
        url: `/pages/SiteManagement/ReturnOrderAudit/detail?returnOrderNo=${ele.returnOrderNo}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))} `,
      })
    },
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },
    returnRefresh() {
      this.getData(true, true)
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))

      this.$service.ReturnOrderController.ReturnOrder_ListReturnOrder().then((res) => {
        if (res && res.success) {
          // this.model = res.datas
          // this.status = 'nomore'
          this.model = res.datas
          
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>