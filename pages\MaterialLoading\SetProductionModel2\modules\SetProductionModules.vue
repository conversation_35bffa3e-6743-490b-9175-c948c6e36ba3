<template>
  <view class="bc_fff listPage">
    <u-navbar title="设备换型设置查看" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>设备:</view>
        <view class="ml6">{{ machineName ? machineName + '/' + machineSpecDesc : '' }}</view>
      </view>
      <view class="fs16 c_00b17b">当前生产产品列表</view>
    </view>
    <view class="listContainer mt10 ml10 mr10">
      <scroll-view class="h100x" refresher-enabled scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-for="(ele, index) in model" :key="index">
          <!-- <view class="mb4 ml4 fs14">
            <span>{{ ele.eventTime ? moment(ele.eventTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
            <span class="ml10">{{ ele.eventUser }}</span>
          </view> -->
          <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed">
            <view class="flex between h40 hcenter c_999">
              <view>工序</view>
              <view>{{ ele.processOperationName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>产品编码</view>
              <view>{{ ele.productSpecName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>工艺卡</view>
              <view>{{ ele.cardName ? ele.cardName + '/' + ele.cardRevision : '' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>操作时间</view>
              <view>{{ ele.eventTime ? moment(ele.eventTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>操作人</view>
              <view>{{ ele.eventUser }}</view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      machineSpecDesc: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.machineSpecDesc = e && e.machineSpecDesc
    this.getData()
  },
  methods: {
    moment,
    getData() {
      let params = {
        machineName: this.machineName,
      }
      this.$service.SetProductionModel.GetMachineTaskList(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.model = res.datas
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>