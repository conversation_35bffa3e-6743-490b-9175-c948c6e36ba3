<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="匀浆产出确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请填写" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="浆料种类" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ productRequestInfo.spliceTypeValue }}
          </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="工单" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ productRequestInfo.productRequestName }}
          </view>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(productRequestInfo.productSpecName, productRequestInfo.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="浆料批次" borderBottom labelWidth="100">
          <u--input readonly v-model="productRequestInfo.lotName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="开始时间" borderBottom labelWidth="100">
          <u--input readonly v-model="productRequestInfo.lotCreateTime" border="none"></u--input>
        </u-form-item>

        <u-form-item label="产出数量(设备)" borderBottom labelWidth="150">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input type="number" border="none" v-model="model.device_productQuantity" readonly></u--input>kg</view>
            <view class="flex hcenter flex1 ml10"> <u--input type="number" border="none" v-model="model.device_lotWeight" readonly></u--input>ea</view>
          </view>
        </u-form-item>

        <u-form-item label="产出数量(人工)" borderBottom required labelWidth="150">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input type="number" border="bottom" v-model="model.productQuantity" @input="inputChange($event, 'productQuantity')"></u--input>kg </view>
            <view class="flex hcenter flex1 ml10"> <u--input type="number" border="bottom" v-model="model.lotWeight" @input="intChange($event,'lotWeight')" :readonly="ratioFlag"></u--input>ea</view>
          </view>
        </u-form-item>

        <u-form-item label="固含量" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.param001" :readonly="param001Flag" border="bottom" type="number" @input="inputChange($event, 'param001')"></u--input>
              <view>%</view>
            </view>
          </view>
        </u-form-item>
        <u-form-item label="粘度" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.param002" :readonly="param002Flag" border="bottom" type="number" @input="inputChange($event, 'param002')"></u--input>
              <view>MPA.s</view>
            </view>
          </view>
        </u-form-item>
        <u-form-item label="细度" required labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.param003" :readonly="param003Flag" border="bottom" type="number" @input="inputChange($event, 'param003')"></u--input>
              <view>um</view>
            </view>
          </view>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer2">
      <view @click="readData">读取设备数据</view>
      <view @click="submit">确认</view>
    </view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID, LOCALHOST_PRINT } from '@/utils/common/evtName.js'
export default {
  name: 'homogenateConfirmIndex',
  mixins: [RedScan],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000) // 消抖
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productQuantity: '生产数量(人工)不能为空',
        lotWeight: '电芯计量数量(人工)不能为空',
        param001: '固含量不能为空',
        param002: '粘度不能为空',
        param003: '细度不能为空',
      },
      param001Flag: false,
      param002Flag: false,
      param003Flag: false,
      model: {},
      productRequestInfo: {},
      ratio: null,  // 单位转换
      ratioFlag: false,
      machineNameFlag: false, // 正确设备编号标识
      checkMachineOutput: false, // 是否需要校验IOT设备上报产出数量
      //true->校验产出数量 是否为空或小于等于0
      //<=0  提示错误信息
      //>0   不用提示
      //false就不校验产出数量
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述
        productRequestName: null, //工单
        productQuantity: null, // 生产数量 kg
        lotWeight: null, //电芯计量数量 ea
        device_productQuantity: null, // 设备生产数量 kg
        device_lotWeight: null, // 设备电芯计量数量 ea
        productQuantityUnit: 'kg',
        lotWeightUnit: 'ea',
        param001: null, //固含量
        param002: null, //粘度
        param003: null, //细度
      }
      this.productRequestInfo = {}
      this.machineNameFlag = false
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.model.machineDesc = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null
      this.model.productRequestName = null

      this.model.device_productQuantity = null
      this.model.device_lotWeight = null
      this.model.param001 = null
      this.model.param002 = null
      this.model.param003 = null
      this.param001Flag = false
      this.param002Flag = false
      this.param003Flag = false


      this.model.productQuantity = null
      this.model.lotWeight = null
      this.ratio = null
      this.ratioFlag = false

      this.productRequestInfo = {}

      let params = {
        operateNo: '10', // 后台区分固定字段
        machineName: value,
      }

      try {
        let res = await this.$service.homogenate.trackOut(params)
        this.machineNameFlag = true
        this.model.machineDesc = res.data.machineDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc
        this.model.productRequestName = res.data.productRequestName

        this.productRequestInfo = res.data.trackOutProductRequest
        this.checkMachineOutput = res.data.checkMachineOutput

        this.ratio = res.data.unitconversionDto && res.data.unitconversionDto.ratio
        this.ratioFlag = res.data.unitconversionDto && !!res.data.unitconversionDto.ratio || false
      } catch (error) {
        this.model.machineName = null
      }

    },
    dataHandle(orginData, ratioData) {
      if (this.model[orginData] || this.model[orginData] == '0') {
        this.model[ratioData] = parseInt(this.$utils.calculate_mul(this.model[orginData], this.ratio))
      } else {
        this.model[ratioData] = null
      }
    },

    intChange(e, type) {
      if(e) {
        let str = e &&  e + ""
        let result = str && (str.match(/^\d*/g)[0])
        this.$nextTick(() => {
          this.$set(this.model, type, result)
        })
      }
    },
    
    inputChange(e, type) {
      e = e && (e.match(/^\d*(\.?\d{0,2})/g)[0])
      if (this.ratioFlag) {
        if (type === 'productQuantity') {
          this.dataHandle('productQuantity', 'lotWeight')
        }
      }
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },

    async submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      // checkMachineOutput: false, // 是否需要校验IOT设备上报产出数量
      //true->校验产出数量 是否为空或小于等于0
      //<=0  提示错误信息
      //>0   不用提示
      //false就不校验产出数量
      if (this.checkMachineOutput) {
        if (Number(this.model.device_productQuantity) <= 0) {
          this.$Toast(`未采集到设备[${this.model.machineName}]的产出数量`)
          return
        }
      }

      let randomNum = '';
      for (var i = 0; i < 3; i++) {
        randomNum += Math.floor(Math.random() * 10);
      }
      let time = new Date().getTime()
      let printTimeStr = this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
      let timeKey = `${this.model.machineName}_${time}_${randomNum}`

      let dto = {
        actionId: 'TrackOutForStock', // 消息名称
        menuId: 'TrackOutForStock', // 消息名称
        requestId: timeKey, // 时间戳
        reqMap: {
          userId: this.$getLocal(USER_ID), // 登陆人
          DS_TIBCO_REPLY: [],
          // 暂定写死
          DS_TIBCO_MAIN: [
            {
              DEST_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_VALUE: 'GET_RV_XMLDATA',
              MAX_TIME: '15',
            },
          ],
          langCd: '',
          DS_TIBCO_MSG: [
            {
              COMMAND_ID: 'TrackOutForStock', // 消息名称
              FILE_ID: 'TrackOutForStock', // 消息名称
              MESSAGE_NAME: 'TrackOutForStock', // 消息名称
              TRANSACTION_ID: timeKey, // 时间戳
              EVENT_USER: this.$getLocal(USER_ID), // 登陆人
              EVENT_COMMENT: '', // 功能名称
              REPLYSUBJECT_NAME: 'TrackOutForStock', // 界面名称
              TERMINAL: 'PDA', // PDA请求，WEB请求
              // FACTORYNAME:'',
              MACHINENAME: this.model.machineName,
              PROCESSOPERATIONNAME: this.model.processOperationName,
              LOTNAME: this.productRequestInfo.lotName,
              PRODUCTQUANTITY: this.model.productQuantity,
              PRODUCTQUANTITYUNIT: this.model.productQuantityUnit,
              LOTWEIGHT: this.model.lotWeight,
              LOTWEIGHTUNIT: this.model.lotWeightUnit,
              PARAM001: this.model.param001,
              PARAM002: this.model.param002,
              PARAM003: this.model.param003
            },
          ],
          DS_TIBCO_REPEAT: [],
        },
      }

      try {
        let res = await this.$service.common.commonExec(dto)
        if (res.success) {
          this.$Toast('操作成功')
        }
        // let params = {
        //   machineName: 'C1Z001001',
        //   operateNo: '10',
        //   printTimeKey: 'C1Z001001_1661303992763'
        // }
        let params = {
          machineName: this.model.machineName,
          operateNo: '10',
          printTimeKey: timeKey,
          printTimeStr: printTimeStr,
          userId: this.$getLocal(USER_ID)
        }
        let pres = await this.$service.common.printLot(params, { showLoading: false })
        this.initModel()
        for (let i = 0; i < pres.data.basicLotLabelList.length; i++) {
          const ele = pres.data.basicLotLabelList[i]
          setTimeout(() => {
            let option = {
              ip: pres.data.aoiPrintIp,
              port: pres.data.aoiPrintPort,
              printName: pres.data.printName,
              priParameterntKey: [
                { type: "", name: "processOperationDesc", value: ele.processOperationDesc || '', required: false }, // 工序名称
                { type: "", name: "dueDate", value: ele.dueDate || '', required: false },
                { type: "", name: "releaseTime", value: ele.releaseTime || '', required: false },
                { type: "", name: "machineName", value: ele.machineName || '', required: false },
                { type: "", name: "productRequestName", value: ele.productRequestName || '', required: false },
                { type: "", name: "productSpecDesc", value: ele.productSpecDesc || '', required: false },// 型号
                { type: "", name: "lotName", value: ele.lotName || '', required: false }, // '二维码
                { type: "", name: "specification", value: ele.specification || '', required: false }, // '规格
                { type: "", name: "lotWeight", value: ele.lotWeight || '', required: false },
                { type: "", name: "lotWeightUnit", value: ele.lotWeightUnit || '', required: false },
                { type: "", name: "productQuantity", value: ele.productQuantity || '', required: false },
                { type: "", name: "productQuantityUnit", value: ele.productQuantityUnit || '', required: false },
                { type: "", name: "createUserValue", value: ele.createUserValue || '', required: false },
                { type: "", name: "releaseUserValue", value: ele.releaseUserValue || '', required: false },
                { type: "", name: "param001", value: ele.param001 || '', required: false },
                { type: "", name: "param002", value: ele.param002 || '', required: false },
                { type: "", name: "param003", value: ele.param003 || '', required: false },
              ]
            }
            this.printPackage(option)
          }, 1000 * i)
        }
      } catch (error) {
        console.log('error', error);
      }
    },

    printPackage(options = {}) {
      let obj = {
        ReportType: "gridreport",     /*报表类型 gridreport fastreport reportmachine 为空 将默认为gridreport  */
        ReportName: "yunJiang.grf",     /*报表文件名 条形码 */
        PrinterName: `${options.printName}`,      /*可选。指定打印机，为空的话 使用默认打印机, 请在 控制面板 -> 设备和打印机 中查看您的打印机的名称 */
        Parameter: JSON.stringify(options.priParameterntKey),
      }
      if (!options.ip || !options.port) {
        return
      }
      let printerUrl = `http://${options.ip}:${options.port}/printreport`
      if (LOCALHOST_PRINT) {
        printerUrl = `http://${LOCALHOST_PRINT}:${options.port}/printreport`
      }
      uni.request({
        url: printerUrl,
        data: obj,
        header: {
          'content-type': 'application/x-www-form-urlencoded', //自定义请求头信息
        },
        method: 'POST',
        success: (res) => { },
      })
    },
    scan(key) {
      // #ifdef H5
      this.model.machineName = 'C1Z001001'
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
    async readData() {
      if (!this.model.machineName) {
        this.$Toast('设备编号不能为空')
        return
      }
      if (!this.machineNameFlag) {
        return this.$Toast('请重新输入或扫描设备编号')
      }
      this.model.device_productQuantity = null
      this.model.device_lotWeight = null
      let params = {
        machineName: this.model.machineName,
        operateNo: '10',
      }
      let res = await this.$service.common.queryMachineOutput(params)
      if (res.data && res.data[0]) {
        this.model.device_productQuantity = res.data[0].machineProductQuantity
        this.model.device_lotWeight = res.data[0].machineLotWeight
        this.model.productQuantity = res.data[0].machineProductQuantity
        this.model.lotWeight = res.data[0].machineLotWeight

        this.model.param001 = res.data[0].param001
        this.model.param002 = res.data[0].param002
        this.model.param003 = res.data[0].param003
        this.param001Flag = !!res.data[0].param001
        this.param002Flag = !!res.data[0].param002
        this.param003Flag = !!res.data[0].param003
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>