<template>
  <view class="bc_fff listPage">
    <u-navbar title="工装安装-已安装明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ma10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index) in simpleTrackProduct" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>工装编码:</view>
              <view>{{ ele.durableName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>工装描述:</view>
              <view>{{ ele.durableNameDesc }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>安装位置:</view>
              <view>{{ ele.portName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>额度使用寿命:</view>
              <view>{{ ele.usedCountTotal }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>警戒使用寿命:</view>
              <view>{{ ele.warningUsedCount }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>已使用寿命:</view>
              <view>{{ ele.usedCount }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>剩余使用寿命:</view>
              <view>{{ ele.residueUsedCount }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>安装时间:</view>
              <view>{{ ele.eventTime }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>操作人员:</view>
              <view>{{ ele.eventUser }}</view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      simpleTrackProduct: []
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.getData()
  },
  methods: {
    getData(clearOldData = false, refresh = false) {
      let params = {
        machineName: this.machineName,
      }
      this.$service.ToolingAndInspection.getDurableListByMachineName(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.simpleTrackProduct = res.datas
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>