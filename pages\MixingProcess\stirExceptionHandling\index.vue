<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="搅拌工序-异常处理" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="标签条码" borderBottom required labelWidth="100">
          <u--input v-model="model.bqtm" border="none" focus placeholder="请扫描标签条码" @focus="focusEvent('bqtm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('bqtm')"></view>
        </u-form-item>

        <u-form-item label="搅拌机" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.jbj }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item>

        <u-form-item label="工步进度" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gbjd }} </view>
        </u-form-item>

        <!-- <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.scgd }} </view>
        </u-form-item>

        <u-form-item label="品种名" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.pzm }} </view>
        </u-form-item> -->

        <u-form-item label="操作选择" required  borderBottom labelWidth="100">
          <!-- <view class="w100x flex right"> {{ model.durableType }} </view> -->
          <view class="w100x flex" :style="'flex-direction: row-reverse'">
            <u-radio-group
                v-model="model.czfs"
                placement="column"
                style="width: 100%;"
              >
              <view style="display: flex;flex-direction: row-reverse">
                <u-radio
                  v-for="(item, index) in radiolist1"
                  :key="index"
                  :customStyle="{ marginRight: '10rpx' }"
                  :label="item.name"
                  :name="item.name"
                >
                </u-radio>
              </view>
            </u-radio-group>
          </view>
        </u-form-item>

        <!-- <u-form-item label="不良原因" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType('blyy')">
            <view>{{ model.blyy }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item> -->
        <template v-if="model.czfs == '投料'">
          <u-form-item label="投料" borderBottom required labelWidth="100">
            <u--input v-model="model.wlbqtm" border="none" focus placeholder="请扫描物料标签条码" @focus="focusEvent('wlbqtm')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('wlbqtm')"></view>
          </u-form-item>
          <u-form-item label="投入重量(kg)" borderBottom required labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w100x hcenter">
                <u--input type="number" v-model="model.trzl" border="none" placeholder="请输入投入重量"></u--input>
              </view>
            </view>
          </u-form-item>
        </template>
        <template v-else-if="model.czfs == '搅拌'">
          <u-form-item label="搅拌开始时间" borderBottom labelWidth="150">
            <view class="w100x flex right"> {{ model.jbkssj }} </view>
          </u-form-item>
          <u-form-item label="搅拌时长(min)" borderBottom labelWidth="150">
            <view class="w100x flex right"> {{ model.jbsc }} </view>
          </u-form-item>
        </template>
        <template v-else-if="model.czfs == '产出确认'">
          <u-form-item label="产出数量(kg)" borderBottom required labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w100x hcenter">
                <u--input type="number" v-model="model.ccsl" border="none" placeholder="请输入产出数量"></u--input>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="浆料粘度(Mpa.s)" borderBottom required labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w100x hcenter">
                <u--input type="number" v-model="model.jlzd" border="none" placeholder="请输入浆料粘度"></u--input>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="颗粒度(μm)" borderBottom required labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w100x hcenter">
                <u--input type="number" v-model="model.kld" border="none" placeholder="请输入颗粒度"></u--input>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="固含量(%)" borderBottom required labelWidth="150">
            <view class="w100x flex right">
              <view class="flex w100x hcenter">
                <u--input type="number" v-model="model.ghl" border="none" placeholder="请输入固含量"></u--input>
              </view>
            </view>
          </u-form-item>
        </template>
        <u-form-item label="备注" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.bz" border="none" placeholder="请输入备注"></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view v-if="model.czfs == '投料'" class="btnContainer" @click="submit(1)">投料</view>
    <view v-else-if="model.czfs == '搅拌'" class="flex jb_box">
      <view :class="{ 'btnContainer_dis': startDisabeld }" class="btnContainer" @click="submit(2)">搅拌开始</view>
      <view :class="{ 'btnContainer_dis': endDisabeld }" class="btnContainer" @click="submit(3)">搅拌结束</view>
    </view>
    <view v-else class="btnContainer" @click="submit(4)">产出确认</view>
  </view>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeDurableName2 = this.$debounce(this.changeDurableName2, 1000)
    // this.changeLotName = this.$debounce(this.changeLotName, 1000)
    // this.changeBz = this.$debounce(this.changeBz, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        bqtm: '',
        jbj: '',
        gx: '',
        gbjd: '',
        wlbqtm: '',
        trzl: '',
        bz: '',
        czfs: '投料',
        jbkssj: '',
        jbsc: '',
        ccsl: '',
        jlzd: '',
        kld: '',
        ghl: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      selectType: '',
      searchData: [],
      oldGxType: '',
      tipContent: '',
      radiolist1: [
        {
          name: '产出确认'
        },
        {
          name: '搅拌'
        },
        {
          name: '投料'
        }
      ],
      zjpdjObj: {},
      fjpdjObj: {},
      startDisabeld: true,
      endDisabeld: true,
    }
  },
  watch: {
    'model.bqtm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.wlbqtm': {
      handler(val) {
        this.changeDurableName2(val)
      }
    },
  },
  onLoad() {
    this.list = []
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        processOperationName: this.model.gxType
      }
      this.$service.QualityControl.getReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            label: item.reasonCode + '/' + item.reasonCodeDesc,
            value: item.reasonCode,
            reasonCodeDesc: item.reasonCodeDesc,
            reasonCodeType: item.reasonCodeType
          }
          // return item.reasonCode
        })
        // this.GetReasonCodeTypeList.forEach(item => {
        //   item = item.reasonCodeType + '/' + item.typeDesc
        // })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if(type == 'blyy') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if(this.selectType == 'blyy') {
        // 选完不良原因清空备注
        this.model.bz = ''
        this.model.blyy = e.value[0].label
        this.model.blyyType = e.value[0].value
        this.model.blyyText = e.value[0].reasonCodeDesc
        this.model.ngReasonCodeType = e.value[0].reasonCodeType
      }
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/QualityControl/SemiDetermination/modules/SemiDeterminationList`
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        bqtm: '',
        jbj: '',
        gx: '',
        gbjd: '',
        wlbqtm: '',
        trzl: '',
        bz: '',
        czfs: '投料',
        jbkssj: '',
        jbsc: '',
        ccsl: '',
        jlzd: '',
        kld: '',
        ghl: ''
      }
    },
    submit(type) {
      if(!this.model.bqtm) {
        this.$Toast('请输入标签条码！')
        return
      }
      if(!this.model.bz) {
        this.$Toast('请输入备注！')
        return
      }
      if(type == 1) {
        if(!this.model.wlbqtm) {
          this.$Toast('请输入投料条码！')
          return
        }
        if(!this.model.trzl) {
          this.$Toast('请输入投入重量！')
          return
        }
        let params = {
          lotName: this.model.bqtm,
          machineName: this.model.machineName,
          feedingConsumableName: this.model.wlbqtm,
          feedingConsumableWeight: Number(this.model.trzl),
          remark: this.model.bz
        }
        this.$service.stirExceptionHandling.feedingMaterialForNg(params).then(res => {
          this.$Toast('投料提交成功！')
          // 提交成功初始化
          this.initModel()
        })
      } else if(type == 2) {
        let params = {
          lotName: this.model.bqtm,
          machineName: this.model.machineName,
          mixingStartTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          remark: this.model.bz
        }
        this.$service.stirExceptionHandling.mixingStartForNg(params).then(res => {
          this.$Toast('搅拌开始提交成功！')
          // 提交成功初始化
          this.initModel()
        })
      } else if(type == 3) {
        let params = {
          lotName: this.model.bqtm,
          machineName: this.model.machineName,
          mixingEndTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          remark: this.model.bz
        }
        this.$service.stirExceptionHandling.mixingEndForNg(params).then(res => {
          this.$Toast('搅拌结束提交成功！')
          // 提交成功初始化
          this.initModel()
        })
      } else if(type == 4) {
        let params = {
          lotName: this.model.bqtm,
          machineName: this.model.machineName,
          remark: this.model.bz,
          outPutQuantity: this.model.ccsl,
          viscosity: this.model.jlzd,
          particleSize: this.model.kld,
          solidContent: this.model.ghl,
          ngFlag: 'Y'
        }
        this.$service.stirExceptionHandling.endMixing(params).then(res => {
          this.$Toast('产出提交成功！')
          // 提交成功初始化
          this.initModel()
        })
      }
    },
    getRemainingTime(val) {
      // let endTime = new Date(moment(val).valueOf())
      // let min = endTime.getMinutes();
      // endTime.setMinutes(min + Number(this.model.processStandard));
      // let stime = moment(new Date()).valueOf();
      // let etime = moment(endTime).valueOf();
      let usedTime = val;  //两个时间戳相差的毫秒数
      let hours = parseInt((usedTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      let minutes = parseInt((usedTime % (1000 * 60 * 60)) / (1000 * 60));
      // this.model.jbsc = Number(hours) * 60 + Number(minutes)
      // console.log(this.remainingTime, 'remainingTimeremainingTime');
      return Number(hours) * 60 + Number(minutes)
    },
    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 在制品条码 */
    async changeDurableName(value) {
      console.log('value', value);
      if (!value) return
      this.columns = []
      let params = {
        lotName: value
      }
      try {
        let res = await this.$service.stirExceptionHandling.getLotInfo(params)
        this.searchData = res.datas ? res.datas : []
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...res.datas[0],
            bqtm: data.lotName,
            jbj: data.machineDescription,
            gx: data.processOperationDesc,
            gbjd: data.currentWorkStepName,
            wlbqtm: '',
            trzl: '',
            bz: '',
            czfs: '投料',
            jbkssj: '',
            jbsc: '',
            ccsl: '',
            jlzd: '',
            kld: '',
            ghl: ''
            // bqtm: data
          }
          // 获取搅拌信息
          this.getMixingForNg()
        } else {
          this.model.bqtm = ''
          this.$Toast('条码不存在！')
        }
      } catch (error) {
        console.log(error);
        this.initModel()
      }
    },
    /* 物料条码 */
    async changeDurableName2(value) {
      console.log('value', value);
      if (!value) return
      this.columns = []
      let params = {
        consumableName: value
      }
      try {
        let res = await this.$service.stirExceptionHandling.getConsumableInfo(params)
        this.searchData = res.datas ? res.datas : []
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...this.model,
            wlbqtm: data.consumableName,
            trzl: '',
            bz: ''
          }
          // 获取搅拌信息
        } else {
          this.model.wlbqtm = ''
          this.$Toast('物料标签条码不存在！')
        }
      } catch (error) {
        console.log(error);
        this.model.wlbqtm = ''
        // this.initModel()
      }
    },
    /* 搅拌信息 */
    async getMixingForNg() {
      let params = {
        lotName: this.model.bqtm
      }
      try {
        let res = await this.$service.stirExceptionHandling.mixingForNg(params)
        this.searchData = res.datas ? res.datas : []
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          let num = 0
          if(!data.startTime) {
            this.startDisabeld = false
            this.endDisabeld = true
          } else {
            this.endDisabeld = false
            this.startDisabeld = true
            num = new Date().getTime() - new Date(data.startTime).getTime()
          }
          console.log('this.getRemainingTime(num)', this.getRemainingTime(num));
          this.model = {
            ...this.model,
            jbkssj: data.startTime,
            jbsc: num > 0 ? this.getRemainingTime(num) : '',
            bz: ''
          }
          // 获取搅拌信息
        } else {
          this.model.bqtm = ''
          this.$Toast('标签条码不存在！')
        }
      } catch (error) {
        console.log(error);
        this.model.bqtm = ''
        // this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
    changeBz(value) {
      if (!value) return
      // 如果有备注、在制品条码、不良原因，则直接加入明细中
      if(this.model.zzptm && this.model.blyy) {
          this.searchData.forEach(item => {
            let obj = {
              ...this.model,
              ...item,
              // blxx: this.model.blxx ? this.model.blxx : '',
              // blxxType: this.model.blxxType ? this.model.blxxType : '',
              // reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
            }
            this.oldGxType = this.model.gxType
            this.list.push(obj)
          })
          this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
          this.initModel()
        }
    },
    async changezjpdj(value, type) {
      if (!value) return
      let params = {
        durableName: value,
        polarity: type
      }
      try {
        let res = await this.$service.QualityControl.getDurableInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.zjpdj = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if(type == 'C') {
            this.model.zjpdjsl = productData.lotQuantity // 正极片弹夹数量
            this.zjpdjObj= productData // 正极片详情
          } else {
            this.model.fjpdjsl = productData.lotQuantity // 正极片弹夹数量
            this.fjpdjObj= productData // 正极片详情
          }
        }
      } catch (error) {
        if(type == 'C') {
          this.model.zjpdj = ''
        } else {
          this.model.fjpdj = ''
        }
        
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
.jb_box {
  // padding: 0 10px;
  .btnContainer {
    margin: 10px;
    margin-bottom: 0px;
  }
  .btnContainer_dis {
    background-color: #aeb2b6;
  }
}
</style>
