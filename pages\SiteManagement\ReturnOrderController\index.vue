<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="仓库编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockCode')">
            <view v-if="model.stockCode">{{ $utils.filterObjLabel(dicts.stockCodeList, model.stockCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="库位编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockLocationCode')">
            <view v-if="model.stockLocationCode">{{ $utils.filterObjLabel(dicts.stockLocationCodeList, model.stockLocationCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockLocationCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="退库方式" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('trayReturnWay')">
            <view v-if="model.trayReturnWay">{{ $utils.filterObjLabel(dicts.trayReturnWayList, model.trayReturnWay) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'trayReturnWay' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="退库类型" borderBottom :required="model.trayReturnWay != 'ConsumableReturn'" labelWidth="100" v-if="model.trayReturnWay !== 'ConsumableReturn'">
          <view class="w100x flex right" @click="checkSelect('trayReturnType')" v-if="model.trayReturnWay">
            <view v-if="model.trayReturnType">{{ $utils.filterObjLabel(dicts.trayReturnTypeList, model.trayReturnType) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'trayReturnType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="托盘/标签条码" labelWidth="150">
          <u--input v-model="model.trayNo" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('trayNo')"></view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="mt10">
        <!-- refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower" -->
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h30 hcenter c_999" v-if="model.trayReturnWay !== 'ConsumableReturn'">
              <view> 托盘标签</view>
              <view>{{ ele.trayNo }}</view>
            </view>

            <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">标签条码</view>
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">数量</view>
            </view>
            <view class="table_content">
              <view v-for="(item, itemindex) in ele.returnItemConsumableList" :key="itemindex" class="flex bl_e1e1e1 h40">
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ itemindex + 1 }}</view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.consumableName }}</view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60"> {{ item.returnQuantity }}</view>
              </view>
              <view class="pt10" v-if="!ele.returnItemConsumableList || ele.returnItemConsumableList.length === 0">
                <u-empty mode="data"></u-empty>
              </view>
            </view>
            <!-- <view v-for="(item, itemindex) in ele.returnItemConsumableList" :key="itemindex">
            <view class="flex between h30 hcenter c_999">
              <view> 最小包装条码/数量</view>
              <view>{{ item.consumableName }}/{{ item.returnQuantity }}</view>
            </view>
          </view> -->
            <view class="mt10">
              <u-button type="error" text="删除" @click="deleteItem(ele, index)"></u-button>
            </view>
          </view>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view>
      </view>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changetrayNo = this.$debounce(this.changetrayNo, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },

      columns: [],
      select: false,
      selectType: '',

      rulesTip: {
        stockCode: '仓库编码不能为空',
        stockLocationCode: '库位编码不能为空',
        trayReturnWay: '退库方式不能为空',
        // trayReturnType: '退库类型不能为空'
      },
      trayNoParmas: {},
      model: {},
      list: [], // 物料扫描数据
      // list: Array.from({ length: 10 }, (v, i) => i),
      dicts: {
        stockCodeList: [], // 仓库编码
        stockLocationCodeList: [], // 库位编码
        trayReturnWayList: [], // 退库方式 
        trayReturnTypeList: [],
      },
    }
  },
  computed: {
  },
  watch: {
    'model.trayNo': {
      handler(val) {
        this.changetrayNo(val)
      }
    },

  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.getEnumValue('TrayReturnType', 'trayReturnTypeList') // 退库类型
    this.getEnumValue('TrayReturnWay', 'trayReturnWayList') // 退库类型
    this.getStockCodeList() // 仓库编码
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'stockCode':
          this.columns = this.dicts.stockCodeList
          break;
        case 'stockLocationCode':
          this.columns = this.dicts.stockLocationCodeList
          break;
        case 'trayReturnType':
          this.columns = this.dicts.trayReturnTypeList
          break;
        case 'trayReturnWay':
          this.columns = this.dicts.trayReturnWayList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'stockCode') {
        this.dicts.stockLocationCodeList = []
        let data = {
          stockCode: this.model.stockCode,
        }
        let res = await this.$service.Dicts.getstockLocationCodeList(data)
        this.dicts.stockLocationCodeList = res.datas.map(item => ({
          label: item.stockLocationText,
          value: item.stockLocationCode,
        }))
      }
    },

    initModel() {
      this.model = {
        stockCode: '',  //  	 仓库编码
        stockLocationCode: '',//  	库位编码
        trayReturnWay: '',  // 
        trayReturnType: '',  // 库位编码
        trayNo: '',//  	托盘标签
      }
    },
    getStockCodeList() {
      this.$service.common.getDictByQueryId('GetStockCodeList').then(res => {
        this.dicts.stockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
      })
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.trayReturnWay == 'TrayReturn') {
        if (_.isEmpty(this.model.trayReturnType)) {
          return this.$Toast('退库类型不能为空')
        }
      }

      let arr = []
      this.list.forEach(item => {
        arr.push(...item.returnItemConsumableList)
      })

      let params = {
        returnItemConsumableList: arr,
        stockCode: this.model.stockCode,
        stockLocationCode: this.model.stockLocationCode,
        trayReturnType: this.model.trayReturnType,
        trayReturnWay: this.model.trayReturnWay,
      }


      this.$service.ReturnOrderController.submit(params).then(res => {
        this.$Toast('操作成功')
        this.model.trayNo = ''
        this.list = []
      })
    },

    async changetrayNo(value) {
      if (!value) return
      let findIndex = this.list.findIndex(item => item.trayNo == value)
      if (findIndex > -1) {
        this.$Toast('该托盘标签已存在!')
        this.model.trayNo = ''
        return
      }
      try {
        let params = {
          trayNo: value,
          trayReturnWay: this.model.trayReturnWay,
          trayReturnType: this.model.trayReturnType
        }
        let res = await this.$service.ReturnOrderController.scanTrayNo(params)
        this.$Toast('操作成功!')
        this.list.push({
          trayNo: this.model.trayNo,
          returnItemConsumableList: res.datas[0].returnItemConsumableList,
        })
        this.model.trayNo = ''
      } catch (error) {
        this.model.trayNo = ''
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
