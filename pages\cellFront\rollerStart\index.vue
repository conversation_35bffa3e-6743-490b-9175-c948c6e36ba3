<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="极卷上卷" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" class="flex_shrink0"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备编号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom labelWidth="100">
          <u--input readonly v-model="model.palletDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="合卷上卷" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect">
            <text class="nowrap">{{ scrollerShow }}</text>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="100">
          <u--input v-model="model.lotName" border="none" placeholder="请扫描或输入极卷条码" :focus="lotName_focus" @focus="focusEvent('lotName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>

        <u-form-item label="工单" borderBottom labelWidth="100">
          <u--input readonly v-model="model.productRequestName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产出数量" labelWidth="100">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input type="number" border="none" v-model="model.productQuantity" readonly></u--input>{{ model.productQuantityUnit }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input type="number" border="none" v-model="model.lotWeight" readonly></u--input>{{ model.lotWeightUnit }} </view>
          </view>
        </u-form-item>
      </u--form>
      <view class="lin40 dib fs16 pl20" style="color: #409eff" @click="onSkip"> 已上卷明细 </view>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="name" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">确认</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import RedScan from "@/mixins/RedScan";
export default {
  mixins: [RedScan],
  name: 'rollerStartIndex',
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      model: {},
      rulesTip: {
        machineName: '设备编号不能为空',
        scroller: '请选择合卷上卷',
        lotName: '极卷条码不能为空',
      },
      columns: [
        { value: 'N', name: '否' },
        { value: 'Y', name: '是' },
      ],
      select: false,
      volumeLotFlag: false,
      machineNameFlag: false,
      lotName_focus: false
    };
  },
  computed: {
    scrollerShow() {
      let arr = this.columns.find(i => i.value == this.model.scroller)
      return arr && arr.name
    }
  },
  watch: {
    'model.scroller': {
      handler(val) {
        if (val === 'N' && this.volumeLotFlag) {
          this.$Toast(`[${this.model.machineName}]设备已上极卷合卷上卷请选择是`)
          this.model.scroller = null
        }
      }
    },
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.lotName': {
      handler(val) {
        this.changeLotName(val)
      }
    },
  },

  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        palletDesc: null, // 极性
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述,
        scroller: null,
        lotName: null, // 极卷条码
        productRequestName: null,// 工单
        productSpecName: null, // 产品单号
        productSpecDesc: null, // 产品描述
        productQuantity: null, // 生产数量 kg
        lotWeigh: null, // 生产数量 kg
        productQuantityUnit: null,
        lotWeightUnit: null,
        releaseTime: null,// 完成时间
      }
      this.volumeLotFlag = false
      this.machineNameFlag = false
    },
    selectFirm(e) {
      this.$set(this.model, 'scroller', e.value[0].value)
      this.lotName_focus = true
      this.select = false
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的设备编号')
      }
      this.lotName_focus = false
      this.select = true
    },
    onSkip() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: `/pages/cellFront/rollerStart/detail?machineName=${this.model.machineName}`
      })
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.model.machineDesc = null // 设备描述
      this.model.palletDesc = null // 极性
      this.model.processOperationName = null // 工序编码
      this.model.processOperationDesc = null // 工序描述
      this.model.areaName = null // 产线编码
      this.model.areaDesc = null // 产线描述,
      this.model.scroller = null // 合卷上卷
      this.model.lotName = null // 极卷条码
      this.model.productRequestName = null// 工单
      this.model.productSpecName = null // 产品单号
      this.model.productSpecDesc = null // 产品描述
      this.model.productQuantity = null // 生产数量 kg
      this.model.lotWeigh = null // 生产数量 kg
      this.model.releaseTime = null// 完成时间

      this.volumeLotFlag = false
      let params = {
        operateNo: '30', // 后台区分固定字段
        machineName: value,
      }
      try {
        let res = await this.$service.roller.trackIn(params)
        this.machineNameFlag = true
        this.volumeLotFlag = res.data.volumeLotFlag
        this.model.machineDesc = res.data.machineDesc
        this.model.palletDesc = res.data.palletDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc
      } catch (error) {
        this.model.machineName = null
      }

    },
    async changeLotName(value) {
      if (!value) return

      this.model.productRequestName = null
      this.model.productSpecName = null
      this.model.productSpecDesc = null
      this.model.productQuantity = null
      this.model.productQuantityUnit = null
      this.model.lotWeight = null
      this.model.lotWeightUnit = null
      let params = {
        operateNo: '30', // 后台区分固定字段
        lotName: value,
        machineName: this.model.machineName,
      }

      try {
        let res = await this.$service.roller.queryLot(params)
        this.model.productRequestName = res.data.productRequestName
        this.model.productSpecName = res.data.productSpecName
        this.model.productSpecDesc = res.data.productSpecDesc
        this.model.productQuantity = res.data.productQuantity
        this.model.productQuantityUnit = res.data.productQuantityUnit
        this.model.lotWeight = res.data.lotWeight
        this.model.lotWeightUnit = res.data.lotWeightUnit
      } catch (error) {
        this.model.lotName = null
      }

    },
    submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        lotName: this.model.lotName,
        machineName: this.model.machineName,
        operateNo: '30',
        userId: this.$getLocal(USER_ID)
      }
      this.$service.coating.assignLot(params).then(res => {
        if (res.success) {
          this.$Toast('指定成功')
          this.initModel()
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'C1Z001005'
          break;
        case 'lotName':
          this.model.lotName = 'C8G1C-11'
          break;
        default:
          break;
      }
      // #endif 
      //#ifdef APP-PLUS
      switch (key) {
        case 'machineName':
          uni.scanCode({
            success: (res) => {
              this.$set(this.model, key, res.result)
            },
          })
          break;
        case 'lotName':
          uni.scanCode({
            success: (res) => {
              this.$set(this.model, key, res.result)
            },
          })
          break;
        default:
          break;
      }
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>