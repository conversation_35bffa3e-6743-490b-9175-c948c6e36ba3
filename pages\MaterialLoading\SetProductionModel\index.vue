<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="换型方式" required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('machineTaskType')">
            <view v-if="model.machineTaskType">{{ $utils.filterObjLabel(dicts.machineTaskTypeList, model.machineTaskType) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'machineTaskType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {


        // lineTypeIndex
        lineTypeIndexTitle: '产线换型设置',

        // machineTypeIndex
        machineTypeIndexTitle: '设备换型设置',
      },
      rulesTip: {
        machineTaskType: '请选择换型方式',
      },
      model: {},
      dicts: {
        machineTaskTypeList: [
          // { value: 'MachineChangeTask', label: '设备换型设置', },
          // { value: 'AreaChangeTask', label: '整线换型设置', },
        ],
      },
      columns: [],
      select: false,
      selectType: '',
    }
  },
  computed: {},
  watch: {},
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.getEnumValue('MachineTaskType', 'machineTaskTypeList') // 单据类型
    this.initModel()
  },
  methods: {
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'machineTaskType':
          this.columns = this.dicts.machineTaskTypeList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },

    initModel() {
      this.model = {
        machineTaskType: 'MachineChangeTask', // 设备换型
      }
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },

    // 确定
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.machineTaskType == 'AreaChangeTask') {
        uni.navigateTo({
          url: `/pages/MaterialLoading/SetProductionModel/lineTypeIndex?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        })
      } else {
        uni.navigateTo({
          url: `/pages/MaterialLoading/SetProductionModel/machineTypeIndex?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
