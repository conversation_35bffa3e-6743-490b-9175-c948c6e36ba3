<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="电芯烘烤开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="烘烤机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入烘烤机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="烘烤机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item v-if="!showSelect" label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.processOperationName }} </view>
        </u-form-item>

        <u-form-item v-if="showSelect" label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="clickProcessOperationName">
            <u--input readonly v-model="model.processOperationName" border="none" placeholder="请选择"></u--input>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="烘洞号" borderBottom required labelWidth="130">
          <u--input :focus="portNameFocus" @blur="portNameFocus=false" v-model="model.portName" border="none" placeholder="请扫描或输入烘洞号" @focus="focusEvent('portName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('portName')"></view>
        </u-form-item>

        <u-form-item label="载具条码" borderBottom required labelWidth="130">
          <u--input v-model="model.durableName" border="none" placeholder="请扫描或输入载具条码" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <!-- <u-form-item label="烘烤标准" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.description : '' }} </view>
        </u-form-item> -->

        <u-form-item label="已扫码载具数" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ lotList.length }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changePortName = this.$debounce(this.changePortName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        machineName: '烘烤机编号不能为空',
        portName: '烘洞号编号不能为空',
      },
      model: {},
      columns: [],
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showSelect: false,
      ProcessOperationNameList: [],
      lotList: [],
      portNameFocus: false
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.portName': {
      handler(res) {
        this.changePortName(res)
      }
    },
    'model.durableName': {
      handler(res) {
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 烘烤机
        description: null, // 烘烤机描述
        processOperationName: null, // 工序
        portName: null, // 烘洞号
        durableName: null
        // lotName: null, // 载具条码
        // ovenTime: null, // 烘烤总时间
        // description: null,
        // productSpecName: null
      }
    },
    selectFirm(e) {
      this.model.processOperationName = e.value[0]
      this.select = false
    },

    clickProcessOperationName() {
      this.select = true
      this.columns = this.ProcessOperationNameList
    },

    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.lotList.length == 0) {
        return this.$Toast('请先扫描载具条码!')
      }
      let params = {
        ...this.model,
        durableList: this.lotList
      }
      this.$service.bakeCell.startOven(params).then(res => {
        if (res.success) {
          this.$Toast('烘烤开始成功!')
          this.lotList = []
          this.initModel()
        }
      })
    },

    /* 烘烤机 */
    async changeMachineName(value) {
      if (!value) return
      this.columns = []
      let params = {
        machineName: value,
        menuName: 'Oven'
      }
      try {
        let res = await this.$service.bakeCell.getMachineInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('烘烤机不可用!')
          }
          if (!res.datas[0].processOperationName) {
            this.model.machineName = ''
            return this.$Toast('烘烤机没有指定工序!')
          }
          this.model = res.datas[0]
          this.model.machineName = value
          this.ProcessOperationNameList = this.model.processOperationName.split(',')
          if (this.ProcessOperationNameList.length > 1) {
            this.showSelect = true
            this.model.processOperationName = this.ProcessOperationNameList[0]
          } else {
            this.showSelect = false
          }
          if (this.model.parameter) {
            this.model.ovenTime = JSON.parse(this.model.parameter).ovenTime
          }
          this.portNameFocus = true
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 扫烘洞编码 */
    async changePortName(value) {
      if (!value) return
      let params = {
        machineName: this.model.machineName,
        portName: value
      }
      let res = await this.$service.bakeCell.checkPort(params)
      if (res.success) {
        if (res.datas.length == 0) {
          this.model.portName = ''
          return this.$Toast('请扫描正确的烘洞编码!')
        }
        if(!res.datas[0].checkResult) {
          this.$Toast(`烘洞${this.model.portName}已开始烘烤`)
          this.model.portName = ''
          this.lotList = []
          return
        }
        this.model.portName = value
      }
    },
    // 扫描载具
    async changeLotName(value) {
      if (!value) return
      let params = {
        // machineName: this.model.machineName,
        // lotName: value
        machineName: this.model.machineName,
        portName: this.model.portName,
        durableName: this.model.durableName,
        processOperationName: this.model.processOperationName,
      }
      try {
        let res = await this.$service.bakeCell.getDurableInfo(params)
        if (res.success) {
          if (res.datas.length == 0) return this.$Toast('载具编码不存在！')
          if (this.lotList.findIndex(item => item.carrierName == this.model.durableName) > -1) {
            let index = this.lotList.findIndex(item => item.carrierName == this.model.durableName)
            this.lotList.splice(index, 1)
            this.$Toast('载具条码解绑成功!')
          } else {
            let obj = {
              ...res.datas[0]
            }
            this.lotList.push(obj)
            this.$Toast('载具条码绑定成功!')
          }
          this.model.durableName = ''
        }
      } catch (error) {
        this.model.durableName = ''
      }
    },

    gotoQuery() {
      if (this.lotList.length == 0) return
      uni.navigateTo({
        url: `/pages/bake/bakeStartCell/modules/SweptVolume?portName=${this.model.portName}&machineName=${this.model.machineName}&lotList=${JSON.stringify(this.lotList)}`,
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.ZKKX.03.01'
          break;
        case 'portName':
          this.model.portName = '1'
          break;
        case 'lotName':
          this.model.lotName = '1000073'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'portName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'durableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
