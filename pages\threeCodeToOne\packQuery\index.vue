<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="mb10 br10 bc_fff pa10">
      <u--form labelPosition="left" :model="model" labelWidth="100">

        <u-form-item label="条码" borderBottom required labelWidth="100">
          <u--input v-model="model.batchId" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('batchId')"></view>
        </u-form-item>

        <u-form-item label="物料箱标签" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.carrierName2 }}
          </view>
        </u-form-item>
        <u-form-item label="成品编码" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.productSpecName }}
          </view>
        </u-form-item>

        <u-form-item label="装箱规格(行*列*层)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.packRule }}</view>
        </u-form-item>

        <u-form-item label="装箱数量" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.countInf }}</view>
        </u-form-item>
      </u--form>
    </view>
    <view class="myContainer ma10">
      <view class="mt10">
        <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered"
          @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll"
          refresher-background="#f3f3f7">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list1" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>箱号</view>
              <view>{{ ele.carrierName }}</view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view>箱内坐标</view>
              <view>{{ ele.coord }} </view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view>客户码</view>
              <view> {{ ele.barcode }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view> PACK码</view>
              <view> {{ ele.sourceSn }}</view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view>包装工单</view>
              <view>{{ ele.productOrderName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>包装时间</view>
              <view> {{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>包装人员</view>
              <view>{{ ele.eventUser }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>客户</view>
              <view></view>
            </view>
          </view>
          <NoData v-if="!list1 || list1.length === 0"></NoData>

        </scroll-view>
      </view>
    </view>

    <view class="btnContainer" @click="submit">查看装箱履历</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import moment from 'moment'
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changebatchId = this.$debounce(this.changebatchId, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      columns: [],
      select: false,
      selectType: '',
      model: {},
      list1: [],
      list: [],
    }
  },
  computed: {
  },
  watch: {
    'model.batchId': {
      handler(val) {
        this.changebatchId(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
  },
  methods: {
    moment,
    initModel() {
      this.model = {
        batchId: '',
        carrierName2: '',
        countInf: '',// 装箱数量
        productSpecName: '',//成品编码
        packRule: '',//装箱规格
        layerCount: '',//每层载荷
        Quantity: '',//总数
      }
    },

    // 设置
    submit() {
      console.log("testeste")
      uni.navigateTo({
        url: `/pages/threeCodeToOne/packQuery/detail?list1=${encodeURIComponent(JSON.stringify(this.list))}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))} `,
      })
    },

    async changebatchId(value) {
      if (!value) return

      let params = {
        carrierName: value,
      }
      let res = await this.$service.ThreeCodeToOne.getPackBoxInf(params)
      if (res.success) {
        this.model.countInf = res.datas[0].countInf
        this.model.productSpecName = res.datas[0].productSpecName
        this.model.packRule = res.datas[0].packRule
        this.model.layerCount = res.datas[0].layerCount
        this.model.Quantity = res.datas[0].Quantity
        this.model.carrierName2 = value
      }
      this.list1 = []
      try {
        let parmas = {
          batchId: value,
        }
        let res = await this.$service.ThreeCodeToOne.packQuery(parmas)
        if (res.success) {
          const data = res.datas[0];

          if (!data.packingList || (Array.isArray(data.packingList) && data.packingList.length === 0)) {
            this.$Toast('该箱无绑定关系，请核实!');
          }

          this.list1 = data.packingList || [];
          this.list = data.packingHistList || [];
        }
        this.model.batchId = ''
      } catch (error) {
        this.model.batchId = ''
      }
    },
    getCoordinate(index) {
      if (!this.model.layerCount || this.model.layerCount === 0) return '-';
      const divisor = this.model.Quantity - index;
      const quotient = Math.floor(divisor / this.model.layerCount); // 商
      const remainder = divisor % this.model.layerCount;            // 余数

      let layer, count;
      if (remainder === 0) {
        // 整除
        layer = quotient;
        count = this.model.layerCount;
      } else {
        // 不整除
        layer = quotient + 1;
        count = remainder;
      }

      return `第${layer}层${count}个`;
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
