<template>
  <view class="listPage bc_f3f3f7 pl5 pr5 pb10">
    <view class="listContainer">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view class="mb10 br10 bc_fff pa10 fs12" v-for="(ele, index) in model" :key="index">
          <view class="flex flex_wrap">
            <view class="flex w100x lin24 c_999">
              <view class="w60">条码号:</view>
              <view class="c_00b17b"> {{ ele.lotName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">设备编码:</view>
              <view> {{ ele.machineName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">设备名称:</view>
              <view> {{ ele.machineText }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">工序编码:</view>
              <view> {{ ele.processOperationName }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">工序名称:</view>
              <view> {{ ele.processOperationText }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">工步:</view>
              <view> {{ ele.workStep }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">单位:</view>
              <view> {{ ele.unit }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">参数编码:</view>
              <view> {{ ele.parameterId }} </view>
            </view>
            <view class="flex w50x lin24 c_999">
              <view class="w60">参数描述:</view>
              <view> {{ ele.parameterText }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">参数值:</view>
              <view> {{ ele.parameterValue }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">参数结果:</view>
              <view> {{ ele.parameterResult }} </view>
            </view>

            <view class="flex w50x lin24 c_999">
              <view class="w60">操作时间:</view>
              <view class="fs10 nowrap"> {{ ele.eventTime }} </view>
            </view>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>

      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'MachineParamInfoLot',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },

  props: {
    paramsOption: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      model: [],
      // model: Array.from({ length: 30 }, (v, i) => i),
    };
  },
  watch: {

  },
  created() {
    // console.log('12312312', this.paramsOption);
    this.initSearchModel()
    this.getData()
  },

  methods: {
    initSearchModel() {
      this.searchModel = {
        pageNo: this.pageNumber,
        limit: 10,
      }
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      params.lotName = this.paramsOption.batchId
      // setTimeout(() => {
      //   // let res = Array.from({ length: 20 }, (v, i) => i)
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return
      await this.$service.PDARetrospectiveReport.retrospectLotHist(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.datas[0].records : [...this.model, ...res.datas[0].records]
          if (this.searchModel.pageNo == res.datas[0].pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;
  // .topContainer {
  //   flex-shrink: 0;
  // }
  .listContainer {
    flex: 1;
  }
}
</style>