<template>
  <view class="bc_fff listPage">
    <u-navbar title="材料上料-已上料明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ma10">
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(item, index) in simpleTrackProduct" :key="index">
            <view class="topContainer bc_999 br12 ma10">
              <view class="flex h30 hcenter c_00b17b">
                <view>安装点:</view>
                <view class="ml6">{{ item.data[index].materialLocationName }}</view>
              </view>
              <view class="flex h30 hcenter c_00b17b">
                <view>材料:</view>
                <view class="ml6">{{ item.data[index].consumableSpecName }}</view>
              </view>
            </view>
            <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index) in item.data" :key="index">
              <view class="flex between h40 hcenter c_999">
                <view>标签条码:</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料数量(M):</view>
                <view>{{ ele.createQuantity }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料时间:</view>
                <view>{{ ele.createTime }}</view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      simpleTrackProduct: []
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.getData()
  },
  methods: {
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      return resArr
    },
    getData(clearOldData = false, refresh = false) {
      let params = {
        machineName: this.machineName,
      }
      this.$service.SemiFinishedVehicle.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import "../../../../styles/publicStyle.scss";
</style>