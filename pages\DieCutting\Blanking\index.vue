<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切下料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="产品描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="弹夹" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.durableName }} </view>
        </u-form-item>

        <u-form-item label="弹夹容量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.capacity }} </view>
        </u-form-item>

        <u-form-item label="批次ID" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="设备计数(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.computedQty }} </view>
        </u-form-item>

        <u-form-item label="人工确认(片)" borderBottom required labelWidth="130">
          <!-- <view class="w100x flex right" v-if="!uesrConfirmFlag"> {{ model.uesrConfirm }}</view> -->
          <u--input type="number" placeholder="请输入数量(片)" v-model="model.uesrConfirm" border="none"></u--input>
        </u-form-item>

        <u-form-item label="报废数量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.ngQuantity }} </view>
          <u-icon class="ml15" @click="gotoQuery(1)" name="plus-circle" color="#2979ff" size="28"></u-icon>
          <u-icon class="ml15" @click="gotoQuery(2)" name="minus-circle" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-form-item label="良品数量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.productQuantity }} </view>
        </u-form-item>

        <u-form-item label="极卷信息" labelWidth="120">
          <view class="w100x flex right"> </view>
          <u-icon class="ml2" @click="gotoQuery(3)" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>

    <view class="btnContainer2">
      <view @click="readComputedQty">读取数量</view>
      <view @click="submit">下料</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        uesrConfirm: '人工确认不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      uesrConfirmFlag: false,
      show: false,
      content: '',
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.uesrConfirm': {
      handler(val) {
        if (val) {
          // 良品数量是：人工确认-报废数量
          if (!isNaN(Number(val))) {
            this.model.productQuantity = this.$utils.calculate_reduce(this.model.uesrConfirm, this.model.ngQuantity)
            // if (this.model.productQuantity < 0) this.model.productQuantity = 0
          } else {
            this.model.productQuantity = null
          }
        } else {
          this.model.productQuantity = null
        }
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  onShow() {
    uni.$once('updateData', (data) => {
      if (data) {
        this.$service.DieCutting.DieCuttingByMachineName({ machineName: data.machineName }).then(res => {
          this.model.ngQuantity = res.datas[0].ngQuantity ? res.datas[0].ngQuantity : null
          this.model.uesrConfirm = data.uesrConfirm ? data.uesrConfirm : null
          // 良品数量是：人工确认-报废数量
          this.model.productQuantity = this.model.uesrConfirm - this.model.ngQuantity
          // if (this.model.productQuantity < 0) this.model.productQuantity = 0
        })
      }
    })
  },
  methods: {
    gotoQuery(type) {
      if (type == 1) {
        if (!this.model.machineName) return this.$Toast('设备不能为空！')
        uni.navigateTo({
          url: `/pages/DieCutting/Blanking/modules/addModel?machineName=${this.model.machineName}&uesrConfirm=${this.model.uesrConfirm || 0}&computedQty=${this.model.computedQty || 0}`,
        })
      } else if (type == 2) {
        if (!this.model.machineName) return this.$Toast('设备不能为空！')
        uni.navigateTo({
          url: `/pages/DieCutting/Blanking/modules/deleteModel?machineName=${this.model.machineName}&processOperationName=${this.model.processOperationName}&lotName=${this.model.lotName}&uesrConfirm=${this.model.uesrConfirm || 0}&computedQty=${this.model.computedQty || 0}`,
        })
      } else {
        if (!this.model.machineName) return this.$Toast('设备不能为空！')
        uni.navigateTo({
          url: `/pages/DieCutting/Blanking/modules/PolarInformation?processOperationName=${this.model.processOperationName}&machineName=${this.model.machineName}`,
        })
      }
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备
        machineSpecDesc: null, // 设备描述
        productSpecName: null, // 产品编码
        productSpecDesc: null, // 产品描述
        durableName: null, // 弹夹
        capacity: null, // 弹夹容量
        lotName: null, // 批次号
        uesrConfirm: null, // 人工确认
        ngQuantity: null, // 报废数量
        productQuantity: null, // 良品数量
        computedQty: null // 计算数量
      }
    },
    cancel() {
      this.show = false
    },
    confirm() {
      this.show = false
      this.uesrConfirmFlag = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.saveNo = true
      this.select = false
    },
    readComputedQty() {
      if (!this.model.machineName) return this.$Toast('设备不能为空！')
      let params = {
        machineName: this.model.machineName
      }
      this.$service.DieCutting.getMachineGoodQuality(params).then(res => {
        const { quantity } = res.datas[0]
        if (quantity) {
          if (quantity > this.model.capacity) {
            this.show = true
            this.content = `设备计数大于弹夹容量, 数据异常,请确认是否由人工录入?`
            this.$set(this.model, 'computedQty', quantity)
          } else {
            this.$set(this.model, 'computedQty', quantity)
            this.$set(this.model, 'uesrConfirm', quantity)
          }
          this.$Toast('读取成功！')
        } else {
          this.show = true
          this.content = `设备计数未采集到, 请确认是否由人工录入?`
        }
      })
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.productQuantity < 0) {
        this.$Toast('良品数量不能为负')
        return
      }
	  if(this.model.productQuantity>this.model.capacity)
	  {
		  this.$Toast('产出良品数量不能大于弹夹容量!')
		  return
	  }
      let params = {
        ...this.model
      }
      this.$service.DieCutting.checkDieCuttingConsumeMaterialMsgProcessor(params).then(res => {
        console.log('checkDieCuttingConsumeMaterialMsgProcessor', res);
        if (res.code == 0 && res.datas) {
          if (res.datas[0] === 'false') {
            // 报错
            uni.showModal({
              title: '提示',
              content: res.message,
              cancelText: '取消',
              confirmText: '确认',
              cancelColor: '#666',
              confirmColor: '#409eff',
              success: (res) => {
                if (res.confirm) {
                  this.$service.DieCutting.DieCuttingConsumeMaterialMsgProcessor(params).then(res => {
                    this.$Toast('下料成功')
                    this.initModel()
                  })
                }
                if (res.cancel) { }
              },
            })
          } else {
            uni.showModal({
              title: '提示',
              content: `设备${this.model.machineName}，现产出${this.model.productQuantity}片, 请确认！`,
              cancelText: '取消',
              confirmText: '确认',/* 只可以4个字 */
              cancelColor: '#666',
              confirmColor: '#409eff',
              success: (res) => {
                if (res.confirm) {
                  this.$service.DieCutting.DieCuttingConsumeMaterialMsgProcessor(params).then(res => {
                    this.$Toast('下料成功')
                    this.initModel()
                  })
                }
                if (res.cancel) { }
              },
            })

          }
        }
      })
      // this.$service.DieCutting.DieCuttingConsumeMaterialMsgProcessor(params).then(res => {
      //   this.$Toast('下料成功')
      //   this.initModel()
      // })
    },

    /* 搅拌机 */
    changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      this.$service.DieCutting.DieCuttingByMachineName(params).then(res => {
        if (res.datas.length > 0) {
          this.model = res.datas[0]
          this.model.ngQuantity = this.model.ngQuantity ? this.model.ngQuantity : 0
          if (!this.model.productSpecName) {
            this.$Toast('该设备未设置生产产品编码，请先设置后，再下料！')
            this.initModel()
          }
        } else {
          this.model.machineName = null
          this.$Toast('设备不存在！')
        }
      }).catch(() => {
        this.model.machineName = null
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.LCDP.01.02'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
