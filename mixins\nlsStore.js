/**
 *
 * @description: 国际化 全局
 * @author: ouyangqing
 * 页面使用
*  data() {
    return {
      globalMap: getApp().globalData.globalMap // 获取全局数据
    }
  },
 *
*/

import { PDA_MENUId } from '@/utils/common/evtName.js'
export default {
  globalData: {
    globalMap: {
      msCannotEmpty: '不能为空',
      btSearch: '查询',
      btRefresh: '刷新',
      btReset: '重置',
      lbCancel: '取消',
      btCloseAllTab: '关闭',
      btOk: '确定',
      btSave: '保存',
      btSubmit: '提交',
      SaveAndSubmit: '保存并提交',
      btAdd: '新增',
      btEdit: '编辑',
      btDetail: '详情',
      btRemove: '删除',
      lbBack: '返回',
      lbWarning: '提示',
      lbSuccess: '成功',
      lbError: '错误',
      btActive: '启用',
      btNotActive: '禁用',
      btExit: "退出",
      startTime: '开始日期',
      endTime: '结束日期',
      lbPleaseEnter: '请输入',
      lbPleaseScan: '请扫描',
      lbPleaseSelect: '请选择',

      lbHome: '首页',
      lbmine: '我的',
      currentVersion: '当前版本',
      latestVersion: '最新版本',
      languageSwitching: '语言切换',
      logout: '退出登录',
      msExitSuccess: '退出成功 , 请重新登陆!'
    }
  },
  data() {
    return {
      globalMap: {}
    }
  },

  methods: {
    // 初始化全局国际化
    init_globalMap() {
      // const appInstance = getApp();
      // const globalData = appInstance.globalData;
      this.globalMap = getApp().globalData.globalMap
    },
    setGlobalNls() {
      // let result = menuId.replace(/[^0-9]/g,'');
      // let res_menuId = result.slice(0,9)
      let params = {
        appName: 'GFM',
        menuId: PDA_MENUId,
      };
      this.$service.nls.getGlobalNls(params).then(res => {
        let nlsMap = {}
        if (res.datas.length > 0) {
          res.datas.forEach((item) => {
            nlsMap[item.labelKey] = item.labelText;
          })
        }
        this.setNls(nlsMap)
      })
    },

    setNls(injectMap) {
      // globalMap 初始化
      this.init_globalMap()
      let isSync = false;
      Object.keys(this.globalMap).forEach((key) => {
        if (injectMap.hasOwnProperty(key)) {
          this.globalMap[key] = injectMap[key];
        } else {
          isSync = true;
        }
      })

      if (isSync) {
        this.syncGlobalNls();
      }
    },

    syncGlobalNls() {
      let localType = 'Global'
      let nlsList = []
      Object.keys(this.globalMap).forEach((key) => {
        let typeName = key.startsWith('lb') ? 'label' : key.startsWith('ms') ? 'message' : key.startsWith('bt') ? 'button' : 'label'
        let nlsItem = {
          appName: 'GFM',
          menuId: PDA_MENUId,
          porosMenuId: null,
          labelKey: key,
          labelText: this.globalMap[key],
          localType: localType,
          typeName: typeName,
          nlsValues: []
        }
        nlsList.push(nlsItem);
      });
      let data = nlsList
      this.$service.nls.syncAll(data).then(res => {
        res.datas.forEach((item) => {
          this.globalMap[item.labelKey] = item.labelText
        })
      })
    }
  },
}
