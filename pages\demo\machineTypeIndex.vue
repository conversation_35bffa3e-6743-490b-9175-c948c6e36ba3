<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备号描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineText" border="none"></u--input>
        </u-form-item>

        <!-- <u-form-item label="设备号描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineText" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item> -->

        <!-- <u-form-item label="操作类型" required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('containerBindOrUnBind')">
            <view v-if="model.containerBindOrUnBind">{{ $utils.filterObjLabel(dicts.containerBindOrUnBindList, model.containerBindOrUnBind) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'containerBindOrUnBind' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item> -->

        <!-- {{ model.productOrderName }} -->
        <u-form-item label="工单" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <zqs-select :multiple="true" :list="dicts.productOrderNameList" label-key="label" value-key="value" placeholder=" 请选择" title="工单编码/产品名称" v-model="model.productOrderName"></zqs-select>
          </view>
        </u-form-item>

        <!-- {{ model.processOperationName }} -->
        <u-form-item label="工序" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <zqs-select :multiple="true" :list="dicts.processOperationNameList" label-key="label" value-key="value" placeholder=" 请选择" title="工序编码/工序描述" v-model="model.processOperationName"></zqs-select>
          </view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
export default {
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      rulesTip: {
        machineName: '设备编号不能为空',
        productOrderName: '工单组合不能为空', // 
        processOperationName: '工序组合不能为空', // 
      },
      model: {},
      dicts: {
        // 工单
        productOrderNameList: [
          // { label: 'label)', value: 'value', },
          // { label: 'label2)', value: 'value2', },
        ],
        //  工序
        processOperationNameList: [
          // { label: 'label123)', value: 'value1', },
          // { label: 'labe123l)', value: 'value3', },
        ]
      },

      columns: [],
      select: false,
      selectType: '',
    }
  },
  computed: {
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.machineTypeIndexTitle // 标题
    this.nlsMap = nlsMap
    // this.getEnumValue('MachineTaskType', 'machineTaskTypeList') // 单据类型
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'containerBindOrUnBind':
          // this.columns = this.dicts.containerBindOrUnBindList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },
    initModel() {
      this.model = {
        machineTaskType: '',// 
        machineName: null, // 设备号
        machineText: null, // 设备描述
        productOrderName: [], // 工单组合
        processOperationName: [], // 工序组合
      }
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        // ...this.model
        machineName: this.model.machineName,
        machineTaskType: 'MachineChangeTask',
        productOrderName: this.model.productOrderName.join(','),
        processOperationName: this.model.processOperationName.join(',')
      }
      this.$service.SetProductionModel.machineTaskControllerAdd(params).then(res => {
        this.$Toast('操作成功!')
        this.initModel()
      })
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.model.machineText = ''
      this.model.productOrderName = []
      this.model.processOperationName = []
      this.dicts.productOrderNameList = []
      this.dicts.processOperationNameList = []
      let params = {
        machineName: value,
        machineTaskType: 'MachineChangeTask',
      }
      try {
        let res = await this.$service.SetProductionModel.listProductOrder(params)
        this.model.machineText = res.datas[0].machineText

        this.dicts.productOrderNameList = res.datas[0].productOrderList.map((item) => ({
          label: `${item.productOrderName}/${item.productSpecText}`,
          value: item.productOrderName,
        }))
        this.dicts.processOperationNameList = res.datas[0].processOperationList.map((item) => ({
          label: `${item.processOperationName}/${item.processOperationText}`,
          value: item.processOperationName,
        }))

      } catch (error) {
        this.initModel()
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
