<template>
  <view class="modal" v-if="visible">
    <view class="dialog">
      <view class="dialog-t">
        <slot name="content"></slot>
      </view>
      <view class="flex flex-jcse setBtn h40">
        <view class="flex1 btn-l flex center" @click="onCancel" v-if="cancelText">{{ cancelText }}</view>
        <view class="flex1 btn-r flex center" @click="onConfirm" v-if="confirmText">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cancelText: '取消',
      confirmText: '确认',
      visible: false
    };
  },

  computed: {
  },
  methods: {
    showDialog(option = {}) {
      this.visible = true;
      this.option = option;
      if (option) {
        if (option.cancelText !== null && option.cancelText !== undefined) {
          this.cancelText = option.cancelText;
        }
        if (option.confirmText !== null && option.confirmText !== undefined) {
          this.confirmText = option.confirmText;
        }
      }
    },
    onCancel() {
      this.visible = false;
      if (this.option.onCancel && this.option.onCancel instanceof Function) {
        this.option.onCancel();
      }
    },
    onConfirm() {
      let vm = this
      if (this.option.onConfirm && this.option.onConfirm instanceof Function) {
        this.option.onConfirm(vm);
      }
    },
    close() {
      this.visible = false;
    }
  },
}

</script>



<style lang="scss" scoped>
.modal {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}
.dialog {
  position: absolute;
  // padding: 20rpx;
  overflow: hidden;
  border-radius: 10rpx;
  width: 80%;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
  background-color: #fff;
}

.dialog-t {
  max-height: 500rpx;
  overflow-y: auto;
}

.setBtn {
  left: 0;
  right: 0;
  background-color: #fff;
  .btn-r {
    width: 150rpx;
    padding: 0rpx 20rpx;
    // border-radius: 50rpx;
    // background: #2871f7;
    // border: 2rpx solid #2871f7;
     border: 2rpx solid #d9d9d9;
    font-size: 28rpx;
    font-weight: 400;
    color: rgb(64, 158, 255);
  }
  .btn-l {
    width: 150rpx;
    padding: 10rpx 20rpx;
    // border-radius: 50rpx;
    background: #fff;
    border: 2rpx solid #d9d9d9;
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
  }
}
</style>



