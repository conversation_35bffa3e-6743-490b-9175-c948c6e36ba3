<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="仓库编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockCode')">
            <view v-if="model.stockCode">{{ $utils.filterObjLabel(dicts.stockCodeList, model.stockCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="库位编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockLocationCode')">
            <view v-if="model.stockLocationCode">{{ $utils.filterObjLabel(dicts.stockLocationCodeList, model.stockLocationCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockLocationCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
        <!-- 
        <u-form-item label="物料总标签" borderBottom labelWidth="100">
          <u--input v-model="model.trayNo" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('trayNo')"></view>
        </u-form-item> -->

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecText) }}
          </view>
        </u-form-item>
        <!-- <u-form-item label="物料单位" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.consumableUnit }}
          </view>
        </u-form-item> -->
        <u-form-item label="物料数量" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.totalQuantity }} {{ model.consumableUnit }}</view>
        </u-form-item>
        <!-- <u-form-item label="批次号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.materialBatch }}
          </view>
        </u-form-item> -->

        <u-form-item label="物料箱标签" labelWidth="100">
          <u--input v-model="model.boxNo" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('boxNo')"></view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料箱标签</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w80">批次号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">是否打印</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>

        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.boxNo }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w80">{{ ele.dateCode }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 txt_c">
              <view class="flex w100x hcenter pl10">
                <u--input class="" v-model="ele.receiveQuantity" border="none" type="number" placeholder="请输入"></u--input>
              </view>
            </view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70">
              <u-checkbox-group v-model="ele.isPrint" @change="(value) => checkboxChange(value, ele)">
                <u-checkbox name="1"> </u-checkbox>
              </u-checkbox-group>
            </view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>
    </view>
    <view class="btnContainer" @click="submit('0')">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";


const SHOW_MODEL_KEY = 'CONSUMABLE_RECIEVE_EXIST'
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changetrayNo = this.$debounce(this.changetrayNo, 1000)
    this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
    return {
      lodash: _,
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },

      columns: [],
      select: false,
      selectType: '',

      rulesTip: {
        stockCode: '仓库编码不能为空',
        stockLocationCode: '库位编码不能为空',
        // trayNo: '物料总标签不能为空',
      },
      trayNoParmas: {},
      model: {},
      list: [], // 物料扫描数据
      // list: Array.from({ length: 20 }, (v, i) => ({
      //   isPrint: ['1'],
      //   i: i
      // })),
      test1: [true],
      dicts: {
        stockCodeList: [], // 仓库编码
        stockLocationCodeList: [], // 库位编码
      },
    }
  },
  computed: {
  },
  watch: {
    'model.trayNo': {
      handler(val) {
        this.changetrayNo(val)
      }
    },

    'model.boxNo': {
      handler(val) {
        this.changeboxNo(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.getStockCodeList() // 仓库编码
    this.initModel()
  },
  methods: {

    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'stockCode':
          this.columns = this.dicts.stockCodeList
          break;
        case 'stockLocationCode':
          this.columns = this.dicts.stockLocationCodeList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'stockCode') {
        this.dicts.stockLocationCodeList = []
        let data = {
          stockCode: this.model.stockCode,
        }
        let res = await this.$service.Dicts.getstockLocationCodeList(data)
        this.dicts.stockLocationCodeList = res.datas.map(item => ({
          label: item.stockLocationText,
          value: item.stockLocationCode,
        }))
      }
    },

    initModel() {
      this.model = {
        stockCode: '',  //  	 仓库编码
        stockLocationCode: '',//  	库位编码
        trayNo: '',//  	物料总标签
        consumableSpecName: '',// 物料编码
        consumableSpecText: '', // 物料名称
        consumableUnit: '', // 物料单位
        totalQuantity: '', // 物料发料数量
        materialBatch: '', // 	批次号
        boxNo: '',//  	物料箱标签


      }
    },
    getStockCodeList() {
      this.$service.common.getDictByQueryId('GetStockCodeList').then(res => {
        this.dicts.stockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
      })
    },
    checkboxChange(value, record) {
      if (value.length > 0) {
        record.isPrint = ['1']
      } else {
        record.isPrint = []
      }
    },

    // 设置
    async submit(duplicateReceivingFlag) {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let flag = this.list.every(item => item.receiveQuantity && item.receiveQuantity != 0)
      if (!flag) {
        this.$Toast('请输入数量')
        return
      }

      let obj = {
        "consumableSpecName": this.model.consumableSpecName,
        "erpStockCode": "ERP001",
        "expirationDate": "2024-07-26",

        "factoryName": this.trayNoParmas.factoryName,
        "grade": this.trayNoParmas.factoryName,
        "makeGroupsFlag": "Y",
        "outLot": "20240716121212",
        "produceDate": "2024-07-26",
        "productOrderPickList": [
          {
            "dateCode": "bactch001",
            "productOrderPickName": "SOUTM0000331",
            "productOrderName": "2024072300003",
            "receiveQuantity": 1000
          }
        ],

        "qualityInspection": "240726",
        "qualityState": "Y",
        "recheckDate": "2024-07-26 12:12:12",
        "releaseQuantity": 200,
        "revision": "1",
        "stockCode": this.model.stockCode,
        "stockLocationCode": this.model.stockLocationCode,
        "supNo": "asdads",
        "trayNo": this.model.trayNo,
        "warehousingItemList": this.list,
        "usefulness": "1",
        "warehousingDate": "2024-07-26",
        // 'warehousingItemList':
      }
      try {
        let params = {
          ...this.trayNoParmas,
          warehousingItemList: this.list.map((ele) => ({
            ...ele,
            isPrint: ele.isPrint.length > 0 ? '1' : '0'
          })),
          stockCode: this.model.stockCode,
          stockLocationCode: this.model.stockLocationCode,
          consumableSpecName: this.model.consumableSpecName,
          trayNo: this.model.trayNo,
          duplicateReceivingFlag: duplicateReceivingFlag, // for后端校验是否存在该标签，存在则返回提示给前端，前端弹窗提示
          productOrderPickList: this.trayNoParmas.list && this.trayNoParmas.list.length > 0 && this.trayNoParmas.list.map(ele => ({
            dateCode: ele.materialBatch,
            productOrderName: ele.productOrderName,
            productOrderPickName: ele.materialRequisition,
            receiveQuantity: ele.quantity,
          })) || []
        }
        // handleSucess 自行处理返回结果
        await this.$service.TrayWarehousing.submit(params, { handleSucess: true }).then(res => {
          if (res.success) {
            this.$Toast('操作成功')
            this.model.trayNo = ''
            this.list = []
            this.model.consumableSpecName = ''
            this.model.consumableSpecText = ''
            this.model.materialBatch = ''
            this.model.consumableUnit = ''
            this.model.totalQuantity = ''
            this.trayNoParmas = {}
            if (res.datas.length > 0) {
              setTimeout(() => {
                this.myprintPackage(res.datas) // 打印
              }, 800);
            }
          } else {
            let errMsg = res.msg || res.msgCode
            if (res.data == SHOW_MODEL_KEY) {
              this.openConfirmHandle(errMsg)
            } else {
              this.$Toast(errMsg)
            }
          }
        })
      } catch (error) {
      }
    },
    openConfirmHandle(msg) {
      uni.showModal({
        title: '提示',
        content: `${msg}`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.submit('1')
          }
          if (res.cancel) { }
        },
      })
    },
    async changetrayNo(value) {
      if (!value) return
      this.list = []
      this.trayNoParmas = {}
      this.model.consumableSpecName = ''
      this.model.consumableSpecText = ''
      this.model.materialBatch = ''
      this.model.consumableUnit = ''
      this.model.totalQuantity = ''
      let params = {
        trayId: this.model.trayNo
      }
      try {
        let res = await this.$service.TrayWarehousing.trayMaterialInfo(params)
        this.model.consumableSpecName = res.datas[0].materialCode
        this.model.consumableSpecText = res.datas[0].materialDescription
        this.model.materialBatch = res.datas[0].list[0].materialBatch
        this.model.consumableUnit = res.datas[0].consumableUnit
        this.model.totalQuantity = res.datas[0].totalQuantity
        this.trayNoParmas = res.datas[0]
        // let productOrderPickList = res.datas[0].list.map(ele => ({
        //   dateCode: ele.materialBatch,
        //   productOrderName: ele.productOrderName,
        //   productOrderPickName: ele.materialRequisition,
        //   receiveQuantity: ele.quantity,
        // }))
        // let obj = {
        //   consumableSpecName: res.datas[0].materialCode,
        //   consumableSpecText: res.datas[0].materialDescription,
        //   productOrderPickList: productOrderPickList,
        //   factoryName: res.datas[0].factoryName,
        //   releaseQuantity: res.datas[0].totalQuantity
        // }
      } catch (error) {
        this.model.trayNo = ''
      }
    },
    async changeboxNo(value) {
      console.log(value, 'valuevaluevaluevalue');

      if (!value) return
      if (this.list.length == 10) {
        this.$Toast('最多添加10个物料箱标签!')
        return
      }
      // 如果存在
      let findIndex = this.list.findIndex(item => item.boxNo == value)
      // dateCode
      if (findIndex > -1) {
        this.$Toast('该物料箱标签已存在!')
        this.model.boxNo = ''
        return
      }
      try {
        let params = {
          boxNo: value,
        }
        if (this.list.length > 0) {
          params.consumableSpecName = this.list[0].consumableSpecName
        }
        let res = await this.$service.TrayWarehousing.scanBoxNo(params)

        if (res.success) {
          // this.list.push({
          //   boxNo: value,
          //   receiveQuantity: '',
          //   isPrint: ['1'],
          // })
          this.list.push({
            ...res.datas[0],
            boxNo: value,
            receiveQuantity: this.list.length > 0 ? this.list[0].receiveQuantity : '', // 数量合第一条扫描的保持一样
            isPrint: [],
          })
          this.model.consumableSpecName = res.datas[0].consumableSpecName
          this.model.consumableSpecText = res.datas[0].consumableSpecText
          this.model.consumableUnit = res.datas[0].consumableUnit

        }
        this.model.boxNo = ''
      } catch (error) {
        this.model.boxNo = ''
      }
    },


    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        case 'boxNo':
          this.model.boxNo = '8000000012*190266*A1*20240826*01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
