<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="报工入库" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="车间" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('workShopSection')">
            <view v-if="model.workShopSection">{{ $utils.filterObjLabel(cjArr, model.workShopSection) }}</view>
            <view class="c_c0c4cc" v-else>请选择车间</view>
            <view class="ml5" :style="{ transform: select && selectType === 'workShopSection' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="生产线" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('areaName')">
            <view v-if="model.areaName">{{ $utils.filterObjLabel(scxArr, model.areaName) }}</view>
            <view class="c_c0c4cc" v-else>请选择生产线</view>
            <view class="ml5" :style="{ transform: select && selectType === 'areaName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="生产订单" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="onSkip('workOrderName')">
            <view v-if="model.workOrderName">{{ model.workOrderName }}</view>
            <view class="c_c0c4cc" v-else>请选择生产订单</view>
            <view class="ml5" :style="{ transform: select && selectType === 'workOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="onSkip('productRequestName')">
            <view v-if="model.productRequestName">{{ model.productRequestName }}</view>
            <view class="c_c0c4cc" v-else>请选择生产工单</view>
            <view class="ml5" :style="{ transform: select && selectType === 'productRequestName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="onSkip('productSpecName')">
            <view v-if="model.productSpecName">{{ model.productSpecName }}</view>
            <view class="c_c0c4cc" v-else>请选择物料</view>
            <view class="ml5" :style="{ transform: select && selectType === 'productSpecName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="报工状态" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('reportProductRequestState')">
            <view v-if="model.reportProductRequestState">{{ $utils.filterObjLabel(bgztArr, model.reportProductRequestState) }}</view>
            <view class="c_c0c4cc" v-else>请选择报工状态</view>
            <view class="ml5" :style="{ transform: select && selectType === 'reportProductRequestState' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="开始日期" labelWidth="150" @click="startTimeShow = true" borderBottom>
          <u-input v-model="model.startTime" disabled disabledColor="#fff" placeholder="请选择开始日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: startTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>

        <u-form-item label="结束日期" labelWidth="150" @click="endTimeShow = true">
          <u-input v-model="model.endTime" disabled disabledColor="#fff" placeholder="请选择结束日期" border="none" />
          <u-icon slot="right" name="arrow-down" :style="{ transform: endTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>

        <!-- <u-form-item label="照片1" borderBottom labelWidth="100">
          <view class="w100x flex right" >
            <Upload></Upload>
          </view>
        </u-form-item> -->
      </u--form>

      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="startTimeShow = false"
        :show="startTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('startTime', $event)"
        @cancel="startTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>
      <u-datetime-picker
        ref="datetimePicker"
        closeOnClickOverlay
        @close="endTimeShow = false"
        :show="endTimeShow"
        v-model="selfRegisterTime"
        mode="date"
        @confirm="pickConfirm('endTime', $event)"
        @cancel="endTimeShow = false"
        :formatter="formatter"
        :maxDate="nowDate"
        visibleItemCount="5"
        itemHeight="68"
      ></u-datetime-picker>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">查询</view>
  </view>
</template>

<script>
import Upload from './component/upload.vue';
export default {
  components: {
    Upload,
  },
  data() {
    return {
      nowDate: Number(new Date()),
      selfRegisterTime: Number(new Date()),
      model: {},
      startTimeShow: false,
      endTimeShow: false,
      columns: [],
      select: false,
      selectType: '',
      cjArr: [], // 车间Arr
      scxArr: [],
      scddArr: [],
      scgdArr: [],
      wlArr: [],
      bgztArr: [],
    }
  },
  onLoad() {
    this.listEnum1()
    this.listEnum2()
    this.initModel()
  },
  methods: {
    async listEnum1() {
      let params = { "enumName": "REPORT_PRODUCTREQUEST_STATE" }
      let res = await this.$service.submitWork.listEnum(params)
      this.bgztArr = res.data.map(item => ({
        value: item.enumValue,
        label: item.description,
      }))
    },
    async listEnum2() {
      let params = { "enumName": "WORK_SHOP_SECTION" }
      let res = await this.$service.submitWork.listEnum(params)
      this.cjArr = res.data.filter(i => i.enumValue !== 'C1').map(item => ({
        value: item.enumValue,
        label: item.description,
      }))
    },
    initModel() {
      this.model = {
        workShopSection: null, // 车间
        areaName: null, // 生产线
        workOrderName: null, // 生产订单
        productRequestName: null, // 生产工单
        productSpecName: null, // 物料
        reportProductRequestState: null, // 报工状态
        startTime: null, // 开始日期
        endTime: null, // 结束日期
      }
    },
    onSkip(type) {
      if (!this.model.workShopSection) {
        this.$Toast('请选择车间')
        return
      }
      if (type == 'workOrderName') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/selectPage?params=${JSON.stringify({
            selectType: type,
            list: this.scddArr,
          })}`,
          events: {
            acceptDataFromOpenedPage: (data) => {
              this.model[type] = data.workOrderName
            }
          }
        })
      }
      if (type == 'productRequestName') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/selectPage?params=${JSON.stringify({
            selectType: type,
            list: this.scgdArr,
          })}`,
          events: {
            acceptDataFromOpenedPage: (data) => {
              this.model[type] = data.productRequestName
            }
          }
        })
      }
      if (type == 'productSpecName') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/selectPage?params=${JSON.stringify({
            selectType: type,
            list: this.wlArr,
          })}`,
          events: {
            acceptDataFromOpenedPage: (data) => {
              this.model[type] = data.productSpecName
            }
          }
        })
      }
    },
    pickConfirm(type, e) {
      switch (type) {
        case 'startTime':
          this.model.startTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.startTimeShow = false
          break;
        case 'endTime':
          this.model.endTime = this.$dayjs(e.value).format('YYYY-MM-DD')
          this.endTimeShow = false
          break;
        default:
          break;
      }
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },

    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'workShopSection':
          this.columns = this.cjArr
          break;
        case 'areaName':
          this.columns = this.scxArr
          break;
        case 'reportProductRequestState':
          this.columns = this.bgztArr
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      if (this.selectType == 'workShopSection') {
        this.scxArr = []
        this.scgdArr = []
        this.wlArr = []
        this.scddArr = []
        this.model.areaName = ''
        this.model.productRequestName = ''
        this.model.productSpecName = ''
        this.model.workOrderName = ''
        let params = {
          workShopSection: this.model.workShopSection
        }
        this.$service.submitWork.listProductRequest(params).then(res => {
          this.scxArr = (res.data.areaNameList || []).map(i => ({ value: i, label: i }))  // 产线列表
          this.scgdArr = res.data.erpProductRequestList //工单列表
          this.wlArr = res.data.erpProductSpecList // 物料列表
          this.scddArr = res.data.erpWorkOrderList //订单列表
        })
      }
      this.select = false
    },
    submit() {
      if (!this.model.workShopSection) {
        this.$Toast('请选择车间')
        return
      }
      let startTime = new Date(this.model.startTime).getTime()
      let endTime = new Date(this.model.endTime).getTime()
      if (startTime && endTime && startTime > endTime) {
        return this.$Toast('开始时间不能大于结束时间')
      }
      let obj = {
        workShopSection: this.model.workShopSection, // 车间
        areaName: this.model.areaName, // 生产线
        workOrderName: this.model.workOrderName,  // 生产订单
        productRequestName: this.model.productRequestName,  // 生产工单
        productSpecName: this.model.productSpecName, // 物料
        reportProductRequestState: this.model.reportProductRequestState, // 报工状态
        startTime: this.model.startTime && `${this.model.startTime} 00:00:00`,
        endTime: this.model.endTime && `${this.model.endTime} 23:59:59`,
      }
      let workShopSectionDec = this.$utils.filterObjLabel(this.cjArr, this.model.workShopSection)
      if (this.model.workShopSection === 'C2') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/component/milddleDetail?params=${JSON.stringify(obj)}&workShopSectionDec=${workShopSectionDec}`
        })
      }
      if (this.model.workShopSection === 'C3') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/component/afterDetail?params=${JSON.stringify(obj)}&workShopSectionDec=${workShopSectionDec}`
        })
      }
      if (this.model.workShopSection === 'M' || this.model.workShopSection === 'P') {
        uni.navigateTo({
          url: `/pages/cellFront/submitWork/component/packDetail?params=${JSON.stringify(obj)}&workShopSectionDec=${workShopSectionDec}&workShopSection=${this.model.workShopSection}`
        })
      }
    }

  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
