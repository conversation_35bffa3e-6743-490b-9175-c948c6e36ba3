<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="搜索类型" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('searchType')">
            <view v-if="model.searchType">{{ $utils.filterObjLabel(dicts.searchTypeList, model.searchType) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'searchType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="订单" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('workOrderName')">
            <view v-if="model.workOrderName">{{ $utils.filterObjLabel(dicts.workOrderNameList, model.workOrderName) }}
            </view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'workOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="工单" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('productOrderName')">
            <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList,
              model.productOrderName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="条码" borderBottom labelWidth="100">
          <u--input v-model="model.batchIdList" border="none" placeholder="请扫描或输入设备编号"></u--input>
          <view class="iconfont icon-saoma" @click="scan('batchIdList')"></view>
        </u-form-item>

        <u-form-item label="开始日期" labelWidth="150" @click="startTimeShow = true" borderBottom>
          <u-input v-model="model.beginDate" disabled disabledColor="#fff" placeholder="请选择开始日期" border="none" />
          <u-icon slot="right" name="arrow-down"
            :style="{ transform: startTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>

        <u-form-item label="结束日期" labelWidth="150" @click="endTimeShow = true">
          <u-input v-model="model.endDate" disabled disabledColor="#fff" placeholder="请选择结束日期" border="none" />
          <u-icon slot="right" name="arrow-down"
            :style="{ transform: endTimeShow ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }"></u-icon>
        </u-form-item>
      </u--form>
      <u-datetime-picker ref="datetimePicker" closeOnClickOverlay @close="startTimeShow = false" :show="startTimeShow"
        v-model="selfRegisterTime" mode="date" @confirm="pickConfirm('beginDate', $event)"
        @cancel="startTimeShow = false" :formatter="formatter" :maxDate="nowDate" visibleItemCount="5"
        itemHeight="68"></u-datetime-picker>
      <u-datetime-picker ref="datetimePicker" closeOnClickOverlay @close="endTimeShow = false" :show="endTimeShow"
        v-model="selfRegisterTime" mode="date" @confirm="pickConfirm('endDate', $event)" @cancel="endTimeShow = false"
        :formatter="formatter" :maxDate="nowDate" visibleItemCount="5" itemHeight="68"></u-datetime-picker>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
        @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">查询</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
import useNls from "@/mixins/useNls";

export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    this.changebatchIdList = this.$debounce(this.changebatchIdList, 1000)
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        searchTitle: '查询结果'
      },
      dicts: {
        productOrderNameList: [], // 工单编码
        workOrderNameList: [], // 订单编码
        searchTypeList: [], // 搜索类型
        scanLotTypeList: [], // 条码类型


        lotGradeList: [],  // 品质
        lotHoldStateList: [], // 拦截
        lotTrackStateList: [], // 条码状态字典
        lotEventNameList: [], // 事件名字段
        disposalNgStateList: [], // 不合格处理措施
      },

      nowDate: Number(new Date()),
      selfRegisterTime: Number(new Date()),
      startTimeShow: false,
      endTimeShow: false,
      model: {},
      columns: [],
      select: false,
      selectType: '',
    }
  },
  watch: {
    'model.batchIdList': {
      handler(val) {
        this.changebatchIdList(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.getEnumValue('SearchType', 'searchTypeList') // 搜索类型
    this.getEnumValue('ScanLotType', 'scanLotTypeList') // 条码类型

    // this.getEnumValue('LotGrade', 'lotGradeList')
    // this.getEnumValue('LotHoldState', 'lotHoldStateList')
    // this.getEnumValue('LotTrackState', 'lotTrackStateList')
    // this.getEnumValue('LotEventName', 'lotEventNameList')
    // this.getEnumValue('DisposalNgState', 'disposalNgStateList')

    this.init_workOrderNameList() // 工单编码
    this.init_productOrderNameList() // 工单编码
    this.initModel()

  },

  methods: {
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'searchType':
          this.columns = this.dicts.searchTypeList
          break;
        case 'workOrderName':
          this.columns = this.dicts.workOrderNameList
          break;
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },
    changebatchIdList(value) {
      if (!value) return
    },
    initModel() {
      this.model = {
        statementType: '2', // "1-正向, 2-反向",
        searchType: '1', // 搜索类型
        batchIdList: '',
        productOrderName: '', // 工单编码
        workOrderName: '', // 订单编码
        beginDate: '', //  查询开始时间
        endDate: '', // 查询结束时间
      }
    },
    pickConfirm(type, e) {
      switch (type) {
        case 'beginDate':
          this.model.beginDate = this.$dayjs(e.value).format('YYYY-MM-DD 00:00:00')
          this.startTimeShow = false
          break;
        case 'endDate':
          this.model.endDate = this.$dayjs(e.value).format('YYYY-MM-DD 00:00:00')
          this.endTimeShow = false
          break;
        default:
          break;
      }
    },

    submit() {
      let beginDate = new Date(this.model.beginDate).getTime()
      let endDate = new Date(this.model.endDate).getTime()
      if (beginDate && endDate && beginDate > endDate) {
        return this.$Toast('开始时间不能大于结束时间')
      }
      if (['1', '2'].includes(this.model.searchType)) {
        if (_.isEmpty(this.model.batchIdList)) {
          return this.$Toast('【条码类型,物料类型】查询方式，条码不能为空')
        }
      } else {
        if (!this.model.workOrderName && !this.model.productOrderName) {
          return this.$Toast('【订单或工单】查询方式， 请输入订单或工单!')
        }
      }
      let obj = {
        ...this.model
      }
      // statementType
      // "1-正向, 2-反向"
      uni.navigateTo({
        url: `/pages/ProductionExecution/PDARetrospectiveReport/search?params=${JSON.stringify(obj)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}&statementType=2`,
      })
    },

    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },

    init_workOrderNameList() {
      this.$service.common.getDictByQueryId('WorkOrder_listJustWorkOrderName').then(res => {
        this.dicts.workOrderNameList = res.datas.map(item => ({
          label: item.workOrderName,
          value: item.workOrderName,
        }))
      })
    },

    init_productOrderNameList() {
      this.$service.common.getDictByQueryId('ProducOrder_listJustProducOrder').then(res => {
        this.dicts.productOrderNameList = res.datas.map(item => ({
          label: item.productOrderName,
          value: item.productOrderName,
        }))
      })
    },

    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'batchIdList':
          this.model.batchIdList = 'C1Z001002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
