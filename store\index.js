import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex);

const store = new Vuex.Store({
	state: {
		// contextInfo: {},
		// permissionList: [],
		UnqualifiedEntryList: [], // 不合格录入
		InspectionSampleList: [], // 取样明细 InspectionSampling/sampleList


		SemiRecordList: [],
		SemiDeterminationList: [],
		FinshSampleList: [],
		WorkingProcessAdjustmentList: []
	},
	mutations: {
		// setContextInfo(state, contextInfo) {
		// 	state.contextInfo = contextInfo;
		// },
		setUnqualifiedEntryList(state, UnqualifiedEntryList) {
			state.UnqualifiedEntryList = UnqualifiedEntryList;
		},
		setInspectionSampleList(state, InspectionSampleList) {
			state.InspectionSampleList = InspectionSampleList;
		},
		// changePermissions(state, permissionList) {
		// 	state.permissionList = permissionList
		// },
		// clearPermissions(state) {
		// 	state.permissionList = null
		// },
		changeSemiRecordList(state, SemiRecordList) {
			Vue.set(state, 'SemiRecordList', SemiRecordList);
		},
		changeSemiDeterminationList(state, SemiDeterminationList) {
			Vue.set(state, 'SemiDeterminationList', SemiDeterminationList);
		},
		changeFinshSampleList(state, FinshSampleList) {
			Vue.set(state, 'FinshSampleList', FinshSampleList);
		},
		changeWorkingProcessAdjustmentList(state, WorkingProcessAdjustmentList) {
			Vue.set(state, 'WorkingProcessAdjustmentList', WorkingProcessAdjustmentList);
		},
	},
	getters: {
		// hasPermission: (state) => (permission) => {
		// 	if (permission) {
		// 		return state.permissionList.includes(permission)
		// 	}
		// 	return false
		// },
	},
	actions: {
		// setContextInfo(context) {
		// 	context.commit('setContextInfo');
		// },
		changeSemiRecordList(context, val) {
			context.commit('changeSemiRecordList', val)
		},
		changeSemiDeterminationList(context, val) {
			context.commit('changeSemiDeterminationList', val)
		},
		changeFinshSampleList(context, val) {
			context.commit('changeFinshSampleList', val)
		},
		changeWorkingProcessAdjustmentList(context, val) {
			context.commit('changeWorkingProcessAdjustmentList', val)
		}
	}
});

export default store
