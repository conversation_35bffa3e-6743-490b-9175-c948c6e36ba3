<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="报工入库详情" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex between h40 hcenter c_999">
        <view>车间</view>
        <view>{{ workShopSectionDec }}</view>
      </view>
      <view class="flex between h40 hcenter c_999">
        <view>生产线</view>
        <view>{{ model.areaName }}</view>
      </view>
    </view>
    <view class="myContainer ma10">
      <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
        <view class="flex between h40 hcenter c_999">
          <view>生产工单</view>
          <view>{{ ele.productRequestName }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>物料</view>
          <view>{{ ele.productSpecName }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>物料描述</view>
          <view>{{ ele.productSpecDesc }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>报工状态</view>
          <view>{{ ele.reportProductRequestStateValue }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>完工量</view>
          <view>{{ ele.finishedQuantity || 0 }}</view>
        </view>
        <view class="flex h40 hcenter c_999">
          <view class="flex flex1 left">
            <view class="mr5">良品数量</view>
            <view>{{ ele.assignedQuantity || 0 }}</view>
          </view>
          <view class="flex flex1 center">
            <view class="mr5">报废数量</view>
            <view>{{ ele.scrappedQuantity || 0 }}</view>
          </view>
          <view class="flex flex1 right">
            <view class="mr5">待定数量</view>
            <view>{{ ele.waitQuantity || 0 }}</view>
          </view>
        </view>
        <view>
          <!-- <u-button  v-if="ele.reportProductRequestState === 'ALREADY_REPORT'" color="gray" disabled text="不可录入"></u-button> -->
          <u-button  type="success" text="录入报工" @click="summit(ele)"></u-button>
        </view>
      </view>
      <NoData v-if="!list || list.length === 0"></NoData>
      <u-modal :show="showModal" confirmText="确认" @confirm="changeConfirm" @cancel="modalCancel" :showCancelButton="true">
        <view style="width: 100%">
          <u--form labelPosition="left" :model="formModel" labelWidth="100" ref="form">
            <u-form-item label="良品数" borderBottom required labelWidth="100">
              <u--input v-model="formModel.assignedQuantity" border="none" type="number" placeholder="请输入良品数"></u--input>
            </u-form-item>
            <u-form-item label="待定数" borderBottom labelWidth="100">
              <u--input v-model="formModel.waitQuantity" border="none" type="number" placeholder="请输入待定数"></u--input>
            </u-form-item>
            <u-form-item label="报废数" borderBottom labelWidth="100">
              <u--input v-model="formModel.scrappedQuantity" border="none" type="number" placeholder="请输入报废数"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </u-modal>
    </view>
    <!-- <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view> -->
  </view>
</template>


<script>
import { USER_ID } from '@/utils/common/evtName.js'
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      model: {},
      workShopSectionDec: '',  //车间
      formModel: {}, // 表单
      selectItem: {}, // 选中项
      list: [], // 列表数据
      showModal: false
    };
  },

  onLoad(e) {
    this.model = e && JSON.parse(e.params)
    this.workShopSectionDec = e && e.workShopSectionDec
    this.getList()
  },
  methods: {
    getList() {
      let params = JSON.parse(JSON.stringify(this.model))
      this.$service.submitWork.detail(params).then(res => {
        this.list = res.data
      })
    },
    summit(ele) {
      this.selectItem = ele
      this.showModal = true
    },
    initFormModel() {
      this.formModel = {
        assignedQuantity: null,
        waitQuantity: null,
        scrappedQuantity: null,
      }
    },
    changeConfirm() {
      if (!this.formModel.assignedQuantity) {
        return this.$Toast('请输入良品数')
      }
      let sum = 0
      const reg = /^\d+$/
      for (const key in this.formModel) {
        const element = Number(this.formModel[key])
        if (!reg.test(element) && element) {
          this.$Toast('请输入正整数')
        }
        sum += element
      }
      if(!this.selectItem.lineSideWareHouse) {
        this.$Toast(`该产线[${this.list.areaDesc}]没有维护线边仓，请联系管理员配置`)
        return
      }
      let params = {
        erpOwlProductRequestList: [
          {
            workOrderName: this.selectItem.workOrderName,
            productRequestName: this.selectItem.productRequestName,
            lineSideWareHouse: this.selectItem.lineSideWareHouse,
            productSpecName: this.selectItem.productSpecName,
            ...this.formModel,
            unit: this.selectItem.unit,
            reportQuantity: sum
          }
        ],
        operateValue: '10001',
        userId: this.$getLocal(USER_ID)
      }
      // return
      // let selectIndex = this.list.findIndex(i => i.productRequestName === this.selectItem.productRequestName)
      // Object.assign(this.list[selectIndex], this.formModel)
      this.$service.submitWork.erpOwlProductRequest(params).then(res => {
        this.modalCancel()
        this.$Toast('录入成功')
        setTimeout(() => {
          this.getList()
        }, 1000);
      })
    },
    // 模态框取消
    modalCancel() {
      this.showModal = false
      this.initFormModel()
    },

    // initSearchModel(val) {
    //   this.searchModel = {
    //     pageNo: this.pageNumber,
    //     limit: this.pageSize,
    //   }
    // },
    // getData(clearOldData = false, refresh = false) {
    //   clearOldData && (this.pageNumber = 1)
    //   refresh && (this.model = [])
    //   this.searchModel.pageNo = this.pageNumber
    //   this.searchModel.limit = this.pageSize
    //   setTimeout(() => {
    //     // let res = Array.from({ length: 20 }, (v, i) => i)
    //     let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    //     this.model = clearOldData ? res : [...this.model, ...res]
    //     this.refresherTriggered = false
    //     if (this.model.length > 30) {
    //       this.status = 'nomore'
    //     } else {
    //       this.status = 'loadmore'
    //     }
    //   }, 1000)
    //   return
    //   let params = JSON.parse(JSON.stringify(this.searchModel))
    //   this.$service.roller.outQueryPageList(params).then((res) => {
    //     if (res && res.success) {
    //       let { simpleTrackProductRequest, trackProductRequestPage } = res.data
    //       this.simpleTrackProduct = simpleTrackProductRequest

    //       this.model = clearOldData ? trackProductRequestPage.records : [...this.model, ...trackProductRequestPage.records]
    //       if (this.searchModel.pageNo == trackProductRequestPage.pages) {
    //         this.status = 'nomore'
    //       } else {
    //         this.status = 'loadmore'
    //       }
    //       this.refresherTriggered = false
    //     }
    //   }).catch((e) => {
    //     this.refresherTriggered = false
    //   })
    // },
  },
};
</script>


<style lang="scss" scoped>
@import "@/styles/uform.scss";
@import "@/styles/publicStyle.scss";
</style>