<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切卸卷" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="产品描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="170">
          <view class="w100x flex right"> {{ model.poleRollName }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.poleRollProductOrderName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(片)" borderBottom labelWidth="130">
          <!-- <view class="w100x flex right"> {{ model.mountQuantity }} </view> -->
          <view v-if="isDisabled">
            <view class="w100x flex right"> {{ model.mountQuantity }} </view><span class="lin1 ml2">m</span> <span>{{ getShowEa(model.mountQuantity) }}</span
            ><span class="lin1 ml2">ea</span>
          </view>
          <view class="w100x flex right" v-else> {{ model.mountQuantity }} </view>
        </u-form-item>

        <u-form-item label="上卷作业员" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.mountUser }} </view>
        </u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.mountTime }} </view>
        </u-form-item>

        <u-form-item label="已消耗数量(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.consumedQuantity }} </view>
        </u-form-item>

        <u-form-item label="不良计数器(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.ngQuantity }} </view>
        </u-form-item>

        <u-form-item label="人工确认(片)" borderBottom required labelWidth="130">
          <!-- <view class="w100x flex right" v-if="!uesrConfirmFlag"> {{ model.uesrConfirm }}</view> -->
          <u--input placeholder="请输入数量(片)" v-model="model.uesrConfirm" border="none"></u--input>
          <!-- <u--input readonly placeholder="请输入数量(片)" v-model="model.uesrConfirm" border="none"></u--input> -->
        </u-form-item>

        <u-form-item label="报废分类记录(片)" borderBottom required labelWidth="150">
          <view class="w100x flex right"> {{ model.scrapQuantity }} </view>
          <u-icon @click="gotoQuery(1)" class="ml12 mr12" size="30" name="edit-pen"></u-icon>
          <u-icon @click="gotoQuery(2)" size="30" name="close"></u-icon>
        </u-form-item>

        <u-form-item label="剩余数量(片)" borderBottom required labelWidth="130">
          <u--input type="number" placeholder="请输入数量(片)" v-model="model.remainingQuantity" border="none"></u--input>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>

    <view class="btnContainer2">
      <view @click="readComputedQty">读取数量</view>
      <view class="btnContainer" @click="submit">卸卷</view>
    </view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        uesrConfirm: '人工确认不能为空',
        scrapQuantity: '报废分类记录不能为空',
        remainingQuantity: '剩余数量不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      isDisabled: false,
      ratio: null,
      show: false,
      content: '',
      uesrConfirmFlag: false
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  onShow() {
    console.log('onShow', this.model.machineName);
    // if(this.model.machineName) {
    //   this.changeMachineName(this.model.machineName)
    // }
  },
  methods: {
    gotoQuery(type) {
      if (!this.model.machineName) return this.$Toast('设备号不能为空！')
      if (type == 1) {
        if (!this.model.machineName) return this.$Toast('设备不能为空！')
        uni.navigateTo({
          url: `/pages/DieCutting/Unroll/modules/UnrollAdd?machineName=${this.model.machineName}`,
        })
      } else {
        if (!this.model.machineName) return this.$Toast('设备不能为空！')
        uni.navigateTo({
          url: `/pages/DieCutting/Unroll/modules/UnrollDelete?machineName=${this.model.machineName}`,
        })
      }
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备
        consumedQuantity: null, //已消耗数量	integer(int32)	
        description: null, //	设备描述	string	
        eventUser: null, //		string	
        factoryName: null, //	string	
        messageName: null, //	string	
        messagePath: null, //	string	
        mountQuantity: null, //上卷数量	integer(int32)
        mountTime: null, //上卷时间	string(date-time)	
        mountUser: null, //	上卷操作员	string	
        ngQuantity: null, //	不良计数	integer(int32)	
        poleRollName: null, //极卷名	string	
        poleRollProductOrderName: null, //	极卷工单	string	
        processOperationName: null, //	工序名	string	
        productSpecDesc: null, //	产品描述	string	
        productSpecName: null, //产品编码	string	
        remainingQuantity: null, //	剩余数量	integer(int32)	
        returnCode: null, //	string	
        returnMessage: null, //	string	
        scrapQuantity: null, //	报废分类记录	integer(int32)	
        transactionId: null, //	string	
        uesrConfirm: null, //	人工确认
      }
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.saveNo = false
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.saveNo = true
      this.select = false
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.remainingQuantity > this.model.mountQuantity) {
        return this.$Toast('剩余数量不能大于上卷数量！')
      } else if (this.model.remainingQuantity < 0) {
        return this.$Toast('剩余数量不能小于0！')
      }

      uni.showModal({
        title: '提示',
        content: `设备${this.model.machineName}，现卸极卷${this.model.poleRollName},剩余数量：${this.model.remainingQuantity}，请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              ...this.model
            }
            this.$service.DieCutting.DieCuttingUnMountRollingMsgProcessor(params).then(res => {
              this.$Toast('卸卷成功')
              this.initModel()
            })
          }
          if (res.cancel) { }
        },
      })
    },
    cancel() {
      this.show = false
    },
    confirm() {
      this.show = false
      this.uesrConfirmFlag = true
    },
    readComputedQty() {
      if (!this.model.machineName) return this.$Toast('设备不能为空！')
      let params = {
        machineName: this.model.machineName
      }
      this.$service.DieCutting.readMachineGoodQuality(params).then(res => {
        console.log('readMachineGoodQuality', res);
        const { ngQuantity } = res.datas[0]
        if (ngQuantity) {
          this.$set(this.model, 'ngQuantity', ngQuantity)
          this.$set(this.model, 'uesrConfirm', ngQuantity)
          this.$Toast('读取成功！')
        } else {
          this.show = true
          this.content = `设备计数未采集到, 请确认是否由人工录入?`
        }
      })
    },
    /* 设备号 */
    changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      this.$service.DieCutting.getFinishDataByMachineName(params).then(res => {
        if (res.datas.length > 0) {
          this.model = res.datas[0]
          const data = {
            processOperationName: this.model.processOperationName,
            productSpecName: this.model.productSpecName
          }
          this.$service.common.findAllWithEquals(data).then(res => {
            if (res.datas.length > 0) {
              this.ratio = Number(res.datas[0].ratio)
              this.isDisabled = true
            } else {
              this.isDisabled = false
            }
          })
        } else {
          this.model.machineName = ''
          this.$Toast('设备不存在！')
        }
      }).catch(() => {
        this.model.machineName = ''
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'GF20-01-TBD01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
    // 单位转换
    changeNumber2(e, index) {
      e = e && (e.match(/^\d*(\.?\d{0,3})/g)[0])
      if (this.ratio) {
        // this.singlerRollFinishParames.forEach((item, i) => {
        //   if (i == index) {
        //     item.outPutByUser.lossCountEA = Math.round(Number(item.outPutByUser.lossCountM) * this.ratio)
        //     this.$nextTick(() => {
        //       this.$set(item.outPutByUser, 'lossCountM', e)
        //     })
        //   }
        // })
      }
    },
    // ea控制整数
    changeEA2(e, index) {
      let str = e && e + ""
      let result = str && (str.match(/^\d*/g)[0])
      this.singlerRollFinishParames.forEach((item, i) => {
        // if (i == index) {
        //   this.$nextTick(() => {
        //     this.$set(item.outPutByUser, 'lossCountEA', result)
        //   })
        // }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
