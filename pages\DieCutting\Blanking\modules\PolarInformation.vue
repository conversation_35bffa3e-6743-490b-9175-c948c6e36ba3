<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="极卷信息查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>极卷条码</view>
              <view>{{ ele.lotName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上卷数量(片)</view>
              <view>{{ ele.createQuantity }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>工单号</view>
              <view class="flex hcenter">
                <view>{{ ele.productOrderName }}</view>
              </view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上卷作业员</view>
              <view class="flex hcenter">
                <view>{{ ele.createUser }}</view>
              </view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上卷时间</view>
              <view class="flex hcenter">
                <view>{{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      processOperationName: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.processOperationName = e && e.processOperationName
    this.getData()
  },
  methods: {
    moment,
    getData() {
      const params = {
        machineName: this.machineName,
        processOperationName: this.processOperationName
      }
      this.$service.DieCutting.DieCuttingPoleRoll(params).then((res) => {
        if (res && res.success) {
          this.model = res.datas
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>