<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="上料明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer pa10">
      <scroll-view class="h100x " scroll-y  :scroll-top="scrollTop" @scroll="onScroll"  @scrolltolower="lower" refresher-background="#f3f3f7" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(item, index) in model" :key="index">
            <view class="pb10 bb_999_dashed c_00b17b">
              <view class="lin30">
                <text class="mr10">位置：</text>
                <text>{{ item.materialPosition }}</text>
              </view>
              <view class="lin30">
                <text class="mr10">材料：</text>
                <text>{{ item.consumableSpecName }} / {{ item.consumableSpecDesc }}</text>
              </view>
            </view>
            <view class="ele_consumable" v-for="(ele, eleIndex) in item.pdaConsumableItemList" :key="eleIndex">
              <view class="flex between h40 hcenter c_999">
                <view>标签条码</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料数量</view>
                <view>{{ ele.quantity }}  {{ ele.consumeUnit }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料时间</view>
                <view>{{ ele.lastEventTime }}</view>
              </view>
              <!-- <view>
                <u-button type="success" text="卸载" @click="uninstall(ele)"></u-button>
              </view> -->
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'MesTworkPdaDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName:'',
    };
  },
  onLoad(e) {
    this.machineName = e && e.machineName
    this.initSearchModel()
    this.getData()
  },
  methods: {
   
    initSearchModel() {
      this.searchModel = {
        machineName: this.machineName,
        pageNo: this.pageNumber,
        limit: this.pageSize,
      }
    },
    // uninstall(item) {
    //   console.log(item)
    // },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      // setTimeout(() => {
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return
      let params = JSON.parse(JSON.stringify(this.searchModel))
      await this.$service.materialFeeding.queryConsumableItemPageList(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.data.records : [...this.model, ...res.data.records]
          console.log('res.data.pages',res.data.pages, this.searchModel.pageNo);
          if (this.searchModel.pageNo == res.data.pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
          // uni.stopPullDownRefresh()
        }
      }).catch((e) => {
        this.refresherTriggered = false
        // uni.stopPullDownRefresh()
      })
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../styles/publicStyle.scss";

.ele_consumable {
  &:not(:last-child) {
    border-bottom: 1px dashed #999999;
  }
}
.touch {
  touch-action: none;
}
</style>