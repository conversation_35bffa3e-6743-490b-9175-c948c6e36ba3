<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="产线编码" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('areaName')">
            <view v-if="model.areaName">{{ $utils.filterObjLabel(dicts.areaNameList, model.areaName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'areaName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="工段" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('workShopSection')">
            <view v-if="model.workShopSection">{{ $utils.filterObjLabel(dicts.workShopSectionList, model.workShopSection) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'workShopSection' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="工单" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('productOrderName')">
            <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList, model.productOrderName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import _ from "lodash";
export default {
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },


      rulesTip: {
        areaName: '产线编码不能为空',
        workShopSection: '工段不能为空', // 
        productOrderName: '工单编码不能为空', // 
      },
      model: {},


      columns: [],
      select: false,
      selectType: '',
      dicts: {
        areaNameList: [], // 产线
        workShopSectionList: [], // 工段
        productOrderNameList: [], // 工单
      },
    }
  },
  computed: {},
  watch: {},
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.lineTypeIndexTitle // 标题
    this.nlsMap = nlsMap

    this.initModel()
    this.GetAreaNameList()
  },
  methods: {
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'areaName':
          this.columns = this.dicts.areaNameList
          break;
        case 'workShopSection':
          this.columns = this.dicts.workShopSectionList
          break;
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'areaName') {
        this.model.workShopSection = ''
        this.model.productOrderName = ''
        this.dicts.workShopSectionList = []
        this.dicts.productOrderNameList = []

        let data = {
          areaName: this.model.areaName,
        }
        let res = await this.$service.SetProductionModel.GetWorkShop(data)
        this.dicts.workShopSectionList = res.datas.map(item => ({
          label: item.workShopText,
          value: item.workShop,
        }))


      }
      if (this.selectType == 'workShopSection') {
        this.model.productOrderName = ''
        this.dicts.productOrderNameList = []
        let data = {
          areaName: this.model.areaName,
          machineTaskType: 'AreaChangeTask',
          workShopSection: this.model.workShopSection,
        }
        let res = await this.$service.SetProductionModel.listProductOrder(data)
        this.dicts.productOrderNameList = res.datas[0].productOrderList.map((item) => ({
          label: `${item.productOrderName}/${item.productSpecText}`,
          value: item.productOrderName,
        }))
        // "productOrderName": "工单编码",
        //   "productSpecName": "产品编码"
        // "productSpecText": "产品名称"
      }
    },
    GetAreaNameList() {
      this.$service.common.getDictByQueryId('MachineTask_GetAreaName').then(res => {
        this.dicts.areaNameList = res.datas.map(item => ({
          label: item.areaText,
          value: item.areaName,
        }))
      })
    },

    initModel() {
      this.model = {
        areaName: '', //产线编码
        workShopSection: '',// 工段
        productOrderName: '', //工单编码
      }
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        machineTaskType: 'AreaChangeTask',
        areaName: this.model.areaName,
        workShopSection: this.model.workShopSection,
        productOrderName: this.model.productOrderName,
      }
      this.$service.SetProductionModel.machineTaskControllerAdd(params).then(res => {
        this.$Toast('操作成功!')
        this.initModel()
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
