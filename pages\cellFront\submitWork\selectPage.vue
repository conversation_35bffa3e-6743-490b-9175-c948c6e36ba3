<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="title" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <!-- <view class="flex hcenter bc_fff h40 pl10 h50">
      <u-search placeholder="请输入名称" v-model="keyword" @search="search" clearabled @custom="search"></u-search>
    </view> -->
    <view class="myContainer ma10">
      <!-- <scrollPage :requireUrlKey="requireUrlKey" :otherParams="otherParams" v-slot="{ model }">
      </scrollPage> -->
      <!-- <view class="mb10 br10 bc_fff pa10 pl20" v-for="(ele, index) in model" :key="index" @click="submit(ele)">
        {{ ele }}
      </view> -->
      <template v-if="selectType === 'workOrderName'">
        <view class="mb10 br10 bc_fff pa10 pl20" v-for="(ele, index) in model" :key="index" @click="submit(ele)">
          <view class="flex between h40 hcenter c_999">
            <view>生产订单</view>
            <view>{{ ele.workOrderName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>物料</view>
            <view>{{ ele.productSpecName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>描述</view>
            <view>{{ ele.productSpecDesc }}</view>
          </view>
        </view>
      </template>

      <template v-if="selectType === 'productRequestName'">
        <view class="mb10 br10 bc_fff pa10 pl20" v-for="(ele, index) in model" :key="index" @click="submit(ele)">
          <view class="flex between h40 hcenter c_999">
            <view>生产工单</view>
            <view>{{ ele.productRequestName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>物料</view>
            <view>{{ ele.productSpecName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>描述</view>
            <view>{{ ele.productSpecDesc }}</view>
          </view>
        </view>
      </template>

      <template v-if="selectType === 'productSpecName'">
        <view class="mb10 br10 bc_fff pa10 pl20" v-for="(ele, index) in model" :key="index" @click="submit(ele)">
          <view class="flex between h40 hcenter c_999">
            <view>物料</view>
            <view>{{ ele.productSpecName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>描述</view>
            <view>{{ ele.productSpecDesc }}</view>
          </view>
        </view>
      </template>

      <NoData v-if="!model || model.length === 0"></NoData>
    </view>
  </view> 
</template>


<script>
import NoData from '@/components/NoData/noData'
import scrollPage from './component/scrollPage.vue';
const config = {
  'workOrderName': '生产订单预览',
  'productRequestName': '生产工单预览',
  'productSpecName': '物料预览',
}
export default {
  name: 'rollerConfirmDetail',
  components: {
    scrollPage,
    NoData
  },
  data() {
    return {
      selectType: '',
      title: ''
    };
  },
  onLoad(e) {
    let option = e && JSON.parse(e.params)
    console.log('option',option);
    this.selectType = option.selectType
    this.title = config[this.selectType]
    this.model = option.list
    // this.model = Array.from({ length: 20 }, (v, i) => ({label:`生产线${i}`,value:i}))
  },
  methods: {
    submit(val) {
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('acceptDataFromOpenedPage', val);
      uni.navigateBack();
    },
    search(val) {
    }
  },
};
</script>


<style lang="scss" scoped>
@import "../../../styles/publicStyle.scss";
</style>