<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="在制品绑定与解绑载具" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="载具" borderBottom required labelWidth="100">
          <u--input v-model="model.durableName" border="none" focus placeholder="请扫描载具二维码" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <u-form-item label="载具名称" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.durableName }} </view>
        </u-form-item>

        <u-form-item label="载具类型" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.durableType }} </view>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + ' / ' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="后序工序" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.processOperationName ? model.processOperationName + ' / ' + model.processOperationDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="槽位号" borderBottom required labelWidth="150">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.slot" @input="changeSlot($event)" border="bottom" type="number" placeholder="请输入"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="在制品条码" borderBottom required labelWidth="170">
          <!-- {{model.lotName}} -->
          <template v-if="model.slot">
            <u--input v-model="model.lotName" border="none" placeholder="请扫描在制品条码"></u--input>
            <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
          </template>
        </u-form-item>

        <u-form-item label="满载数量" borderBottom labelWidth="130">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.capacity" border="none" readonly type="number"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="已绑数量" labelWidth="100">
          <view class="w100x flex right"> {{ model.lotQuantity }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <!-- <view class="btnContainer" @click="clearAction">清空载具</view> -->

    <!-- <view class="btnContainer" @click="allClearAction">一键解绑</view> -->
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {},
      columns: [],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      list: []
    }
  },
  watch: {
    'model.durableName': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.lotName': {
      handler(res) {
        console.log(res, 'resresresres')
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    this.initModel()

  },
  methods: {
    moment,
    changeSlot(e) {
      this.model.lotName = ''
      let str = e && e + ""
      let result = str && (str.match(/^\d*/g)[0])
      if (result >= 49) {
        this.$Toast('槽位号不能大于48！')
        this.$nextTick(() => {
          this.$set(this.model, 'slot', '')
        })
        return
      }
      this.$nextTick(() => {
        this.$set(this.model, 'slot', result)
      })
    },

    allClearAction() {
      if (!this.model.durableName) {
        this.$Toast('载具不能为空！')
      }
      let lotIDList = this.list.map(item => {
        return {
          lotID: item.lotName
        }
      })
      let params = {
        factoryName: 'GFAJ07',
        lotIDList: lotIDList,
        stack: this.model.durableName
      }
      this.$service.carrierIsBind.deAssignStack(params).then(res => {
        this.$Toast('一键解绑成功！')
        this.changeDurableName(this.model.durableName)
        // setTimeout(() => {
        //   this.GetProductListByDurableName(this.model.durableName)
        // }, 800);
      })
    },
    async GetProductListByDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        durableName: value
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductListByDurableName(params)
        if (res.datas.length > 0) {
          this.list = res.datas
        } else {
          this.list = []
        }
      } catch (error) {
      }
    },
    clearAction() {
      if (!this.model.durableName) {
        this.$Toast('载具不能为空！')
      }
      params = {
        durableName: this.model.durableName,
      }
      this.$service.carrierIsBind.assignStack(params).then(res => {
        this.$Toast('清空载具成功！')
      })
    },
    gotoQuery() {
      if (!this.model.durableName) {
        return this.$Toast('载具不能为空！')
      }
      // if (!this.model.lotQuantity) {
      //   return this.$Toast('绑数量为0')
      // }
      let params = JSON.stringify({
        ...this.model,
      })
      uni.navigateTo({
        url: `/pages/BindingAndUnbinding/modules/ProductList?params=${params}`,
        events: {
          acceptDataFromOpenedPage: (data) => {
            this.changeDurableName(this.model.durableName)
          }
        }
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        durableName: '', //	载具	string	
        durableType: '', //	载具类型	string	
        productSpecDesc: '', //产品描述	string	
        productSpecName: '', //产品编码	string	
        capacity: '', //	满载数量	number	
        lotName: '', // 在制品条码	string(date-time)	
        lotQuantity: '', // 已绑数量	number	
        processOperationName: '', //	工序	string	
        processOperationDesc: '', //	工序描述	string	
        lotGrade: '', // 载具状态
        slot: '', //  曹位号
      }
    },
    submit(value, type) {
      let params = {}
      let formattedTimestamp = moment().format('YYYYMMDDHHmmssSSS');
      let randomNum = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10)).join('')
      let transId = `${formattedTimestamp}.${randomNum}`
      let eventTime = moment().format('YYYY-MM-DD HH:mm:ss')
      params = {
        container: this.model.durableName,
        containerType: "1",
        eventTime: eventTime,
        eventType: "U",
        eventUser: "",
        eventUser: this.$getLocal(USER_ID),
        factoryNo: "10011",
        flag: type == '绑定' ? "1" : '2',
        lineName: "",
        serialNoList: [
          {
            "serialno": this.model.lotName,
            "slot": this.model.slot
          }
        ],
        transId: transId,
        workShopSection: ""
      }
      this.$service.carrierIsBind.ContainerBindOrUnBind(params, { handleSucess: true }).then(res => {
        console.log('res', res)
        if (res.resultCode == 0) {
          this.model.lotName = ''

          if (Number(this.model.slot) + 1 >= 49) {
            this.model.slot = 1
          } else {
            this.model.slot = Number(this.model.slot) + 1
          }
          this.$Toast('绑定成功！')
          this.changeDurableName(this.model.durableName)
        } else {
          this.$Toast(res.resultMsg)
          this.model.lotName = ''
        }
      })
      // params = {
      //   flag: "2",
      //   factoryName: '10011',
      //   lotIDList: [{ lotID: value }],
      //   stack: this.model.durableName,
      // }
      // this.$service.carrierIsBind.deAssignStack(params).then(res => {
      //   this.$Toast('解绑成功！')
      //   this.changeDurableName(this.model.durableName)
      // })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 载具 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        durableName: value
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByDurableName(params)
        if (res.datas.length > 0) {
          this.model.durableName = res.datas[0].durableName
          this.model.capacity = res.datas[0].capacity
          this.model.durableType = res.datas[0].durableType
          this.model.productSpecName = res.datas[0].productSpecName
          this.model.productSpecDesc = res.datas[0].productSpecDesc
          this.model.lotQuantity = res.datas[0].lotQuantity
          this.model.processOperationName = res.datas[0].processOperationName
          this.model.processOperationDesc = res.datas[0].processOperationDesc
          this.model.lotGrade = res.datas[0].lotGrade
          this.GetProductListByDurableName(this.model.durableName)
        } else {
          this.model.durableName = ''
          this.$Toast('未找到载具信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      try {
        let params = {
          lotName: value,
        }
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]

          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            if (productData.carrierName) {
              // this.submit(value, '解绑')
              this.model.lotName = ''
              this.$Toast(`在制品条码已绑定载具！`)
              return
            } else {
              if (productData.lotGrade !== this.model.lotGrade) {
                this.model.lotName = ''
                this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
                return
              }
              if (productData.processOperationName !== this.model.processOperationName) {
                this.model.lotName = ''
                this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
                return
              }
              if (productData.productSpecName !== this.model.productSpecName) {
                this.model.lotName = ''
                this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
                return
              }
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
            }
          }
          // if (!this.model.lotGrade) {
          //   // 载具没产品时候，直接走绑定逻辑
          //   this.submit(value, '绑定')
          // } else {
          // // 如果载具已绑定，则校验
          // if (productData.lotGrade !== this.model.lotGrade) {
          //   this.model.lotName = ''
          //   this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
          //   return
          // }
          // if (productData.processOperationName !== this.model.processOperationName) {
          //   this.model.lotName = ''
          //   this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
          //   return
          // }
          // if (productData.productSpecName !== this.model.productSpecName) {
          //   this.model.lotName = ''
          //   this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
          //   return
          // }
          // // 前面校验通过，且弹夹为空，则进行绑定
          // if (!productData.carrierName) {
          //   if (this.model.lotQuantity == this.model.capacity) {
          //     this.model.lotName = ''
          //     this.$Toast(`载具${this.model.durableName}已装满`)
          //     return
          //   }
          //   this.submit(value, '绑定')
          //   return
          // } else {
          //   if (productData.carrierName !== this.model.durableName) {
          //     this.model.lotName = ''
          //     this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
          //     return
          //   }
          //   this.submit(value, '解绑')
          // }
          // }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'durableName':
          this.model.durableName = 'Stack001'
          break;
        case 'lotName':
          this.model.lotName = 'GF20423032900001'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../styles/uform.scss';
@import '../../styles/publicStyle.scss';
</style>
