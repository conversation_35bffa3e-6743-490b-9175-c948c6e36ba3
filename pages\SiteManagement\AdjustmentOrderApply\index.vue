<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="调出仓库" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('prevStockCode')">
            <view v-if="model.prevStockCode">{{ $utils.filterObjLabel(dicts.prevStockCodeList, model.prevStockCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'prevStockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="调出库位" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('prevStockLocationCode')">
            <view v-if="model.prevStockLocationCode">{{ $utils.filterObjLabel(dicts.prevStockLocationCodeList, model.prevStockLocationCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'prevStockLocationCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="调入仓库" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockCode')">
            <view v-if="model.stockCode">{{ $utils.filterObjLabel(dicts.stockCodeList, model.stockCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="调入库位" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('stockLocationCode')">
            <view v-if="model.stockLocationCode">{{ $utils.filterObjLabel(dicts.stockLocationCodeList, model.stockLocationCode) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'stockLocationCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="物料条码" labelWidth="100">
          <u--input v-model="model.consumableName" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">库存数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.quantity }}</view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>

        <!-- refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower" -->
        <!-- <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h30 hcenter c_999">
              <view> 物料条码</view>
              <view> {{ ele.consumableName }}</view>
            </view>
            <view class="flex between h30 hcenter c_999">
              <view> 库存数量</view>
              <view>{{ ele.quantity }}</view>
            </view>
            <view>
              <u-button type="error" text="删除" @click="deleteItem(ele, index)"></u-button>
            </view>
          </view>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view> -->
      </view>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
export default {
  mixins: [useNls, ScrollMixin],
  components: {
    NoData,
  },
  data() {
    this.changeconsumableSpecName = this.$debounce(this.changeconsumableSpecName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },

      columns: [],
      select: false,
      selectType: '',
      rulesTip: {
        prevStockCode: '调出仓库不能为空',
        prevStockLocationCode: '调出库位不能为空',
        stockCode: '调入仓库不能为空',
        stockLocationCode: '调入库位不能为空',
      },
      trayNoParmas: {},
      model: {},
      list: [], // 物料扫描数据
      // list: Array.from({ length: 10 }, (v, i) => i),
      dicts: {
        prevStockCodeList: [], // 调出仓库
        prevStockLocationCodeList: [], // 调出库位
        stockCodeList: [], // 调入仓库
        stockLocationCodeList: [], // 调入库位
      },
    }
  },
  computed: {
  },
  watch: {
    'model.consumableName': {
      handler(val) {
        this.changeconsumableSpecName(val)
      }
    },

  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle
    await this.initNls(pageParams, this.nlsMap)
    // this.getEnumValue('TrayReturnType', 'trayReturnTypeList') // 退库类型
    this.getStockCodeList() // 仓库编码
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'prevStockCode':
          this.columns = this.dicts.prevStockCodeList
          break;
        case 'prevStockLocationCode':
          this.columns = this.dicts.prevStockLocationCodeList
          break;
        case 'stockCode':
          this.columns = this.dicts.stockCodeList
          break;
        case 'stockLocationCode':
          this.columns = this.dicts.stockLocationCodeList
          break;

        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'prevStockCode') {
        this.dicts.prevStockLocationCodeList = []
        this.model.prevStockLocationCode = undefined
        let data = {
          stockCode: this.model.prevStockCode,
        }
        let res = await this.$service.Dicts.getstockLocationCodeList(data)
        this.dicts.prevStockLocationCodeList = res.datas.map(item => ({
          label: item.stockLocationText,
          value: item.stockLocationCode,
        }))
      }

      if (this.selectType == 'stockCode') {
        this.dicts.stockLocationCodeList = []
        this.model.stockLocationCode = undefined
        let data = {
          stockCode: this.model.stockCode,
        }
        let res = await this.$service.Dicts.getstockLocationCodeList(data)
        this.dicts.stockLocationCodeList = res.datas.map(item => ({
          label: item.stockLocationText,
          value: item.stockLocationCode,
        }))
      }
    },

    initModel() {
      this.model = {
        prevStockCode: '',  //  	 调出仓库
        prevStockLocationCode: '',//  	调出库位	
        stockCode: '',  //  	 调入仓库
        stockLocationCode: '',//  	调入库位
        consumableName: '',//  	物料条码
      }
    },
    getStockCodeList() {
      this.$service.common.getDictByQueryId('GetStockCodeList').then(res => {
        this.dicts.prevStockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
        this.dicts.stockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
      })
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.prevStockCode == this.model.stockCode && this.model.prevStockLocationCode == this.model.stockLocationCode) {
        return this.$Toast('调出仓库和调入仓库不能相同')
      }
      let params = {
        adjustmentOrderItemList: this.list,
        prevStockCode: this.model.prevStockCode,
        prevStockLocationCode: this.model.prevStockLocationCode,
        stockCode: this.model.stockCode,
        stockLocationCode: this.model.stockLocationCode,
      }
      this.$service.AdjustmentOrderApply.submit(params).then(res => {
        this.$Toast('操作成功')
        this.list = []
      })
    },

    async changeconsumableSpecName(value) {
      if (!value) return
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          this.model.consumableName = ''
          return
        }
      }
      let findIndex = this.list.findIndex(item => item.consumableName == value)
      if (findIndex > -1) {
        this.$Toast('该物料条码已存在!')
        this.model.consumableName = ''
        return
      }
      try {
        let params = {
          prevStockCode: this.model.prevStockCode,
          prevStockLocationCode: this.model.prevStockLocationCode,
          consumableName: value,
        }
        let res = await this.$service.AdjustmentOrderApply.scanConsumable(params)
        if (res.datas.length > 0) {
          if (res.datas[0].consumableState != 'Available') {
            this.$Toast(`该物料条码[${this.model.consumableName}]不是可用状态`)
          } else if (res.datas[0].quantity <= 0) {
            this.$Toast(`该物料条码[${this.model.consumableName}]库存数量小于等于0`)
          } else if (res.datas[0].scrapFlag == 'Y') {
            this.$Toast(`该物料条码[${this.model.consumableName}]已报废`)
          } else {
            this.$Toast('操作成功!')
            this.list.push(res.datas[0])
          }
          this.model.consumableName = ''
        } else {
          this.$Toast(`提示该物料条码[${this.model.consumableName}], 在该仓库[${this.model.prevStockCode}]该库位[${this.model.prevStockLocationCode}]不存在`)
          this.model.consumableName = ''
        }
      } catch (error) {
        this.model.consumableName = ''
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
