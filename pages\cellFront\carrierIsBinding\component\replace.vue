<template>
  <view class="listPage pl5 pr5 pb10">
    <view class="topContainer">
      <u--form labelPosition="left" :model="formModel" labelWidth="100">
        <u-form-item label="载具编码" borderBottom required labelWidth="100">
          <u--input v-model="formModel.trayName" border="none" focus placeholder="请输入或扫描载具编码"></u--input>
          <view class="iconfont icon-saoma" @click="scan('trayName')"></view>
        </u-form-item>
        <u-form-item label="被替换产品" borderBottom required labelWidth="100">
          <u--input v-model="formModel.oldReplaceProduct" border="none" :focus="focus_oldReplaceProduct" placeholder="请输入或扫描被替换产品"></u--input>
          <view class="iconfont icon-saoma" @click="scan('oldReplaceProduct')"></view>
        </u-form-item>
        <u-form-item label="新装产品" borderBottom required labelWidth="100">
          <u--input v-model="formModel.newpackageProduct" border="none"  :focus="focus_newpackageProduct" placeholder="请输入或扫描新装产品"></u--input>
          <view class="iconfont icon-saoma" @click="scan('newpackageProduct')"></view>
        </u-form-item>
        <u-form-item label="已装" labelWidth="100">
          <view class="w100x flex right" v-if="model.length > 0">
            {{ model.length }}
          </view>
        </u-form-item>
      </u--form>
    </view>

    <view class="listContainer">
      <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
        <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w100">位置</view>
        <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">产品条码</view>
      </view>
      <view class="table_content pb10">
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
          <view v-for="(item, index) in model" :key="index" class="flex bl_e1e1e1 h50">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w100">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.cellId24 }}</view>
          </view>
          <view class="pt100" v-if="!model || model.length === 0">
            <u-empty mode="data"></u-empty>
          </view>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </scroll-view>
      </view>
    </view>
    <view class="btnContainer" @click="submit">替换</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'replace',
  mixins: [ScrollMixin],
  data() {
    this.changeTrayName = this.$debounce(this.changeTrayName, 1000)
    this.changeOldReplaceProduct = this.$debounce(this.changeOldReplaceProduct, 1000)
    this.changeNewpackageProduct = this.$debounce(this.changeNewpackageProduct, 1000)
    return {
      rulesTip: {
        trayName: '请输入或扫描正确的载具编码',
        oldReplaceProduct: '请输入或扫描被替换产品',
        newpackageProduct: '请输入或扫描新装产品',
      },
      formModel: {
        trayName: null, // 载具
        oldReplaceProduct: null,  //被替换产品 
        newpackageProduct: null, // 新装产品
      },
      trayNameFlag: false, // 正确载具标识
      oldReplaceProductFlag: false, // 正确载具标识
      newpackageProductFlag: false,
      focus_oldReplaceProduct:false,
      focus_newpackageProduct: false
    };
  },
  watch: {
    'formModel.trayName': {
      handler(val) {
        this.focus_oldReplaceProduct = false
        this.changeTrayName(val)
      }
    },
    'formModel.oldReplaceProduct': {
      handler(val) {
        this.focus_newpackageProduct = false
        this.changeOldReplaceProduct(val)
      }
    },
    'formModel.newpackageProduct': {
      handler(val) {
        this.changeNewpackageProduct(val)
      }
    },
  },
  mounted() {
    this.initSearchModel()
  },
  methods: {
    submit() {
      if (!this.trayNameFlag) {
        return this.$Toast('请输入或扫描载具编码')
      }
      if (!this.oldReplaceProductFlag) {
        return this.$Toast('请输入或扫描被替换产品编号')
      }
      if (!this.newpackageProductFlag) {
        return this.$Toast('请输入或扫描新装产品编号')
      }

      uni.showModal({
        title: '提示',
        content: '是否确认替换？',
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let unbind = {
              operateNo: '10003',
              oldCellId24: this.formModel.oldReplaceProduct,
              cellId24List: [this.formModel.newpackageProduct],
              trayName: this.formModel.trayName,
              userId: this.$getLocal(USER_ID)
            }
            this.$service.carrierIsBind.operateTrayProduct(unbind).then(res => {
              let odlIndex = this.model.findIndex(i => i.cellId24 === this.formModel.oldReplaceProduct)
              this.model.splice(odlIndex, 1, {
                cellId24: this.formModel.newpackageProduct
              })
              this.$Toast('替换成功')
              this.formModel.oldReplaceProduct = null
              this.formModel.newpackageProduct = null
              this.oldReplaceProductFlag = false
              this.newpackageProductFlag = false
              console.log('this.model', this.model)
              // this.getData(true, true)

            })
          }
          if (res.cancel) { }
        },
      })
    },
    changeTrayName(value) {
      if (!value) {
        return this.model = []
      }
      this.formModel.trayName = value
      this.trayNameFlag = false
      this.getData(true, true)
    },
    // 被替换产品
    changeOldReplaceProduct(value) {
      if (!value) return
      let params = {
        cellId24: value,
        operateNo: '10003',
        trayName: this.formModel.trayName
      }
      this.oldReplaceProductFlag = false
      this.$service.carrierIsBind.queryScanProduct(params).then(res => {
        this.oldReplaceProductFlag = true
        this.focus_newpackageProduct = true
      }).catch(e=>{
        this.formModel.oldReplaceProduct = null
      })
    },
    // 新产品
    changeNewpackageProduct(value) {
      if (!value) return
      let params = {
        cellId24: value,
        operateNo: '10004',
        trayName: this.formModel.trayName
      }
      this.newpackageProductFlag = false
      this.$service.carrierIsBind.queryScanProduct(params).then(res => {
        this.newpackageProductFlag = true
      }).catch(e=>{
        this.formModel.newpackageProduct = null
      })
    },
    initSearchModel() {
      this.searchModel = {
        operateNo: '10003',
        trayName: null,
        pageNo: this.pageNumber,
        limit: 99999,
      }
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      params.trayName = this.formModel.trayName
      await this.$service.carrierIsBind.queryPageList(params).then((res) => {
        this.trayNameFlag = true
        let records = res.data && res.data.trayProductPage && res.data.trayProductPage.records || []
        this.model = clearOldData ? records : [...this.model, ...records]

        if (this.searchModel.pageNo === 1) {
          this.focus_oldReplaceProduct = true
        }
        if (this.searchModel.pageNo == res.data.trayProductPage.pages) {
          this.status = 'nomore'
        } else {
          this.status = 'loadmore'
        }
        this.refresherTriggered = false
      }).catch((e) => {
        this.formModel.trayName = null
        this.refresherTriggered = false
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'trayName':
          this.formModel.trayName = 'tuopan002'
          break;
        case 'oldReplaceProduct':
          this.formModel.oldReplaceProduct = '666CZ00W37812GC8S1370066'
          break;
        case 'newpackageProduct':
          this.formModel.newpackageProduct = '666CZ00W37812GC8S1370066'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.formModel, key, res.result)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.page {
  background-color: #f3f3f7;
}

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;
  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-top: 20rpx;
    .table_header {
      flex-shrink: 0;
    }
    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>