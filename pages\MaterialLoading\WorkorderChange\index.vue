<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备号描述" borderBottom labelWidth="100">
          <!-- <u--input readonly v-model="model.machineText" border="none"></u--input> -->
          <view class="w100x flex right">{{ model.machineText }}</view>
        </u-form-item>

        <!-- {{ model.productOrderName }} -->
        <!-- <u-form-item label="工单" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <zqs-select :multiple="true" :list="dicts.productOrderNameList" label-key="label" value-key="value" placeholder=" 请选择" title="工单编码/产品名称" v-model="model.productOrderName"></zqs-select>
          </view>
        </u-form-item> -->

        <u-form-item label="工单" borderBottom required labelWidth="80">
          <view class="w100x flex right" @click="checkSelect('productOrderName')">
            <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList, model.productOrderName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <!-- {{ model.processOperationName }} -->
        <!-- <u-form-item label="工序" borderBottom required labelWidth="100">
          <view class="w100x flex right">
            <zqs-select :multiple="true" :list="dicts.processOperationNameList" label-key="label" value-key="value" placeholder=" 请选择" title="工序编码/工序描述" v-model="model.processOperationName"></zqs-select>
          </view>
        </u-form-item> -->

        <u-form-item label="工序" required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('processOperationName')">
            <view v-if="model.processOperationName">{{ $utils.filterObjLabel(dicts.processOperationNameList, model.processOperationName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'processOperationName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
      </u--form>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">工单号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">进站时间</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.lotName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.productOrderName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.inTime }}</view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="revoked(ele, index)" v-if="ele.flag == '1'">
                <u-button type="primary" text="撤销" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
              <view class="w80x" @click="deleteItem(ele, index)" v-else>
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
export default {

  mixins: [ScrollMixin, useNls],
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      rulesTip: {
        machineName: '设备编号不能为空',
        productOrderName: '工单组合不能为空', // 
        processOperationName: '工序组合不能为空', // 
      },
      model: {},
      dicts: {
        // 工单
        productOrderNameList: [
          // { label: 'label)', value: 'value', },
          // { label: 'label2)', value: 'value2', },
        ],
        //  工序
        processOperationNameList: [
          // { label: 'label123)', value: 'value1', },
          // { label: 'labe123l)', value: 'value3', },
        ]
      },
      list: [], // 物料扫描数据
      // list: Array.from({ length: 10 }, (v, i) => i),
      columns: [],
      select: false,
      selectType: '',
    }
  },
  computed: {
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
  },
  methods: {
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        case 'processOperationName':
          this.columns = this.dicts.processOperationNameList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      if (this.selectType == 'processOperationName') {
        this.changeProcessOperationName()
      }
      this.select = false
    },
    revoked(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认撤销？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // let params = {
            //   lotName: item.lotName,
            // }
            this.$service.WorkorderChange.undoDeleteLot(item.lotName).then((res) => {
              this.$Toast('撤销成功！')
              this.changeProcessOperationName()
            })
          }
          if (res.cancel) { }
        },
      })
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // let params = {
            //   lotName: item.lotName,
            // }
            this.$service.WorkorderChange.deleteLot(item.lotName).then((res) => {
              this.$Toast('删除成功！')
              this.changeProcessOperationName()
            })
          }
          if (res.cancel) { }
        },
      })
    },

    initModel() {
      this.model = {
        machineTaskType: '',// 
        machineName: '', // 设备号
        machineText: '', // 设备描述
        productOrderName: '', // 工单组合
        processOperationName: '', // 工序组合
      }
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        // ...this.model
        machineName: this.model.machineName,
        machineTaskType: 'MachineChangeTask',
        productOrderName: this.model.productOrderName,
        processOperationName: this.model.processOperationName
      }
      this.$service.SetProductionModel.machineTaskControllerAdd(params).then(res => {
        this.$Toast('操作成功!')
        this.initModel()
      })
    },
    async changeProcessOperationName() {
      if (!this.model.processOperationName) return
      this.list = []

      let params = {
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
      }

      let res = await this.$service.WorkorderChange.listDeleteLot(params)
      this.list = res.datas[0].deleteList

    },
    /* 设备号 */
    async changeMachineName(value) {
      this.model.machineText = ''
      this.model.productOrderName = ''
      this.model.processOperationName = ''
      this.dicts.productOrderNameList = []
      this.dicts.processOperationNameList = []
      this.list = []
      if (!value) return
      let params = {
        machineName: value,
        machineTaskType: 'MachineChangeTask',
      }
      try {
        let res = await this.$service.SetProductionModel.listProductOrder(params)
        this.model.machineText = res.datas[0].machineText

        this.dicts.productOrderNameList = res.datas[0].productOrderList.map((item) => ({
          label: `${item.productOrderName}/${item.productSpecText}`,
          value: item.productOrderName,
        }))
        this.dicts.processOperationNameList = res.datas[0].processOperationList.reverse().map((item) => ({
          label: `${item.processOperationName}/${item.processOperationText}`,
          value: item.processOperationName,
        }))

      } catch (error) {
        this.initModel()
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
