<template>
  <view class="bc_f3f3f7 myContainerPage">
    <!-- <u-navbar title="设备校准" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar> -->
    <u-navbar title="设备校准" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="130">
        <u-form-item label="设备编码" borderBottom required labelWidth="130">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备编码" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>
        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>
        <u-form-item label="工序" labelWidth="130">
          <view class="w100x flex right"> {{ model.processOperationName }} </view>
        </u-form-item>
        <u-form-item label="上次校准时间" labelWidth="130">
          <view class="w100x flex right"> {{ model.lastCalibrationTime }} </view>
        </u-form-item>
        <u-form-item label="设备校准履历" borderBottom labelWidth="120">
          <view class="w100x flex right"></view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">校准完成</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编码不能为空',
      },
      model: {},
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 工装
        description: null, // 工装描述
        lastCalibrationTime: null
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        machineName: this.model.machineName
      }
      this.$service.EquipmentManagement.finishCalibration(params).then(res => {
        if (res.success) {
          this.$Toast('校准完成!')
          this.initModel()
        }
      })
    },

    /* 工装 */
    async changeMachineName(value) {
      if (!value) return
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.EquipmentManagement.getMachineCheckInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的设备编码!')
          }
          this.model = res.datas[0]
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    gotoQuery() {
      if(!this.model.machineName) {
        this.$Toast('请先填写编码！')
        return
      }
      uni.navigateTo({
        url: `/pages/EquipmentManagement/EquipmentCheck/modules/EquipmentCheckList?machineName=${this.model.machineName}`,
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.COAT02.01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
