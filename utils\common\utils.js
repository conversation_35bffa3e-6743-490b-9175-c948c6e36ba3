export default {
  /**
 * 判断值是否为空（不仅仅是对象）
 * @param obj 判断的对象
 * @returns {boolean} 是否为空: true,为空
 */
  isEmpty(obj) {
    try {
      if (obj == null || obj == undefined) {
        return true;
      }
      //判断数字是否是NaN
      if (typeof obj === "number") {
        return isNaN(obj);
      }
      //判断参数是否是布尔、函数、日期、正则，是则返回false
      if (typeof obj === "boolean" || typeof obj === "function" || obj instanceof Date || obj instanceof RegExp) {
        return false;
      }
      //判断参数是否是字符串，去空，如果长度为0则返回true
      if (typeof obj === "string") {
        return obj.trim().length == 0;
      }

      if (typeof obj === 'object') {
        //判断参数是否是数组，数组为空则返回true
        if (obj instanceof Array) {
          return obj.length == 0;
        }

        //判断参数是否是对象，判断是否是空对象，是则返回true
        if (obj instanceof Object) {
          //判断对象属性个数
          return Object.keys(obj).length == 0;
        }
      }
    } catch (e) {
      console.log(e);
      return false;
    }
  },
  /*  符号连接 */
  optionShowConfig(value1, value2) {
    if (value1 || value2) {
      return (value1 || '') + (value1 && value2 ? '/' : '') + (value2 || '')
    }
  },
  // 获取当前页面route
  getCurPageRoute() {
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
      let curPage = pages[pages.length - 1];
      return curPage.route;
    }
    return '';
  },
  //格式化对象 obj => {value, label}
  $getErgodic(obj) {
    if (!obj) {
      return []
    }
    return Object.entries(obj).map(([v, k]) => ({ label: k, value: v }))
  },
  //格式化对象 list({value, label}) => obj: {value: label}
  $getArr2Obj(arr) {
    if (!arr || arr.length == 0) {
      return {}
    }
    let obj = {}
    arr.forEach(item => {
      obj[item.value] = item.label
    })
    return obj
  },
  /**
 * 过滤对象中为空的属性
 * @param obj
 * @returns {*}
 */
  filterObj(obj) {
    if (!(typeof obj === 'object')) {
      return
    }
    for (var key in obj) {
      if (obj.hasOwnProperty(key) &&
        (obj[key] == null || obj[key] == undefined || obj[key] === '')) {
        delete obj[key]
      }
    }
    return obj
  },
  filterObjLabel(arr, key) {
    const obj = (arr || []).find(item => item.value == key)
    return obj ? obj.label : ''
  },

  // 两数相乘防精度丢失 8.8*100
  calculate_mul(data1, data2) {
    let m = 0, s1 = data1.toString(), s2 = data2.toString();
    // 获取所有参数小数位长度之和
    try { m += s1.split(".")[1].length } catch (e) { }
    try { m += s2.split(".")[1].length } catch (e) { }
    // 替换掉小数点转为数字相乘再除以10的次幂值
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
  },

  calculate_reduce(data1, data2) {
    let r1, r2, m, n;
    // 获取每个参数的小数的位数
    try { r1 = data1.toString().split(".")[1].length } catch (e) { r1 = 0 }
    try { r2 = data2.toString().split(".")[1].length } catch (e) { r2 = 0 }
    // 计算底数为10以最大小数位数为次幂的值
    m = Math.pow(10, Math.max(r1, r2));
    //精度长度以最大小数位数为长度
    n = (r1 >= r2) ? r1 : r2;
    return ((data1 * m - data2 * m) / m).toFixed(n);
  },


  backAndUpdata(callback, refName) {
    setTimeout(() => {
      if (callback) {
        console.log(callback, refName)
        let pages = getCurrentPages();
        let prevPage = pages[pages.length - 2]; //上一个页面
        prevPage[callback] && prevPage[callback](refName);
      }
      uni.navigateBack({
        delta: 1,
      });
    }, 0)
  }
}


