<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="匀浆胶料批次指定" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备编号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom>
          <u--input readonly v-model="model.palletDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="浆料批次" borderBottom labelWidth="100">
          <u--input readonly v-model="model.lotName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="浆料种类" borderBottom labelWidth="100">
          <u--input readonly v-model="model.spliceTypeValue" border="none"></u--input>
        </u-form-item>

        <u-form-item label="胶料批次" borderBottom required>
          <u--input v-model="model.rubberLotName" border="none" placeholder="请扫描或输入胶料批次" :focus="rubberLotName_focus" @focus="focusEvent('rubberLotName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('rubberLotName')"></view>
        </u-form-item>

        <u-form-item label="胶料种类" borderBottom labelWidth="100">
          <u--input readonly v-model="model.rubberSpliceTypeValue" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工单" borderBottom labelWidth="100">
          <u--input readonly v-model="model.productRequestName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产出数量" borderBottom labelWidth="100">
          <view class="flex">
            <view class="flex hcenter flex1"><u--input readonly v-model="model.productQuantity" border="none"></u--input> {{ model.productQuantityUnit }} </view>
            <view class="flex hcenter flex1"><u--input readonly v-model="model.lotWeight" border="none"></u--input> {{ model.lotWeightUnit }} </view>
          </view>
        </u-form-item>

        <u-form-item label="完成时间">
          <u--input readonly v-model="model.releaseTime" border="none"></u--input>
        </u-form-item>
      </u--form>
      <view class="lin40 dib fs16 pl20" style="color: #409eff" @click="onSkip"> 已指定胶料批次 </view>
    </view>

    <view class="btnContainer" @click="submit">确认</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import RedScan from "@/mixins/RedScan";
export default {
  name: 'coatingStartIndex',
  mixins: [RedScan],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeRubberLotName = this.$debounce(this.changeRubberLotName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        rubberLotName: '浆料批次不能为空',
      },
      model: {},
      machineNameFlag: false,
      rubberLotFlag: false,
      rubberLotName_focus: false,
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.rubberLotName': {
      handler(val) {
        this.changeRubberLotName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        palletDesc: null, // 极性
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述,
        lotName: null, //浆料批次
        spliceTypeValue: null,// 浆料种类
        rubberLotName: null, //胶料批次
        rubberSpliceTypeValue: null, //胶料种类
        productRequestName: null,// 工单
        productSpecName: null, // 产品单号
        productSpecDesc: null, // 产品描述
        productQuantity: null, // 生产数量 kg
        lotWeight: null, //电芯计量数量 ea
        productQuantityUnit: null,
        lotWeightUnit: null,
        releaseTime: null,// 完成时间
      }
      this.machineNameFlag = false
      this.rubberLotFlag = false
    },
    onSkip() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: `/pages/cellFront/homogenateRubber/detail?machineName=${this.model.machineName}`
      })
    },

    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.model.lotName = null
      this.model.spliceTypeValue = null
      this.model.machineDesc = null
      this.model.palletDesc = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null
      this.rubberLotName_focus = false
      let params = {
        operateNo: '1001', // 后台区分固定字段
        machineName: value,
      }
      try {
        let res = await this.$service.homogenateRubber.trackIn(params)
        this.machineNameFlag = true
        this.model.machineDesc = res.data.machineDesc
        this.model.palletDesc = res.data.palletDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.lotName = res.data.lotName
        this.model.spliceTypeValue = res.data.spliceTypeValue
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc
        this.rubberLotName_focus = true
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 胶料批次 */

    async changeRubberLotName(value) {
      if (!value) return
      this.rubberLotFlag = false
      this.model.rubberSpliceTypeValue = null
      this.model.productRequestName = null
      this.model.productSpecName = null
      this.model.productSpecDesc = null
      this.model.productQuantity = null
      this.model.productQuantityUnit = null
      this.model.lotWeight = null
      this.model.lotWeightUnit = null
      this.model.releaseTime = null
      let params = {
        operateNo: '1001', // 后台区分固定字段
        lotName: value,
        machineName: this.model.machineName,
      }
      try {
        let res = await this.$service.homogenateRubber.queryLot(params)
        this.rubberLotFlag = true
        this.model.rubberSpliceTypeValue = res.data.spliceTypeValue
        this.model.productRequestName = res.data.productRequestName
        this.model.productSpecName = res.data.productSpecName
        this.model.productSpecDesc = res.data.productSpecDesc
        this.model.productQuantity = res.data.productQuantity
        this.model.lotWeight = res.data.lotWeight
        this.model.productQuantityUnit = res.data.productQuantityUnit
        this.model.lotWeightUnit = res.data.lotWeightUnit
        this.model.releaseTime = res.data.releaseTime
      } catch (error) {
        this.model.rubberLotName = null
      }
    },
    submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      if (!this.rubberLotFlag) {
        return this.$Toast('请输入或扫描正确胶料批次')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        lotName: this.model.lotName,
        rubberLotName: this.model.rubberLotName,
        machineName: this.model.machineName,
        operateNo: '1001',
        userId: this.$getLocal(USER_ID)
      }
      this.$service.homogenateRubber.assignLot(params).then(res => {
        this.$Toast('指定成功')
        this.initModel()
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'C1Z001002'
          break;
        case 'rubberLotName':
          this.model.rubberLotName = 'C8J1C-60'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>