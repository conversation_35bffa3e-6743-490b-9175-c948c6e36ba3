<template>
  <view class="page">
    <view class="content">
      <!-- v-if for国际化菜单 -->
      <index v-if="PageCur == 'index'"></index>
      <mine v-show="PageCur == 'mine'"></mine>
    </view>
    <view class="tab_box flex">
      <view class="flex1 flex flex_column center" @click="NavChange" data-cur="index">
        <view class="w25 h25">
          <image v-if="PageCur == 'index'" src="../../static/home/<USER>"></image>
          <image v-if="PageCur != 'index'" src="../../static/home/<USER>"></image>
        </view>
        <view :class="PageCur == 'index' ? 'c_00b17b' : 'c_666'">{{ globalMap.lbHome }}</view>
      </view>
      <view class="flex1 flex flex_column center" @click="NavChange" data-cur="mine">
        <view class="w25 h25">
          <image v-if="PageCur == 'mine'" src="../../static/home/<USER>"></image>
          <image v-if="PageCur != 'mine'" src="../../static/home/<USER>"></image>
        </view>
        <view :class="PageCur == 'mine' ? 'c_00b17b' : 'c_666'">{{ globalMap.lbmine }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { POROS_SYSTEMID, MENUS } from '@/utils/common/evtName.js'
import index from "./index.vue"
import mine from "./mine.vue"
export default {
  name: 'MesTworkPdaHome',
  components: {
    index,
    mine
  },
  data() {
    return {
      PageCur: 'index',
      globalMap: getApp().globalData.globalMap // 获取全局数据
    };
  },

  mounted() {
  },
  methods: {
    async NavChange(e) {
      this.PageCur = e.currentTarget.dataset.cur;
    },
  },
};
</script>
<style>
page {
  height: 100%;
  background-color: #f0f2f5;
}
</style>
<style lang="scss" scoped>
image {
  width: 100%;
  height: 100%;
}
.page {
  height: calc(100vh - 100rpx);
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
  }
}
.tab_box {
  position: fixed;
  width: 100%;
  height: 100rpx;
  bottom: 0;
  z-index: 1024;
  background-color: #fff;
}
</style>