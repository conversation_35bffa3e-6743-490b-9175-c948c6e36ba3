<template>
	<view class="bc_f3f3f7 myContainerPage">
		<u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
			leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
		<!-- {{ nlsMap }} -->
		<view class="myContainer ma10">
			<u--form labelPosition="left" :model="model" labelWidth="100">
				<u-form-item label="作业类型" borderBottom required labelWidth="100">
					<view class="w100x flex right" @click="checkSelect('workType')">
						<view v-if="model.workType">{{ $utils.filterObjLabel(dicts.workTypeList, model.workType) }}
						</view>
						<view class="c_c0c4cc" v-else>请选择</view>
						<view class="ml5"
							:style="{ transform: select && selectType === 'stockCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</u-form-item>

				<u-form-item label="上下料位置" borderBottom required labelWidth="100">
					<view class="w100x flex right" @click="checkSelect('machineName')">
						<view v-if="model.machineName">{{ $utils.filterObjLabel(dicts.machineList, model.machineName) }}
						</view>
						<view class="c_c0c4cc" v-else>请选择</view>
						<view class="ml5"
							:style="{ transform: select && selectType === 'stockLocationCode' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</u-form-item>


				<u-form-item label="耗材标签" labelWidth="100">
					<u--input v-model="model.boxNo" border="none" placeholder="请扫描或输入"></u--input>
					<view class="iconfont icon-saoma" @click="scan('boxNo')"></view>
				</u-form-item>
			</u--form>

			<u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
				@cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
			<!-- 搜索框 -->
			<u-input type="text" v-model="model.serchNo" placeholder="输入搜索关键词" />

			<!-- 上料明细列表 -->

			<view class="mt10">
				<scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered"
					@refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll"
					refresher-background="#f3f3f7">
					<view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in filteredList" :key="index">
						<view class="flex between h40 hcenter c_999">
							<view>物料标签</view>
							<view>{{ ele.consumableName }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>物料名称</view>
							<view>{{ ele.description }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>初始数量</view>
							<view> {{ ele.realQuantity }}</view>
						</view>
						<view class="flex between h40 hcenter c_999">
							<view> 当前数量</view>
							<view> {{ ele.quantity }}</view>
						</view>

						<view class="flex between h40 hcenter c_999">
							<view>消耗数量</view>
							<view>{{ ele.totalConsumeQuantity }}</view>
						</view>
						<view class="flex between h40 hcenter c_999">
							<view>上料时间</view>
							<view> {{  ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
						</view>
						<view>
							<u-button type="success" @click="summit(ele)">下料</u-button>
						</view>
					</view>
					<NoData v-if="!filteredList || filteredList.length === 0"></NoData>

				</scroll-view>
			</view>

			<u-modal :show="isShowModal" title="编辑"  confirmText="确认" @confirm="changeConfirm" @cancel="modalCancel"
				:showCancelButton="true">
				<view class="listContainer">
					<view class="table_header  flex">

						<u--form labelPosition="left" :model="model" labelWidth="100" ref="form" :border="false">
							<u-form-item label="设备号"  labelWidth="120">
								<view class="w100x flex right"> {{ model.machineName }}</view>
							</u-form-item>
							<u-form-item label="物料标签"  labelWidth="120">
								<view class="w100x flex right"> {{ model.consumableName }}</view>
							</u-form-item>
							<u-form-item label="是否完全消耗"  labelWidth="120">
								<view class="w100x">
									<u-radio-group v-model="model.czfs" placement="column" style="width: 100%;">
										<view style="display: flex;flex-direction: row-reverse">
											<u-radio v-for="item in radiolist" :key="item.value"
												:customStyle="{ marginRight: '10rpx' }" :label="item.name"
												:name="item.name">
											</u-radio>
										</view>
									</u-radio-group>
								</view>
							</u-form-item>
							<u-form-item label="理论剩余数量"  required labelWidth="120">
								<view v-show="model.czfs === '是'" class="w100x flex right"> {{ model.remainingQty }}
								</view>
								<u--input v-show="model.czfs !== '是'" v-model="model.remainingQty" 
									type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
				</view>
			</u-modal>
		</view>


	</view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData';
import useNls from "@/mixins/useNls";
import moment from 'moment';
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";


const SHOW_MODEL_KEY = 'CONSUMABLE_RECIEVE_EXIST'
export default {
	mixins: [useNls, ScrollMixin, PrintPackageMixin],
	components: {
		NoData,
	},
	data() {
		this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
		return {
			isShowModal: false,
			lodash: _,
			pageParams: {},
			pageTitle: '',
			globalMap: getApp().globalData.globalMap, // 获取全局数据
			nlsMap: {

			},

			columns: [],
			select: false,
			selectType: '',
			trayNoParmas: {},
			model: {},
			dicts: {
				workTypeList: [{
					"label": "上料",
					"value": "1"
				}, {
					"label": "下料",
					"value": "2"
				}
				], // 
				machineList: [], //打包机
			},
			filteredList: [],
			originalList: [],
			selectedJobType: '',
			radiolist: [
				{
					name: '是',
					value: 1
				},
				{
					name: '否',
					value: 0
				}
			],
		}
	},
	computed: {
	},
	watch: {
		'model.boxNo': {
			handler(val) {
				this.changeboxNo(val)
			},
		},

		'model.serchNo': {
			handler(val) {
				this.changefilterList(val)
			},
			immediate: true
		},
		'model.czfs': {
			handler(newVal) {
				if (newVal === '是') {
					this.model.remainingQty = 0; // "是" 时设置为 0
				}
			},
			immediate: true // 组件加载时立即执行一次
		},
		// 'model.workType': {
		// 	handler(newVal) {
		// 		if (newVal === '1') {
		// 			this.selectedJobType = '上料';
		// 		} else {
		// 			this.selectedJobType = '下料';
		// 		}
		// 	},
		// 	immediate: true // 组件加载时立即执行一次
		// }
	},
	async onLoad(options) {
		let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
		this.pageParams = pageParams
		this.pageTitle = pageParams.pageTitle // 标题
		await this.initNls(pageParams, this.nlsMap)

		this.init_GetPackMachine()
		this.initModel()
	},
	methods: {
        moment,
		checkSelect(type) {
			this.select = true
			this.selectType = type
			switch (type) {
				case 'workType':
					this.columns = this.dicts.workTypeList
					break;
				case 'machineName':
					this.columns = this.dicts.machineList
					break;
				default:
					break;
			}
		},
		async selectFirm(e) {
			this.$set(this.model, this.selectType, e.value[0].value)
			this.select = false
			if (this.selectType == 'machineName') {
				this.filteredList = []
				this.GetPackMachineFeedingmaterial()
			}
		},

		initModel() {
			this.model = {
				workType: '',  //  	 作业类型
				machineName: '',//  	上下料位置
				boxNo: '',//  	物料箱标签
				serchNo: '',//查询
				remainingQty: 0,
				czfs: '否',
				consumableName: ''
			}
		},
		init_GetPackMachine() {
			this.$service.ThreeCodeToOne.GetPackMachine().then(res => {
				this.dicts.machineList = res.datas.map(item => ({
					label: item.description,
					value: item.machineName,
				}))
			})
		},
		changefilterList(searchValue) {
			if (!searchValue) {
				// 搜索框为空时，恢复原始列表
				this.filteredList = this.originalList;
				return;
			}

			// 进行模糊搜索：匹配 `filteredList` 中所有字段
			this.filteredList = this.originalList.filter(item =>
				Object.values(item).some(value =>
					value && value.toString().includes(searchValue) // 只匹配非空值
				)
			);
		},
		async changeboxNo(value) {

			if (!value) return
			if (!this.model.workType) {
				this.$Toast('请选择作业类型!')
				this.model.boxNo = ''
				return
			}
			if (!this.model.machineName) {
				this.$Toast('请选择上下料位置!')
				this.model.boxNo = ''
				return
			}
			if (this.model.workType == "1") {
				try {
					let params = {
						machineName: this.model.machineName,
						consumableName: this.model.boxNo
					}

					let res = await this.$service.ThreeCodeToOne.FeedingReport(params)

                      if(res.success){
						  this.GetPackMachineFeedingmaterial()
					  }
					this.$Toast(res.msg)



					this.model.boxNo = ''
				} catch (error) {
					this.model.boxNo = ''
				}
			} else if (this.model.workType == "2") {
				const foundItem = this.filteredList.find(item => item.consumableName === value);
				if (!foundItem) {
					this.$Toast('输入的耗材标签不存在，请检查!');
					this.model.boxNo = '';
					return;
				}
				this.model.consumableName = value
				 this.model.remainingQty = this.model.czfs === "是" ? 0 : foundItem.realQuantity;
				this.isShowModal = true
			}

		},
		// 模态框取消
		modalCancel() {
			this.isShowModal = false
			this.model.boxNo = ''
		},
		async changeConfirm() {
			if (this.model.remainingQty === '' || this.model.remainingQty === null || this.model.remainingQty === undefined) {
				return this.$Toast('请输入理论剩余数量!')
			}

			try {
				let params = {
					machineName: this.model.machineName,
					consumableName: this.model.consumableName,
					isConsumption: this.model.czfs == '否' ? 0 : 1,
					remainingQty: this.model.remainingQty
				}

				let res = await this.$service.ThreeCodeToOne.UnloadReport(params)

				this.$Toast(res.msg)
				if(res.success){
					this.modalCancel()
					this.GetPackMachineFeedingmaterial()
				}
				this.model.boxNo = ''
			} catch (error) {
				this.model.boxNo = ''
			}
		},
		async GetPackMachineFeedingmaterial(){
			this.filteredList = []
			let data = {
				machineName: this.model.machineName,
			}
			let res = await this.$service.ThreeCodeToOne.GetPackMachineFeedingmaterial(data)
			this.filteredList = res.datas
			this.originalList = [...this.filteredList]
		},
		summit(item) {
			// console.log('item', item);
			if (!item) return
			// if (!this.model.workType) {
			// 	this.$Toast('请选择作业类型!')
			// 	this.model.boxNo = ''
			// 	return
			// }
			if (!this.model.machineName) {
				this.$Toast('请选择上下料位置!')
				this.model.boxNo = ''
				return
			}

			// if (this.model.workType == "1") {
			// 	try {
			// 		let params = {
			// 			machineName: this.model.machineName,
			// 			consumableName: item.consumableName
			// 		}

			// 		let res = this.$service.ThreeCodeToOne.FeedingReport(params)

			// 		this.$Toast(res.msg)

			// 	} catch (error) {
			// 		console.log(error)
			// 	}
			// } 
				this.model.consumableName = item.consumableName
				this.model.remainingQty = this.model.czfs === "是" ? 0 : item.realQuantity;
				this.isShowModal = true
			
		},
		scan(key) {
		  // #ifdef H5
		  switch (key) {
		    case 'boxNo':
		      this.model.boxNo = '7859696401*ATD00518'
		      break;
		    default:
		      break;
		  }
		  // #endif
		  //#ifdef APP-PLUS
		  uni.scanCode({
		    success: (res) => {
		      this.model.boxNo = res.result
		    },
		  })
		  // #endif
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.list {
	margin-top: 10px;
}

.item {
	padding: 10px;
	background: #f2f2f2;
	margin-bottom: 5px;
}
</style>
