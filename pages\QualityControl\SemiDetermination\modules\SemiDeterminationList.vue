<template>
  <view class="myContainerPage">
    <u-navbar title="待确认不良判定明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <!-- <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">待提交不良明细</view> -->
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1 pa2">在制品/极耳</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">不良原因</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">判定</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">操作</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in list" :key="index" class="flex bl_e1e1e1">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 pa2 txt_c">{{ getString(item) }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70">{{ item.blyy }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.czfs }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2"><view class="btn" @click="submit(item, index)">删除</view></view>
            </view>
            <view class="pt100" v-if="!list || list.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
import { set } from 'vue';
export default {
  mixins: [ScrollMixin],
  data() {
    return {
      // rulesTip: {
      //   durableName: '设备编号不能为空',
      //   poleRollQuantity: '极卷数量不能为空',
      // },
      // model: {},
      // columns: [],
      // durableNameFlag: false, // 正确设备编号标识
      // select: false,
      // show: false,
      // content: '',
      // focusObj: {
      //   saveNo: false,
      //   materialPosition: false
      // },
      // showLotName: true,
      // list: [
      //   {
      //     lotName: '23456789977744',
      //     productOrderName: 'E0001'
      //   }
      // ]
      // list: this.$store.state.SemiRecordList
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.SemiDeterminationList;
      },
      set(value) {
        this.$store.commit('changeSemiDeterminationList', value);
      }
    }
  },
  watch: {
    'model.durableName': {
      handler(val) {
        this.changeDurableName(val)
        this.GetProductListByDurableName(val)
      }
    }
  },
  onLoad(e) {
    // this.initModel()
    // this.model.durableName = e && e.durableName
  },
  methods: {
    moment,
    submit(item, index) {
      this.list.splice(index, 1)
    },
    getString(item) {
      if(item.zzptm != item.lotName && item.zzptm != item.carrierName) {
        // 既不是电芯也不是托盘，为极耳
        return `${item.lotName}
        /${item.zzptm}`
      } else {
        return item.lotName
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/uform.scss';
@import '../../../../styles/publicStyle.scss';
.bottomBtn {
  position: fixed;
  bottom: -28rpx;
  left: 50%;
  transform: translate(-50%, -50%);
}
.table_header {
  flex-shrink: 0;
}
.table_content {
  flex: 1;
  overflow-y: scroll;
}
.btn {
  width: 120rpx;
  height: 50rpx;
  border-radius: 10%;
  background-color: #0285be;
  border: 2rpx solid #b1c2db;
  text-align: center;
  line-height: 50rpx;
  font-size: 24rpx;
  color: #fff;
}
</style>
