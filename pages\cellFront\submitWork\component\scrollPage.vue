<template>
  <view class="h100x">
    <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
      <view>
        <slot :model="model"></slot>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </view>
    </scroll-view>
    <view @click="goTop">
      <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'MesTworkPdaScrollPage',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  props: {
    /* 请求地址Key, 需要再service，对应 */
    requireUrlKey: {
      type: String,
      required: true
    },
    /* 额外请求参数 */
    otherParams: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
    };
  },
  created() {
    this.initSearchModel()
    this.getData()
  },
  methods: {
    initSearchModel() {
      this.searchModel = {
        pageNo: this.pageNumber,
        limit: this.pageSize,
      }
      Object.assign(this.searchModel, { ...this.otherParams })
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      setTimeout(() => {
        let res = Array.from({ length: 20 }, (v, i) => ({label:`生产线${i}`,value:i}))
        // let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        this.model = clearOldData ? res : [...this.model, ...res]
        this.refresherTriggered = false
        if (this.model.length > 30) {
          this.status = 'nomore'
          
        } else {
          this.status = 'loadmore'
        }
      }, 1000)
      return
      let params = JSON.parse(JSON.stringify(this.searchModel))
      this.$service.submitWork[this.requireUrlKey](params).then((res) => {
        if (res && res.success) {
          let { simpleTrackProductRequest, trackProductRequestPage } = res.data
          this.simpleTrackProduct = simpleTrackProductRequest
          this.model = clearOldData ? trackProductRequestPage.records : [...this.model, ...trackProductRequestPage.records]
          if (this.searchModel.pageNo == trackProductRequestPage.pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },

  },
};
</script>

<style lang="scss" scoped>
</style>