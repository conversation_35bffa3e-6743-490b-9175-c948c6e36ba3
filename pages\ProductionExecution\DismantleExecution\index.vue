<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="类型" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('isSnTrace')">
            <view v-if="model.isSnTrace">{{ $utils.filterObjLabel(dicts.isSnTraceList, model.isSnTrace) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'isSnTrace' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="条码号" borderBottom required labelWidth="100">
          <u--input v-model="model.lotName" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
        @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="mt10 flex between" style="color: #409eff"> 物料列表：
        <view class="flex r10">
          <button class="mini-btn mr5" :disabled="disassembleButDis" type="primary" size="mini"
            @click="disassemble">拆解</button>
          <button class="mini-btn" :disabled="replaceButDis" type="primary" size="mini" @click="replace">替换</button>
        </view>
      </view>
      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料编码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料描述</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">数量</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1 bc_fff" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index"
            @click="tabChecked(index, ele)" :class="{ 'active-bg': selectedIndexes.includes(index) }">
            <view class="fs13 flex center bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableSpecName }}</view>
            <view class="fs13 flex center bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.description }}</view>
            <view class="fs13 flex center bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableName }}</view>
            <view class="fs13 flex center bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumeQuantity }}</view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      select: false,
      selectType: '',
      columns: [],
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      rulesTip: {
        trayNo: '托盘标签不能为空',
      },
      model: {},
      list: [],  // 扫描数据
      dicts: {
        isSnTraceList: [{ label: '半成品', value: 'true' }, { label: '原材料', value: 'false' }],  // 入库类型  半成品：true 原材料：false
      },
      disassembleButDis: true,   // 拆解按钮是否禁用
      replaceButDis: true,  // 替换按钮是否禁用
      activeIndex: -1,  // 列表当前选中行
      currentRowData: {},  // 选中行数据
      selectedIndexes: [],      // ✅ 批量选中的行索引
      selectedRows: [],         // ✅ 批量选中的行数据
      currentLotName: ''
    }
  },
  computed: {
  },
  watch: {
    'model.lotName': {
      handler(val) {
        this.changeLotName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.initModel()
  },
  methods: {
    // 拆解按钮
    disassemble() {
      if (this.selectedRows.length === 0) return;
      uni.showModal({
        title: '提示',
        content: `请确认是否拆解选中的 ${this.selectedRows.length} 个物料？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            const params = {
              list: this.selectedRows
            }
            this.$service.SnTraceController.postDecompositionExecution(params).then(res => {
              if (res.data == true) {
                this.$Toast('拆除成功')
                this.changeLotName(this.currentLotName)

                
                this.selectedRows = [];
                this.selectedIndexes = [];
                this.disassembleButDis = true;
                this.replaceButDis = true;
              }
            })

            // this.activeIndex = -1
            // this.disassembleButDis = true;
            // this.replaceButDis = true;


          }
          if (res.cancel) { }
        }
      })
    },
    // 替换按钮
    replace() {
      uni.showModal({
        title: '提示',
        content: ``,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        editable: true,
        placeholderText: '条码号',
        success: (res) => {
          if (res.confirm) {
            if (res.content.length == 0) {
              this.$Toast('条码号')
              return;
            }
            console.log(res.content)
            let parmas = {
              ...this.currentRowData,
              consumableName: res.content
            }
            this.$service.SnTraceController.putDecompositionExecution(parmas).then(res => {
              console.log(res.data)
              if (res.data == true) {
                this.changeLotName(this.currentLotName)
              }
            })
            this.activeIndex = -1
            this.replaceButDis = true;
            this.disassembleButDis = true;
          }
          if (res.cancel) { }
        },
      })

    },
    // 选中行事件
    // tabChecked(index, row) {
    //   this.disassembleButDis = false;
    //   this.replaceButDis = false;
    //   this.currentRowData = row
    //   console.log('选中行', this.currentRowData)
    //   this.activeIndex = index
    // },
    tabChecked(index, row) {
      const i = this.selectedIndexes.indexOf(index);
      if (i === -1) {
        this.selectedIndexes.push(index);
        this.selectedRows.push(row);
      } else {
        this.selectedIndexes.splice(i, 1);
        this.selectedRows.splice(i, 1);
      }

      // 是否启用按钮
      this.disassembleButDis = this.selectedRows.length === 0;
      this.replaceButDis = this.selectedRows.length === 0;
    }
    ,
    // 初始化
    initModel() {
      this.model = {
        lotName: '',  // 条码号
        isSnTrace: '', // 半成品：true 原材料：false
      }
    },
    // 下拉框选择
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'isSnTrace':
          this.columns = this.dicts.isSnTraceList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },
    // 监听条码号，根据条码号查询数据
    async changeLotName(value) {
      if (!value) return
      if (!this.model.isSnTrace) return
      this.currentLotName = value
      this.list = []
      try {
        let parmas = {
          sn: value,
          isSnTrace: this.model.isSnTrace
        }
        this.$service.SnTraceController.getDecompositionExecution(parmas).then(res => {
          console.log('111')
          console.log(res.data)
          this.list = res.data
        })
      } catch (error) {
        this.model.lotName = ''
        this.activeIndex = -1
        this.disassembleButDis = true;
        this.replaceButDis = true;
      }
      this.model.lotName = ''
      this.activeIndex = -1
      this.disassembleButDis = true;
      this.replaceButDis = true;
    },
    // 扫码
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'lotName':
          this.model.lotName = 'LK1F5CF_ZJMFD0901' // LK1F5CF_ZJMFD0901
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.active-bg {
  background-color: #d0ebff; // 浅蓝
}
</style>
