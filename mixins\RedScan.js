var main, receiver, filter
var _codeQueryTag = false
export default {
  data() {
    return {
      scanCodeType: '',
    }
  },
  created() {
    // #ifndef H5
    this.initScan()
    this.startScan()
    // #endif
  },
  destroyed() {
    // #ifndef H5
    this.stopScan()
    // #endif
  },
  methods: {
    // 用于input 对焦
    focusEvent(val) {
      this.scanCodeType = val
    },
    initScan() {
      let _this = this
      main = plus.android.runtimeMainActivity()
      var IntentFilter = plus.android.importClass(
        'android.content.IntentFilter'
      )
      console.log(IntentFilter)
      filter = new IntentFilter()
      //下面的addAction内改为自己的广播动作
      filter.addAction('com.yt.action.scan')
      receiver = plus.android.implements(
        'io.dcloud.feature.internal.reflect.BroadcastReceiver',
        {
          onReceive: function (context, intent) {
            plus.android.importClass(intent)
            //下面的getStringExtra内改为自己的广播标签
            let code = intent.getStringExtra('text')
            console.log(code)
            _this.queryCode(code)
          },
        }
      )
    },
    startScan() {
      main.registerReceiver(receiver, filter)
    },
    stopScan() {
      main.unregisterReceiver(receiver)
    },
    queryCode: function (code) {
      if (_codeQueryTag) return false
      _codeQueryTag = true
      setTimeout(function () {
        _codeQueryTag = false
      }, 150)
      // var id = code
      // console.log('id:', id)
      // uni.$emit('scan', {
      //   code: id,
      // })
      this.model[this.scanCodeType] = code
    },
  },
}