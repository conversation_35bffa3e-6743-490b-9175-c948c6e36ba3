<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="材料上料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备编号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="工单" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect">
            <view>{{ selectProduct.productRequestName }}</view>
            <u-icon name="arrow-down" color="black" size="18" ></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="时间" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ selectProduct.createTime }}
          </view>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(selectProduct.productSpecName, selectProduct.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="BOM" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(selectProduct.bomId, selectProduct.bomDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="标签条码" required borderBottom labelWidth="100">
          <u--input v-model="model.consumableName" border="none" ref="consumableName" :focus="focusObj.consumableName" placeholder="扫描材料最小包装条码" @focus="focusEvent('consumableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>

        <u-form-item label="材料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="数量" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.quantity }} {{ model.consumeUnit }} </view>
        </u-form-item>
        <u-form-item label="位置" required labelWidth="100">
          <u--input v-model="model.materialPosition" border="none" ref="materialPosition" :focus="focusObj.materialPosition" placeholder="扫描设备上的材料安装位置条码" @focus="focusEvent('materialPosition')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('materialPosition')"></view>
        </u-form-item>
      </u--form>
      <view class="lin40 fs16 pl20 dib" style="color: #409eff" @click="onSkip"> 已上料明细 </view>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">提交</view>
    <!-- <view class="flex_shrink0 ">
      <u-button  type="success"  @click="submit" >保存</u-button>
    </view> -->
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    // this.changeMaterialPosition = this.$debounce(this.changeMaterialPosition, 1000)
    this.changeConsumableName = this.$debounce(this.changeConsumableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productRequestName: '工单不能为空',
        materialPosition: '位置不能为空',
        consumableName: '标签条码不能为空',
      },
      model: {},
      // model: {
      //   machineName: null, // 设备编号
      //   machineDesc: null, // 设备描述
      //   processOperationName: null, // 工序编码
      //   processOperationDesc: null, // 工序描述
      //   areaName: null, // 产线编码
      //   areaDesc: null, // 产线描述
      //   productRequestName: null, //工单
      //   materialPosition: null, // 位置
      //   consumableName: null, // 标签条码
      //   consumableSpecName: null, // 产品编码
      //   consumableSpecDesc: null, // 产品编码
      //   quantity: null, // 数量
      //   consumeUnit: null, // 单位
      // },
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        consumableName: false,
        materialPosition: false
      },
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    // 'model.materialPosition': {
    //   handler(val) {
    //     this.changeMaterialPosition(val)
    //   }
    // },
    'model.consumableName': {
      handler(res) {
        if (res && res.indexOf(';') > -1) {
          let consumableName = res.split(';')[0]
          let materialPosition = res.split(';')[2] || '' // 自动带出位置
          this.$set(this.model, 'consumableName', consumableName)
          this.$set(this.model, 'materialPosition', materialPosition)
        } else {
          this.changeConsumableName(res)
        }
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述
        productRequestName: null, //工单
        materialPosition: null, // 位置
        consumableName: null, // 标签条码
        consumableSpecName: null, // 产品编码
        consumableSpecDesc: null, // 产品编码
        quantity: null, // 数量
        consumeUnit: null, // 单位
      }
    },
    onSkip() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: `/pages/cellFront/materialFeeding/detail?machineName=${this.model.machineName}`
      })
    },
    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的设备编号')
      }
      this.focusObj.consumableName = false
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.consumableName = true
      this.select = false
    },
    submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        processOperationName: this.model.processOperationName,
        areaName: this.model.areaName,
        productSpecName: this.selectProduct.productSpecName,
        bomId: this.selectProduct.bomId,
        consumableName: this.model.consumableName,
        machineName: this.model.machineName,
        materialPosition: this.model.materialPosition,
        productRequestName: this.model.productRequestName,
        quantity: this.model.quantity,
        userId: this.$getLocal(USER_ID)
      }
      this.$service.materialFeeding.consumable(params).then(res => {
        this.$Toast('上料成功')
        this.model.materialPosition = null // 位置
        this.model.consumableName = null // 标签条码
        this.model.consumableSpecName = null // 产品编码
        this.model.consumableSpecDesc = null// 产品编码
        this.model.quantity = null // 数量
        this.model.consumeUnit = null // 单位
      })
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return

      this.machineNameFlag = false
      this.model.machineDesc = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null

      this.model.productRequestName = null
      this.model.materialPosition = null
      this.model.consumableName = null
      this.model.consumableSpecDesc = null
      this.model.consumableSpecName = null
      this.model.consumeUnit = null
      this.model.quantity = null

      this.columns = []
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.materialFeeding.queryPdaMachineName(params)
        this.machineNameFlag = true
        this.model.machineDesc = res.data.machineDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc
        this.columns = res.data.pdaProductRequestList
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 位置 */
    changeMaterialPosition(value) {
      if (!value) return
      let params = {
        // machineName: this.model.machineName,
        materialPosition: value,
      }
      // this.$service.materialFeeding.checkMaterialPosition(params)
    },
    /* 标签条码 */
    async changeConsumableName(value) {
      if (!value) return
      this.model.consumableSpecDesc = null
      this.model.consumableSpecName = null
      this.model.consumeUnit = null
      this.model.quantity = null
      let params = {
        consumableName: value
      }
      try {
        let res = await this.$service.materialFeeding.queryConsumableName(params)
        this.model.consumableSpecDesc = res.data.consumableSpecDesc
        this.model.consumableSpecName = res.data.consumableSpecName
        this.model.consumeUnit = res.data.consumeUnit
        this.model.quantity = res.data.quantity
      } catch (error) {
        this.model.consumableName = null
        this.model.materialPosition = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'C1Z001001'
          break;
        case 'materialPosition':
          let res = '20221013000009-003;1000;3010011;10006;131430;1000000072;2022-10-11;2023-01-09;2022-10-13'
          if (res.indexOf(';')) {
            let materialPosition = res.split(';')[2]
            this.model.materialPosition = materialPosition
          }
          break;
        case 'consumableName':
          let res1 = '20221013000009-003;1000;3010011;10006;131430;1000000072;2022-10-11;2023-01-09;2022-10-13'
          this.model.consumableName = res1
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'materialPosition') {
        uni.scanCode({
          success: (res) => {
            let str = res.result
            if (str.indexOf(';')) {
              let materialPosition = res.split(';')[2] || ''
              this.$set(this.model, key, materialPosition)
            } else {
              this.$set(this.model, key, res.result)
            }
          },
        })
      }
      if (key === 'consumableName') {
        uni.scanCode({
          success: (res) => {
            let str = res.result
            if (str.indexOf(';')) {
              let consumableName = res.split(';')[0]
              let materialPosition = res.split(';')[2] || '' // 自动带出位置
              this.$set(this.model, 'consumableName', consumableName)
              this.$set(this.model, 'materialPosition', materialPosition)
            } else {
              this.$set(this.model, key, res.result)
            }
          },
        })
      }

      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
// .status-bar {
//   height: var(--status-bar-height);
//   width: 100%;
// }
</style>
