<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="合卷上卷" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineDescription }} </view>
        </u-form-item>

        <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="可上极卷" borderBottom labelWidth="100">
          <view class="w100x flex right"> 极卷查询 </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="170">
          <u--input v-model="model.poleRollLotName" border="none" placeholder="请扫描极卷条码" @focus="focusEvent('poleRollLotName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('poleRollLotName')"></view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="极卷数量(m)" borderBottom labelWidth="130">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.poleRollQuantity" border="none" readonly type="number"></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>

    <view class="btnContainer" @click="submit">上卷</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.poleRollLotName': {
      handler(res) {
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/Polar/modules/Upgradeable?machineName=${this.model.machineName}&productOrderName=${this.model.productOrderName}&type=PolarCombined`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        lotName: null, //	出货牌号	string	
        machineDescription: null, //	设备描述	string	
        machineName: null, //	设备名	string	
        machinePolarity: null, //设备极性	string	
        poleRollLoadingLotName: null, //上卷条码	string	
        poleRollLoadingQuantity: null, //	上卷数量	number	
        poleRollLoadingTime: null, //上卷时间	string(date-time)	
        poleRollLoadingType: null, //上卷类型	string	
        poleRollLotName: null, //	极卷条码	string	
        poleRollQuantity: null, //极卷数量	number	
        processOperationName: null, //	工序	string	
        productSpecDesc: null, //产品描述	string	
        productSpecName: null, //产品编码	string	
        productOrderName: null, //	工单号
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoadingCheck(params).then(res => {
        if (res.success && res.message) {
          this.show = true
          this.content = res.message
        } else {
          this.$Toast('合卷上卷成功!')
          this.initModel()
        }
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 设备 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.Polar.getMachineDataForJoinRoll(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 极卷编码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        machineName: this.model.machineName,
        poleRollLotName: value,
        productOrderName: this.model.productOrderName
      }
      try {
        let res = await this.$service.Polar.getPoleRollDataForRoll(params)
        if (res.success) {
          if (res.datas.length == 0) return this.$Toast('极卷编码不存在！')
          this.model.poleRollQuantity = res.datas[0].poleRollQuantity
        }
      } catch (error) {
        this.model.poleRollLotName = ''
      }
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'YN-GZ-WG-ZP-401'
          break;
        case 'poleRollLotName':
          this.model.poleRollLotName = 'LOTMOUNTTEST01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'poleRollLotName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
