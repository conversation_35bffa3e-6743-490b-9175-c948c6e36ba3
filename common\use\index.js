/**
 * 获取文本
 * @param array 要比较的数组
 * @param value 要比较的值
 * @param field 显示的字段
 * @param compareField 要比较的字段
 * @returns {string}
 */
export const getArrayText = function(array, value, field = 'i18n', compareField = 'value') {
	let levelText = ''
	let current = array.find(item => item[compareField] === value)
	if (current) {
		levelText = current[field]
	}
	return levelText
}

const common = {}
common.install = function(Vue, options) {
	Vue.prototype.getArrayText = function(array, value, field = 'i18n', compareField = 'value') {
		return getArrayText(array, value, field, compareField)
	}
};

export default common