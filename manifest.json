{
    "name" : "骆驼TH-MES",
    "appid" : "__UNI__21A39F9",
    "description" : "",
    "versionName" : "2.6.5",
    "versionCode" : 200,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "safearea" : {
            // "background": "#fff", // 默认白色
            "bottom" : {
                "offset" : "none"
            }
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "Barcode" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_PRIVILEGED\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SECURE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_USER_DICTIONARY\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx9df58fb1f968a893",
                        "UniversalLinks" : ""
                    }
                },
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : "",
                    "xxxhdpi" : ""
                },
                "ios" : {
                    "iphone" : {
                        "app@2x" : "",
                        "app@3x" : "",
                        "notification@2x" : "",
                        "notification@3x" : "",
                        "settings@2x" : "",
                        "settings@3x" : "",
                        "spotlight@2x" : "",
                        "spotlight@3x" : ""
                    },
                    "appstore" : "",
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "notification" : "",
                        "notification@2x" : "",
                        "proapp@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : ""
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    "h5" : {
        "title" : "骆驼替换-MES",
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "template" : "index.html",
        "devServer" : {
            "port" : "",
            "proxy" : {
                "/luoto-mes" : {
                    "target" : "http://**********:30844",
                    //"target" : "http://localhost:8030",
                    //"target" : "http://*************:32057",
                    // "target" : "http://***********:8030",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/api" : "/"
                    }
                },
                "/getech-lt-board" : {
                    "target" : "http://**********:30844",
                    //"target" : "http://**********:30844",
                    //"target" : "http://localhost:8033",
                    // "target" : "http://***********:8030",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/api" : "/"
                    }
                },
                "/api/poros-authcenter" : {
                    "target" : "http://**********:30844",
                    //"target" : "http://*************:32057",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/api" : "/"
                    }
                },
                "/api/poros-permission" : {
                    "target" : "http://**********:30844",
                    //"target" : "http://*************:32057",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/api" : "/"
                    }
                }
            }
        }
    },
    /* 小程序特有相关 */
    "mp-weixin" : {
        /* 微信小程序特有相关 */
        "appid" : "wx9df58fb1f968a893",
        "setting" : {
            "urlCheck" : false,
            "es6" : false,
            "postcss" : true,
            "minified" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : ""
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "quickapp-webview" : {
        "icon" : "static/applogo.png"
    },
    "sassImplementationName" : "node-sass"
}
