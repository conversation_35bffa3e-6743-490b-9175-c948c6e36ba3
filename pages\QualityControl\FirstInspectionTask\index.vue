<template>
  <view class="bc_fff myContainerPage">
    <u-navbar title="首检任务明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <!-- <view class="myContainer ma5"> -->
    <view class="myContainer ml10 mr10 mb10">
      <u--form class="ml10 mr10" labelPosition="left" :model="form" labelWidth="100">
        <u-form-item label="设备号:" required labelWidth="100">
          <u--input v-model="form.sbh" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('sbh')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('sbh')"></view>
        </u-form-item>
        <u-form-item label="设备描述:" labelWidth="100">
          <view class="w100x flex right">
            {{ form.sbms }}
          </view>
        </u-form-item>
        <u-form-item label="工序:" required labelWidth="130">
          <view class="w100x flex right" @click="selectReasonCodeType('gx')">
            <view>{{ form.gx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10">现存首检任务</view>
      <scroll-view scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(ele, index) in simpleTrackProduct" :key="index">
            <view class="flex between ma10 mb10 bb_eee hcenter">
              <view class="flex1 mr10 pb10">
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">首检任务编码:</view>
                  <view>{{ ele.taskNo }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">任务创建方式:</view>
                  <view
                    >{{ ele.createTypeDictText }} <span v-if="ele.createTypeDictText == '手动创建'">{{ '(' + ele.createUser + ')' }}</span></view
                  >
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">任务状态:</view>
                  <view>{{ ele.inspectStateDictText }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">创建时间:</view>
                  <view>{{ ele.createTime }}</view>
                </view>
              </view>
              <view class="flex flex_column">
                <view class="btn pl10 pr10" :class="canJunmpAction(1, ele) ? 'cantBtn' : ''" style="margin-bottom: 20rpx" @click="delAction(ele)">删除任务</view>
                <view class="btn pl10 pr10" :class="canJunmpAction2(1, ele) ? 'cantBtn' : ''" style="margin-bottom: 20rpx" @click="junmAction(1, ele)">开始取样</view>
                <view class="btn pl10 pr10" :class="canJunmpAction2(2, ele) ? 'cantBtn' : ''" style="margin-bottom: 20rpx" @click="junmAction(2, ele)">参数录入</view>
                <view class="btn pl10 pr10" :class="canJunmpAction2(3, ele) ? 'cantBtn' : ''" @click="junmAction(3, ele)">整体判定</view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <!-- </view> -->
    <view class="btnContainer" @click="submit">创建任务</view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.sbh': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'form.gx': {
      handler(val) {
        this.getInspectionTaskList(val)
      }
    },
  },
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      form: {
        sbh: '',
        sbms: '',
        gx: '',
        gxType: '',
      },
      simpleTrackProduct: [
      ],
      columns: [
        []
      ],
      select: false,
      GetReasonCodeTypeList: []
    };
  },

  onLoad(e) {
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    junmAction(type, item) {
      if (type == 1) {
        if (item.inspectStateDictText == '待取样') {
          uni.navigateTo({
            url: `/pages/QualityControl/FirstInspectionSample/index?processOperationName=${this.form.gx}&machineName=${this.form.sbh}&taskNo=${item.taskNo}&createTypeDictText=${item.createTypeDictText}&inspectStateDictText=${item.inspectStateDictText}`,
          })
        }
      } else if (type == 2) {
        if (item.inspectStateDictText == '检验中' || item.inspectStateDictText == '待判定') {
          // 只有检验，待判定可以参数录入
          let params = {
            taskNo: item.taskNo,
            createTypeDictText: item.createTypeDictText,
            inspectStateDictText: item.inspectStateDictText,
            machineName: item.machineName,
            processOperationName: item.processOperationName,
            lotName: item.lotName, //  样品条码
            workOrderName: item.workOrderName, // 工单号
            productSpecName: item.productSpecName, //  产品编码
          }
          uni.navigateTo({
            url: `/pages/QualityControl/FirstInspectionParams/index?params=${JSON.stringify(params)}`,
          })
        }
      } else if (type == 3) {
        if (item.inspectStateDictText == '待判定') {
          // 只有待判定中可以整体判定
          uni.navigateTo({
            url: `/pages/QualityControl/FirstInspectionDecision/index?processOperationName=${this.form.gx}&machineName=${this.form.sbh}&taskNo=${item.taskNo}&createTypeDictText=${item.createTypeDictText}&inspectStateDictText=${item.inspectStateDictText}`,
          })
        }
      }
    },
    canJunmpAction2(type, item) {
      if (type == 1) {
        if (item.inspectStateDictText == '待取样') {
          return false
        } else {
          return true
        }
      } else if (type == 2) {
        if (item.inspectStateDictText == '检验中' || item.inspectStateDictText == '待判定') {
          // 只有检验，待判定可以参数录入
          return false
        } else {
          return true
        }
      } else if (type == 3) {
        if (item.inspectStateDictText == '待判定') {
          // 只有待判定中可以整体判定
          return false
        } else {
          return true
        }
      }
    },
    GetReasonCodeType() {
      const params = {
        inspectType: 'FirstInspection',
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return item.reasonCodeType + '/' + item.typeDesc
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if (type == 'gx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if (this.selectType == 'gx') {
        this.form.gx = e.value[0].label
        this.form.gxType = e.value[0].value
      }
      this.select = false
    },
    initModel() {
      this.form = {
        sbh: '',
        sbms: '',
        gx: '',
        gxType: '',
      }
    },
    focusEvent(type) {
      // this.form[type] = ''
    },
    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        inspectType: 'FirstInspection',
        machineName: value
      }
      try {
        let res = await this.$service.QualityControl.getMachineDataForFirstInspection(params)
        console.log('getMachineDataForFirstInspection', res);
        if (res.datas.length > 0) {
          let data = res.datas[0]
          this.form = {
            ...res.datas[0],
            sbh: this.form.sbh,
            sbms: data.description,
            gx: ''
          }
          this.GetReasonCodeTypeList = data.machineGroupName.split(',').map(item => {
            return {
              label: item,
              value: item
            }
          })
        } else {
          this.form.sbh = ''
          this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 任务列表 */
    async getInspectionTaskList(val) {
      if (!this.form.gx) return
      let params = {
        inspectType: 'FirstInspection',
        machineName: this.form.machineName,
        processOperationName: this.form.gx
      }
      try {
        this.simpleTrackProduct = []
        let res = await this.$service.QualityControl.getInspectionTaskList(params)
        if (res.datas.length > 0) {
          // let data =  res.datas[0]
          this.simpleTrackProduct = res.datas
        } else {
          this.simpleTrackProduct = []
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        // this.initModel()
      }
    },
    submit(item) {
      // 创建首检任务
      let params = {
        inspectType: 'FirstInspection',
        machineName: this.form.machineName,
        processOperationName: this.form.gx
      }
      this.$service.QualityControl.startInspectionTask(params).then((res) => {
        console.log('startInspectionTask', res);
        if (res.datas.length > 0) {
          this.$Toast('创建首检任务成功！')
          setTimeout(() => {
            // 刷新任务列表
            this.getInspectionTaskList(this.form.gx)
          }, 500)
        } else {
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      })
    },
    delAction(item) {
      if (item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return
      }
      uni.showModal({
        title: '提示',
        content: `是否确认删除首检任务？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // 删除首检任务
            let params = {
              inspectType: 'FirstInspection',
              taskNo: item.taskNo
            }
            this.$service.QualityControl.inspectionTaskDelete(params).then((res) => {
              console.log('inspectionTaskDelete', res);
              this.$Toast('删除任务成功！')
              // 刷新任务列表
              this.getInspectionTaskList(this.form.gx)
            })
          }
          if (res.cancel) { }
        },
      })

    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.form, key, res.result)
        },
      })
    },
    canJunmpAction(type, item) {
      if (item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return true
      } else {
        return false
      }
    }
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }

  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
.btn {
  margin: 0 auto;
  height: 34px;
  line-height: 34px;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 13px;
  text-align: center;
  border-radius: 11px;
}
.cantBtn {
  background-color: #eee;
}
</style>