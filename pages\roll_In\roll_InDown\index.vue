<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="辊压工序-完工" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="辊压机" prop="machineName" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="辊压机描述" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.description }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processOperationName ? model.processOperationName + ' / ' + model.processOperationDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.productSpecName }} </view>
        </u-form-item>

        <!-- <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.lotName }} </view>
        </u-form-item> -->

        <u-form-item label="开始时间" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processStartTime ? moment(model.processStartTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">产出确认</view>
        <view v-if="singlerRollFinishParames.length > 0">
          <view v-for="(item, index) in singlerRollFinishParames" :key="index">
            <u-form-item label="出货牌号" borderBottom labelWidth="100">
              <view class="w100x flex right">{{ item.lotName }} </view>
            </u-form-item>

            <!-- <u-form-item label="良品数量(设备)" borderBottom labelWidth="140">
              <u--input readonly v-model="item.outPutByMachine.outPutQuantityM" border="none"></u--input><span class="lin1 ml2">m</span> <u--input readonly v-model="item.outPutByMachine.outPutQuantityEA" border="none"></u--input
              ><span class="lin1 ml2">ea</span>
            </u-form-item> -->

            <u-form-item required label="良品数量" borderBottom labelWidth="140">
              <u--input v-model="item.outPutByUser.outPutQuantityM" border="none" @input="changeNumber($event, index)"></u--input><span class="lin1 ml2">m</span> <u--input :disabled="isDisabled" v-model="item.outPutByUser.outPutQuantityEA" border="none" @input="changeEA($event, index)"></u--input
              ><span class="lin1 ml2">ea</span>
            </u-form-item>

            <u-form-item required label="坏品数量" borderBottom labelWidth="140">
              <u--input v-model="item.outPutByUser.lossCountM" border="none" @input="changeNumber2($event, index)"></u--input><span class="lin1 ml2">m</span> <u--input :disabled="isDisabled" v-model="item.outPutByUser.lossCountEA" border="none" @input="changeEA2($event, index)"></u--input
              ><span class="lin1 ml2">ea</span>
            </u-form-item>

            <u-form-item label="总坏品数量" borderBottom labelWidth="120">
              <view class="w100x flex right"> {{ getNgQuantity(index) }} </view>
              <u-icon class="ml15" @click="addAction(index)" name="plus-circle" color="#2979ff" size="28"></u-icon>
            </u-form-item>

            <view v-for="(item2, index2) in item.reasonList" :key="index2">
              <u-form-item label="不合格代码" borderBottom required labelWidth="100">
                <view class="w100x flex right">
                  <view>{{ item2.reasonCode }}</view>
                  <u-icon name="arrow-down" @click="selectReasonCode(index, index2)" color="black" size="18"></u-icon>
                  <u-icon class="ml15" @click="delAction(index, index2)" name="minus-circle" color="#2979ff" size="28"></u-icon>
                </view>
              </u-form-item>

              <u-form-item label="坏品数量" borderBottom required labelWidth="130">
                <u--input type="number" placeholder="请输入坏品数量" v-model="item2.ngQuantity" border="none"></u--input>
              </u-form-item>
            </view>

            <!-- <u-form-item required label="厚度(um)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.thickness" border="none" ></u--input></u-form-item> -->
            <!-- <u-form-item required label="弧高(mm)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.sagitta" border="none" ></u--input></u-form-item> -->
            <u-form-item required label="弧高左(mm)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.sagitta" border="none"></u--input></u-form-item>
            <u-form-item required label="弧高右(mm)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.sagittaRight" border="none"></u--input></u-form-item>
            <u-form-item required label="厚度左(um)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.thicknessLeft" border="none"></u--input></u-form-item>
            <u-form-item required label="厚度中(um)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.thickness" border="none"></u--input></u-form-item>
            <u-form-item required label="厚度右(um)" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.thicknessRight" border="none"></u--input></u-form-item>

            <u-form-item required label="停机位数" borderBottom labelWidth="140"> <u--input v-decimal3 v-model="item.downCount" border="none"></u--input></u-form-item>
          </view>
        </view>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <!-- <view class="btnContainer2">
      <view @click="submit">完工</view>
      <view class="disabled">条码打印</view>
    </view> -->
    <view class="btnContainer" @click="submit">完工</view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import PrintPackageMixin from "@/mixins/printPackage";
import moment from 'moment'
export default {
  mixins: [RedScan, PrintPackageMixin],
  name: 'stirFeeding',
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备号不能为空',
      },
      model: {},
      machineNameFlag: false,
      checkMachineOutput: false, // 是否需要校验IOT设备上报良品数量
      ratio: null,  // 单位转换
      isDisabled: false,
      singlerRollFinishParames: [{
        lotName: '', // 出货牌号
        outPutByMachine: {
          outPutQuantityEA: null, // 良品数量(ea)
          outPutQuantityM: null, // 良品数量(m)
          lossCountEA: null, // 坏品数量(ea)
          lossCountM: null, // 坏品数量(m)
        },
        outPutByUser: { // 	良品数量(人工)
          outPutQuantityEA: null, // 良品数量(ea)
          outPutQuantityM: null, // 良品数量(m)
          lossCountEA: null, // 坏品数量(ea)
          lossCountM: null, // 坏品数量(m)
        },
        // thickness: null, // 厚度
        // sagitta: null, // 弧高
        thicknessLeft: null,
        thickness: null,
        thicknessRight: null,
        sagitta: null,
        sagittaRight: null,
        downCount: null, // 停机位数
        reasonList: [],
      }],
      GetReasonCodeList: [],
      select: false,
      chooseIndex: '',
      chooseIndex2: '',
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    addAction(index) {
      this.singlerRollFinishParames[index].reasonList.push({
        reasonCode: '',
        ngQuantity: '',
        reasonCodeType: '',
        reasonCodeText: ''
      })
    },
    delAction(index, index2) {
      this.singlerRollFinishParames[index].reasonList.splice(index2, 1)

    },
    getNgQuantity(index) {
      let data = this.singlerRollFinishParames[index].reasonList
      let num = 0
      data.forEach(item => {
        if (item.ngQuantity) {
          num += Number(item.ngQuantity)
        } else {
          num += 0
        }
      })
      return num
    },
    selectReasonCode(index, index2) {
      this.columns = this.GetReasonCodeList
      this.select = true
      this.chooseIndex = index
      this.chooseIndex2 = index2
    },
    selectFirm(e) {
      // if (e.value[0].reasonCodeType) {
      //   // this.model.reasonCodeType = e.value[0].reasonCodeType
      //   // this.GetReasonCode(this.model.reasonCodeType)
      // } else {
      //   this.model.reasonCode = e.value[0].reasonCode
      // }
      this.singlerRollFinishParames[this.chooseIndex].reasonList[this.chooseIndex2].reasonCode = e.value[0].labelText
      this.singlerRollFinishParames[this.chooseIndex].reasonList[this.chooseIndex2].reasonCodeType = e.value[0].reasonCode
      this.singlerRollFinishParames[this.chooseIndex].reasonList[this.chooseIndex2].reasonCodeText = e.value[0].description
      this.select = false
    },
    GetReasonCode() {
      const params = {
        processOperationName: this.model.processOperationName
      }
      this.$service.DieCutting.GetReasonCodeByProcessOperationName(params).then(res => {
        this.GetReasonCodeList = res.datas
        this.GetReasonCodeList.forEach(item => {
          item.labelText = item.reasonCode + '/' + item.description
        })
      })
    },
    moment,
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        description: null, // 设备描述:
        processOperationName: null, // 工序
        productOrderName: null, // 工单号
        productSpecName: null, // 产品编码:
        processStartTime: null, // 开始时间
        slittingQuantity: null, // 分切条数
      }
      this.singlerRollFinishParames = []
      setTimeout(() => {
        this.singlerRollFinishParames.push({
          lotName: '', // 出货牌号
          outPutByMachine: {
            outPutQuantityEA: null, // 良品数量(ea)
            outPutQuantityM: null, // 良品数量(m)
            lossCountEA: null, // 坏品数量(ea)
            lossCountM: null, // 坏品数量(m)
          },
          outPutByUser: { // 	良品数量(人工)
            outPutQuantityEA: null, // 良品数量(ea)
            outPutQuantityM: null, // 良品数量(m)
            lossCountEA: null, // 坏品数量(ea)
            lossCountM: null, // 坏品数量(m)
          },
          // thickness: null, // 厚度
          // sagitta: null, // 弧高
          thicknessLeft: null,
          thickness: null,
          thicknessRight: null,
          sagitta: null,
          sagittaRight: null,
          downCount: null, // 停机位数
          reasonList: [],
        })
      }, 100);
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value,
        operationType: 'Rolling'
      }
      try {
        let res = await this.$service.Polar.getMachineDataForFinish(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
          if (this.model.operationType && this.model.operationType != 'Rolling') {
            this.$Toast('辊压机不存在!')
            this.initModel()
            return
          }
          this.GetReasonCode()
          if (res.datas[0].singlerRollFinishParames.length > 0) {
            this.singlerRollFinishParames = res.datas[0].singlerRollFinishParames.map(item => {
              return {
                ...item,
                reasonList: []
              }
            })
            this.singlerRollFinishParames.forEach(item => {
              if (!item.outPutByMachine) {
                item.outPutByMachine = {
                  outPutQuantityEA: null, // 良品数量(ea)
                  outPutQuantityM: null, // 良品数量(m)
                  lossCountEA: null, // 坏品数量(ea)
                  lossCountM: null, // 坏品数量(m)
                }
              }
              if (!item.outPutByUser) {
                item.outPutByUser = {
                  outPutQuantityEA: null, // 良品数量(ea)
                  outPutQuantityM: null, // 良品数量(m)
                  lossCountEA: null, // 坏品数量(ea)
                  lossCountM: null, // 坏品数量(m)
                }
              }
            })
          } else {
            this.singlerRollFinishParames = [{
              lotName: '', // 出货牌号
              outPutByMachine: {
                outPutQuantityEA: null, // 良品数量(ea)
                outPutQuantityM: null, // 良品数量(m)
                lossCountEA: null, // 坏品数量(ea)
                lossCountM: null, // 坏品数量(m)
              },
              outPutByUser: { // 	良品数量(人工)
                outPutQuantityEA: null, // 良品数量(ea)
                outPutQuantityM: null, // 良品数量(m)
                lossCountEA: null, // 坏品数量(ea)
                lossCountM: null, // 坏品数量(m)
              },
              // thickness: null, // 厚度
              // sagitta: null, // 弧高
              thicknessLeft: null,
              thickness: null,
              thicknessRight: null,
              sagitta: null,
              sagittaRight: null,
              downCount: null, // 停机位数
              reasonList: []
            }]
          }
          const data = {
            processOperationName: this.model.processOperationName,
            productSpecName: this.model.productSpecName
          }
          this.$service.common.findAllWithEquals(data).then(res => {
            if (res.datas.length > 0) {
              this.ratio = Number(res.datas[0].ratio)
              this.isDisabled = true
            } else {
              this.isDisabled = false
            }
          })
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    // 完工
    submit() {

      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let flag = false
      let msg = ''
      this.model.singlerRollFinishParames = JSON.parse(JSON.stringify(this.singlerRollFinishParames))
      // 判断是否有空值
      let ngInfoList = []

      this.model.singlerRollFinishParames.map((item, index) => {
        if (!Number(item.outPutByUser.outPutQuantityEA) || !Number(item.outPutByUser.outPutQuantityM)) {
          msg = '请填写良品数量!'
          flag = true
        }
        // if (!Number(item.outPutByUser.lossCountEA) || !Number(item.outPutByUser.lossCountM)) {
        //   msg = '请填写坏品数量!'
        //   flag = true
        // }
        if ((!Number(item.outPutByUser.lossCountEA) && Number(item.outPutByUser.lossCountEA) != 0) || (!Number(item.outPutByUser.lossCountM) && Number(item.outPutByUser.lossCountM) != 0)) {
          msg = '请填写坏品数量!'
          flag = true
        }
        // if (!Number(item.thickness)) {
        //   msg = '请填写厚度!'
        //   flag = true
        // }
        // if (!Number(item.sagitta)) {
        //   msg = '请填写弧高!'
        //   flag = true
        // }
        if (!Number(item.sagitta)) {
          msg = '请填写弧高(左)!'
          flag = true
        }
        if (!Number(item.sagittaRight)) {
          msg = '请填写弧高(右)!'
          flag = true
        }
        if (!Number(item.thicknessLeft)) {
          msg = '请填写厚度(左)!'
          flag = true
        }
        if (!Number(item.thickness)) {
          msg = '请填写厚度(中)!'
          flag = true
        }
        if (!Number(item.thicknessRight)) {
          msg = '请填写厚度(左)!'
          flag = true
        }
        if (!Number(item.downCount) && Number(item.downCount) != 0) {
          msg = '请填写停机位数!'
          flag = true
        }
        if (item.reasonList && item.reasonList.length > 0) {
          let arr = item.reasonList.map(item2 => {
            if (!item2.reasonCode) {
              msg = '请选择不合格代码!'
              flag = true
            }
            if (!item2.ngQuantity) {
              msg = '请填写坏品数量!'
              flag = true
            }
            return {
              ngQuantity: item2.ngQuantity,
              reasonCode: item2.reasonCodeType,
              reasonCodeDesc: item2.reasonCodeText
            }
          })
          ngInfoList.push({
            lotName: item.lotName ? item.lotName : this.model.lotName,
            ngDetailInfoList: arr
          })
        }
        if (item.outPutByUser.lossCountM != this.getNgQuantity(index)) {
          msg = '坏品数量与总坏品数量不一致!'
          flag = true
        }
      })
      if (flag) return this.$Toast(msg)


      // let lotNameArr = this.singlerRollFinishParames.map(item=>item.lotName)
      // let str = lotNameArr.join(',')
      let str = this.singlerRollFinishParames[0].outPutByUser.outPutQuantityM
      uni.showModal({
        title: '提示',
        content: `设备${this.model.machineName}，现产出${str}米, 请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let obj = {
              machineName: this.model.machineName,
              processOperationName: this.model.processOperationName,
              ngInfoList: ngInfoList
            }
            console.log('obj', obj);
            // this.$service.Size.recordNgInfo(obj).then(res => {})
            let params = {
              ...this.model,
              operationType: 'Rolling'
            }
            this.$service.Polar.singlerRollFinishMsgProcessor(params).then(res => {
              this.$Toast('操作成功！')
              this.$service.Size.recordNgInfo(obj).then(res => { })
              setTimeout(() => {
                // 如果工序是C开头的话就要调打出货牌，其他的就不调
                if (this.model.processOperationName[0] == 'C') {
                  this.printPackage(res.datas[0], 'Out') // 打印
                }
                this.initModel()
              }, 800);
            }).catch(() => {
              // this.initModel()
            })
          }
          if (res.cancel) { }
        },
      })

    },
    // 单位转换
    changeNumber(e, index) {
      e = e && (e.match(/^\d*(\.?\d{0,3})/g)[0])
      if (this.ratio) {
        this.singlerRollFinishParames.forEach((item, i) => {
          if (i == index) {
            item.outPutByUser.outPutQuantityEA = Math.round(Number(item.outPutByUser.outPutQuantityM) * this.ratio)
            this.$nextTick(() => {
              this.$set(item.outPutByUser, 'outPutQuantityM', e)
            })
          }
        })
      }
    },
    // ea控制整数
    changeEA(e, index) {
      let str = e && e + ""
      let result = str && (str.match(/^\d*/g)[0])
      this.singlerRollFinishParames.forEach((item, i) => {
        if (i == index) {
          this.$nextTick(() => {
            this.$set(item.outPutByUser, 'outPutQuantityEA', result)
          })
        }
      })
    },
    // 单位转换
    changeNumber2(e, index) {
      e = e && (e.match(/^\d*(\.?\d{0,3})/g)[0])
      if (this.ratio) {
        this.singlerRollFinishParames.forEach((item, i) => {
          if (i == index) {
            item.outPutByUser.lossCountEA = Math.round(Number(item.outPutByUser.lossCountM) * this.ratio)
            this.$nextTick(() => {
              this.$set(item.outPutByUser, 'lossCountM', e)
            })
          }
        })
      }
    },
    // ea控制整数
    changeEA2(e, index) {
      let str = e && e + ""
      let result = str && (str.match(/^\d*/g)[0])
      this.singlerRollFinishParames.forEach((item, i) => {
        if (i == index) {
          this.$nextTick(() => {
            this.$set(item.outPutByUser, 'lossCountEA', result)
          })
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.FJGY.01.03'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.model.machineName = res.result
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>