import CryptoJS from "@/utils/des_encrypt/crypto-js.js";
export const encryptByDES = (message, keyvi) => {
  var key = CryptoJS.MD5(keyvi).toString();
  var iv = CryptoJS.MD5(keyvi).toString();
  var crypto_key = CryptoJS.enc.Utf8.parse(key);
  var crypto_iv = CryptoJS.enc.Utf8.parse(iv.substr(0, 8));

  var encode_str = CryptoJS.TripleDES.encrypt(message, crypto_key, {
    iv: crypto_iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encode_str.toString();
};
