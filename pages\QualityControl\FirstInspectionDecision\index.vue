<template>
  <view class="bc_fff myContainerPage">
    <u-navbar title="首检整体判定" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"></u-navbar>
    <view class="myContainer ma5">
      <u--form class="ml10 mr10" labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="首检任务编码:" required labelWidth="120">
          <view class="w100x flex right" @click="selectReasonCodeType('sjrwbm')">
            <view>{{ model.sjrwbm }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>
        <u-form-item label="设备号:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.sbh }}
          </view>
        </u-form-item>
        <u-form-item label="工序:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.gx }}
          </view>
        </u-form-item>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">样品检验结果</view>
      <view class="listContainer mb10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1 pa2">样品条码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验状态</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验结果</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in list" :key="index" class="flex bl_e1e1e1 h40">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 pa2 txt_c">{{ item.lotName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.lotState_dictText }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.inspectResult }}</view>
            </view>
            <view v-if="!list || list.length === 0">
              <NoData></NoData>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">不良品录入</view>
      <view v-if="list.length > 0">
        <view v-for="(ele, index) in list2" :key="index">
          <view class="flex between ma10 mb10 bb_eee hcenter">
            <view class="flex1 mr10 pb10">
              <view class="flex h40 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">样品条码:</view>
                <view>{{ ele.lotName }}</view>
              </view>
              <view class="flex h40 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">不良信息:</view>
                <view class="w150 between flex">
                  <!-- <view>{{ ele.blxx }}</view> -->
                  <u--input clearable v-model="ele.blxx" border="none" placeholder="请输入" @change="(val) => jgzChange(val)"></u--input>
                  <u-icon name="arrow-down" color="black" size="18" @click="selectReasonCodeType('blxx', index)"></u-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <NoData v-else></NoData>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb10">总体判定结果</view>
      <view class="result_span"
        ><span>整体判定结果：</span><span class="fb">{{ getResultText() }}</span></view
      >
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">整体判定</view>
  </view>
</template>
<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'model.sjrwbm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
  },
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.jgzChange = this.$debounce(this.jgzChange, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      model: {
        sjrwbm: '',
        sbh: '',
        gx: ''
      },
      simpleTrackProduct: [],
      columns: [
        []
      ],
      select: false,
      GetReasonCodeTypeList: [],
      GetReasonCodeTypeList2: [],
      list: [],
      list2: [],
      chooseIndex: 0
    };
  },
  onLoad(options) {
    let { taskNo, processOperationName, machineName } = options
    this.model.sjrwbm = taskNo
    this.model.sbh = machineName
    this.model.gx = processOperationName
    // 获取下拉
    this.GetReasonCodeType()
    this.GetReasonCodeType2()
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('getInspectionTaskList')
    },
    GetReasonCodeType() {
      const params = {
        inspectType: 'FirstInspection',
      }
      this.$service.QualityControl.waitJudgeGetTaskNoList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            ...item,
            label: item.taskNo,
            value: item.taskNo
          }
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    GetReasonCodeType2(val) {
      const params = {
        reasonCode: val ? val : ''
      }
      this.$service.QualityControl.waitJudgeGetReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList2 = res.datas.map((item) => {
          return {
            label: item.reasonCode + '/' + item.codeDesc,
            value: item.reasonCode,
            codeDesc: item.codeDesc
          }
        })
        console.log('this.GetReasonCodeTypeList2', this.GetReasonCodeTypeList2);
      })
    },
    selectReasonCodeType(type, index) {
      // if(type == 'yptm') {
      //   this.columns[0] = [].concat(this.GetLotNameList)
      // }
      if (type == 'sjrwbm') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      if (type == 'blxx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList2)
        this.chooseIndex = index
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if (this.selectType == 'sjrwbm') {
        this.model.sjrwbm = e.value[0].label
        this.model.sbh = e.value[0].machineName
        this.model.gx = e.value[0].processOperationName
        // 样品检验结果
        // this.GetReasonCodeType2()
        // 选完工序获取不良现象
        // this.GetReasonCodeType2()
      }
      if (this.selectType == 'blxx') {
        console.log(this.list2, this.chooseIndex, e.value[0]);
        this.$set(this.list2[this.chooseIndex], 'blxx', e.value[0].value)
        // this.$set(this.list2[this.chooseIndex], 'blxxType', e.value[0].value)
      }
      this.select = false
    },
    // initModel() {
    //   this.form = {
    //     machineName: null, // 设备号
    //     machineDescription: null, // 搅拌机描述
    //     quantity: null // 数量
    //   }
    // },
    // focusEvent(type) {
    //   this.form[type] = ''
    // },
    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        inspectType: 'FirstInspection',
        taskNo: this.model.sjrwbm
      }
      try {
        let res = await this.$service.QualityControl.getInspectionTaskDetailList(params)
        console.log('getInspectionTaskDetailList', res);
        if (res.datas.length > 0) {
          this.list = res.datas
          // 筛选不合格
          this.list2 = res.datas.filter(item => {
            return item.inspectResult == '不合格'
          })
        } else {
          this.list = []
        }
      } catch (error) {
        console.log('error', error);
        this.list = []
      }
    },
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      return resArr
    },
    getData(machineName) {
      let params = {
        machineName,
      }
      this.$service.MaterialLoading.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            res.datas.forEach(item => item.qty = 0)
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          } else {
            this.simpleTrackProduct = []
          }
        }
      })
    },
    submit(item) {
      if (!this.model.sjrwbm) {
        this.$Toast('请选择对应的首检任务编码！')
        return
      }
      if (this.list.length == 0) {
        this.$Toast('暂无样品检验结果！')
        return
      }
      let str = this.getResultText()
      uni.showModal({
        title: '提示',
        content: `设备：${this.model.sbh}的检验整体判定为：${str}，请确认`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // if(!this.model.yptm) {
            //   this.$Toast('请选择对应的样品条码')
            //   return
            // }
            let flag = false
            this.list2.forEach((item, index) => {
              if (!item.blxx) flag = true
            })
            if (flag) {
              this.$Toast('不良信息不能为空！')
              return
            }
            this.list.forEach((item, index) => {
              this.list2.forEach(item2 => {
                if (item.lotName == item2.lotName) {
                  this.list[index].reasonCode = item2.blxx
                }
              })
            })
            let str = this.getResultText()
            // 取样完成
            let params = {
              inspectType: 'FirstInspection',
              machineName: this.model.sbh,
              processOperationName: this.model.gx,
              taskNo: this.model.sjrwbm,
              inspectionTaskLotList: this.list,
              inspectResult: str
            }
            this.$service.QualityControl.waitJudgeSave(params).then((res) => {
              console.log('waitJudgeSave', res);
              this.$Toast('首检整体判定成功！')
              // 初始化
              this.model = {
                sjrwbm: '',
                sbh: '',
                gx: ''
              }
              this.list = []
              this.list2 = []
              setTimeout(() => {
                this.GetReasonCodeType()
              }, 500)

            })
          }
          if (res.cancel) { }
        },
      })
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
    getResultText() {
      let flag = false
      this.list.forEach((item) => {
        if (item.inspectResult == '合格') {
          // 有一个合格则整体为合格
          flag = true
        }
      })
      if (flag) {
        return '合格'
      } else {
        if (this.list.length == 0) {
          return '-'
        }
        return '不合格'
      }
    },
    jgzChange(val) {
      this.GetReasonCodeType2(val)
    }
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
.result_span {
  text-align: center;
  font-size: 24px;
}
</style>