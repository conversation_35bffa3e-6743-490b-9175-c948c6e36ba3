<template>
  <view class="bc_fff myContainerPage">
    <u-navbar title="调机" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <!-- <view class="myContainer ma5"> -->
      <view class="myContainer ml10 mr10 mb10">
        <u--form class="ml10 mr10" labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="设备号:" required labelWidth="100">
            <u--input v-model="form.sbh" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('sbh')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('sbh')"></view>
          </u-form-item>
          <u-form-item label="设备描述:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.sbms }}
            </view>
          </u-form-item>
          <u-form-item label="设备人员确认:" required labelWidth="100">
            <u--input v-model="form.machineUser" border="none" focus placeholder="请扫描或输入设备人员" @focus="focusEvent('machineUser')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineUser')"></view>
          </u-form-item>
          <u-form-item label="工艺人员确认:" required labelWidth="100">
            <u--input v-model="form.operationUser" border="none" focus placeholder="请扫描或输入工艺人员" @focus="focusEvent('operationUser')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('operationUser')"></view>
          </u-form-item>
          <u-form-item label="质量人员确认:" required labelWidth="100">
            <u--input v-model="form.qualityUser" border="none" focus placeholder="请扫描或输入质量人员" @focus="focusEvent('qualityUser')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('qualityUser')"></view>
          </u-form-item>
          <!-- <u-form-item label="工序:" required labelWidth="130">
            <view class="w100x flex right" @click="selectReasonCodeType('gx')">
              <view>{{ form.gx }}</view>
              <u-icon name="arrow-down" color="black" size="18"></u-icon>
            </view>
          </u-form-item> -->
        </u--form>
        <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c flex hcenter mt5">
          <view class="mr5">调机参数</view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </view>
        <view class=" h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
          <view class="mr5">人工-录入:</view>
        </view>
        <template v-if="inputList.length > 0">
          <view v-for="(ele, index) in inputList" :key="index">
            <view class="flex between ma10 mb10 bb_eee hcenter">
              <view class="flex1 mr10 pb10">
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w200 c_000">{{ ele.paramName }}:</view>
                  <view class="flex right">
                    <view class="flex hcenter">
                      <u--input placeholder="请输入" v-model="ele.value" border="none"></u--input>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
        <NoData v-else></NoData>
        <view class=" h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
          <view class="mr5">人工-勾选:</view>
        </view>
        <template v-if="selectList.length > 0">
          <view v-for="(ele, index) in selectList" :key="index">
            <view class="flex between ma10 mb10 bb_eee hcenter">
              <view class="flex1 mr10 pb10">
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w200 c_000">{{ ele.paramName }}:</view>
                  <view class="flex right">
                    <view class="flex hcenter">
                      <view>通过：</view>
                      <u-checkbox-group>
                        <u-checkbox
                          @change="(e) => checkboxChange(e, index)"
                          v-model="ele.value" 
                          v-for="(item, index2) in radiolist1" :key="index2" 
                          :name="item.name"
                        >{{item.name}}</u-checkbox>
                      </u-checkbox-group>
                      <!-- <u--input placeholder="请输入" v-model="ele.value" border="none"></u--input> -->
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
        <NoData v-else></NoData>
        <!-- <scroll-view scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        </scroll-view> -->
        <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.sbh': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    // 'form.gx': {
    //   handler(val) {
    //     this.getInspectionTaskList(val)
    //   }
    // },
  },
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        sbh: '设备编号不能为空',
        machineUser: '设备人员不能为空',
        operationUser: '工艺人员不能为空',
        qualityUser: '质量人员不能为空',
      },
      form: {
        sbh: '',
        sbms: '',
        gx: '',
        gxType: '',
        machineUser: '',
        operationUser: '',
        qualityUser: '',
      },
      simpleTrackProduct: [
        // {
        //   paramsName: 'E001环境参数-温度',
        //   paramsType: 'input'
        // },
        // {
        //   paramsName: 'E001环境参数-温度2',
        //   paramsType: 'input'
        // },
        // {
        //   paramsName: 'E001环境参数-温度3',
        //   paramsType: 'select'
        // },
        // {
        //   paramsName: 'E001环境参数-温度4',
        //   paramsType: 'select'
        // }
      ],
      columns: [
        []
      ],
      select: false,
      GetReasonCodeTypeList: [],
      radiolist1: [
        {
          name: '通过'
        }
      ],
    };
  },
  computed: {
    inputList() {
      let arr = this.simpleTrackProduct.filter((item) => item.debugType == 'input')
      return arr
    },
    selectList() {
      let arr = this.simpleTrackProduct.filter((item) => item.debugType == 'select')
      return arr
    },
  },
  onLoad(e) {
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        inspectType: 'ProcessInspection',
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return item.reasonCodeType + '/' + item.typeDesc
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if(type == 'gx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if(this.selectType == 'gx') {
        this.form.gx = e.value[0].label
        this.form.gxType = e.value[0].value
      }
      this.select = false
    },
    initModel() {
      this.form = {
        sbh: '',
        sbms: '',
        gx: '',
        gxType: '',
        machineUser: '',
        operationUser: '',
        qualityUser: '',
      }
    },
    focusEvent(type) {
      // this.form[type] = ''
    },
    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        // inspectType: 'ProcessInspection',
        machineName: value
      }
      try {
        let res = await this.$service.Tuning.getInfoByMahcine(params)
        console.log('getInfoByMahcine', res);
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.form = {
            sbh: this.form.sbh,
            sbms: data.machineDesc
          }
          this.simpleTrackProduct = data.debugMachineList
        } else {
          this.initModel()
          this.simpleTrackProduct = []
          this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
        this.simpleTrackProduct = []
      }
    },
    /* 任务列表 */
    async getInspectionTaskList(value) {
      if (!value) return
      let params = {
        inspectType: 'ProcessInspection',
        machineName: this.form.machineName,
        processOperationName: this.form.gx
      }
      try {
        let res = await this.$service.QualityControl.getInspectionTaskList(params)
        console.log('getInspectionTaskList', res);
        if (res.datas.length > 0) {
          // let data =  res.datas[0]
          this.simpleTrackProduct = res.datas
        } else {
          this.simpleTrackProduct = []
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        // this.initModel()
      }
    },
    checkboxChange(e, index) {
      console.log('checkboxChange', e);
      this.selectList[index].value = e
    },
    submit(item) {
      console.log('submit', this.inputList, this.selectList);
      for (let key in this.rulesTip) {
        if (!this.form[key]) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let flag = false
      this.inputList.forEach(item => {
        if(!item.value)flag = true
      })
      this.selectList.forEach(item => {
        if(!item.value)flag = true
      })
      if(flag) {
        this.$Toast('设备未维护调机参数，请先维护调机参数再操作!')
        return
      }
      let arr = [].concat(this.inputList).concat(this.selectList)
      arr.forEach((item,index) => {
        if(!item.value && item.debugType == 'select') {
          arr[index].debugResult = false
        } else {
          arr[index].debugResult = item.value
        }
      })
      // return
      // 创建巡检任务
      let params = {
        machineDesc: this.form.sbms,
        machineName: this.form.sbh,
        machineUser: this.form.machineUser,
        operationUser: this.form.operationUser,
        qualityUser: this.form.qualityUser,
        debugMachineList: arr
      }
      this.$service.Tuning.saveResult(params).then((res) => {
        console.log('saveResult', res);
        if (res.datas.length > 0) {
          this.$Toast('提交成功！')
          this.initModel()
          this.simpleTrackProduct = []
        } else {
          // this.form.sbh = ''
          // this.$Toast('设备号不存在！')
        }
      })
    },
    delAction(item) {
      if(item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return
      }
      uni.showModal({
          title: '提示',
          content: `是否确认删除巡检任务？`,
          cancelText: '取消',
          confirmText: '确认',
          cancelColor: '#666',
          confirmColor: '#409eff',
          success: (res) => {
            if (res.confirm) {
              // 删除巡检任务
              let params = {
                inspectType: 'ProcessInspection',
                taskNo: item.taskNo
              }
              this.$service.QualityControl.inspectionTaskDelete(params).then((res) => {
                console.log('inspectionTaskDelete', res);
                this.$Toast('删除任务成功！')
                // 刷新任务列表
                this.getInspectionTaskList(this.form.gx)
              })
            }
            if (res.cancel) {}
          },
        })
      
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.form, key, res.result)
        },
      })
    },
    canJunmpAction(type, item) {
      if(item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return true
      } else {
        return false
      }
    },
    gotoQuery() {
      if(!this.form.sbh) {
        this.$Toast('请先输入设备号！')
        return
      }
      uni.navigateTo({
        url: `/pages/cellFront/Tuning/modules/TuningList?machineName=${this.form.sbh}&machineNameDesc=${this.form.sbms}`
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
.btn {
  margin: 0 auto;
  height: 34px;
  line-height: 34px;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 13px;
  text-align: center;
  border-radius: 11px;
}
.cantBtn {
  background-color: #eee;
}
</style>