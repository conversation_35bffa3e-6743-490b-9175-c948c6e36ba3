<template>
  <view class="listPage pl5 pr5 pb10">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>

    <view class="listContainer ma10">
      <scroll-view class="h100x"  scroll-y :scroll-top="scrollTop" @scroll="onScroll"
        refresher-background="#f3f3f7">

        <view class="timeline-container">
          <view v-for="(ele, index) in list1" :key="index" class="timeline-item">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="returnOrderNo">{{ ele.eventTime }}</view>
              <view class="stockCode">{{ ele.eventName }}</view>
              <view class="stockLocation">{{ ele.eventUser }}</view>
              <view class="stockLocation">{{ ele.barcode }}</view>
            </view>
          </view>
        </view>
        <NoData v-if="!list1 || list1.length === 0"></NoData>
      </scroll-view>
    </view>

  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import useNls from "@/mixins/useNls";
import _ from "lodash";

export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '事件时间轴',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {},
      status: 'nomore',
      // 示例数据
      list1: [],
    };
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题
    this.list1 = JSON.parse(decodeURIComponent(options.list1))
    if (!this.list1 || (Array.isArray(this.list1) && this.list1.length === 0)) {
      this.$Toast('该箱无装箱履历，请核实!');
    }
    this.nlsMap = nlsMap
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata()
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.timeline-container {
  position: relative;
  padding-left: 20px;
  padding-right: 20px;
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.timeline-dot {
  position: absolute;
  left: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #4caf50;
  /* 绿色圆点 */
  border: 2px solid #fff;
  top: 50%;
  transform: translateY(-50%);
  /* 垂直居中 */
}

.timeline-content {
  padding-left: 40px;
  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.returnOrderNo {
  font-size: 16px;
  color: #333;
}

.stockCode,
.stockLocation {
  font-size: 14px;
  color: #999;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 9px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #4caf50;
}
</style>
