export default {
  data() {
    return {
    }
  },
  methods: {
    async myprintPackage(data) {
      try {
        if (data.length > 0) {
          uni.showLoading({
            title: '打印中...',
            mask: true
          });
          let promises = []
          for (let i = 0; i < data.length; i++) {
            let { ip, port, printMachineName, temeptName, printData } = data[i];
            let parasmArray = []
            for (const key in printData) {
              const element = printData[key];
              parasmArray.push({
                type: "",
                name: key,
                value: element,
                required: false
              })
            }
            let option = {
              ip: ip,
              port: port,
              ReportName: temeptName,
              printName: printMachineName,
              priParameterntKey: parasmArray,
            }
            // 每次请求之间等待 i 秒 避免打印速度过快
            await new Promise(resolve => setTimeout(resolve, 1000 * i));
            // 发送请求
            const result = await this.printPackage(option);
            promises.push(result)
          }
          let c = await Promise.all(promises);
          console.log('c', c);
          // 全部打印执行完成
          uni.hideLoading()
          this.$Toast('打印成功')
        } else {
          this.$Toast('无法查询打印信息!')
        }
      } catch (error) {
        uni.hideLoading()
        this.$Toast('无法查询打印信息!')
        console.log('error', error);
      }
    },
    printPackage(options = {}) {
      // let p = new Promise((res, rej) => {
      //   setTimeout(() => {
      //     let a = Math.floor(Math.random() * 10 + 1)
      //     if (a > 5) {
      //       res('res')
      //     } else {
      //       rej('rej')
      //     }
      //   }, 1000);
      // })
      // return p
      let promise = new Promise((resolved, rejected) => {
        let obj = {
          ReportType: "gridreport",     /*报表类型 gridreport fastreport reportmachine 为空 将默认为gridreport  */
          // ReportName: "fenQie.grf",     /*报表文件名 条形码 */
          PrinterNameUrlEncode: '1',
          ReportName: options.ReportName,     /*报表文件名 条形码 */
          PrinterName: encodeURIComponent(options.printName),      /*可选。指定打印机，为空的话 使用默认打印机, 请在 控制面板 -> 设备和打印机 中查看您的打印机的名称 */
          // Copies: options.printNumber, //份数
          // Parameter: JSON.stringify(options.priParameterntKey),
          method:'printreport',
          ReportType:'gridreport',
          ReportVersion:'1',
          ReportUrl:'',
          Parameter: options.priParameterntKey,
        }
        if (!options.ip || !options.port) {
          this.$Toast('请检查打印机ip和端口')
          return
        }
        let printerUrl = `http://${options.ip}:${options.port}`
        uni.request({
          url: printerUrl,
          data: obj,
          // header: {
          //   'content-type': 'application/x-www-form-urlencoded', //自定义请求头信息
          // },
          method: 'POST',
          success: (res) => {
            if (res.statusCode == 200) {
              resolved(res)
            } else {
              rejected(res)
            }
          },
          fail: (err) => {
            rejected(err);
          }
        })
      })
      return promise
    },

  },
}
