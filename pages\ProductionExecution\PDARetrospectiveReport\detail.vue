<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="明细" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <u-tabs ref="uTabs" :list="tabList" :current="current" lineWidth="100" lineColor="#01bfbf" :activeStyle="{ color: '#333', fontWeight: 'bold' }" :inactiveStyle="{ color: '#ccc', transform: 'scale(1)' }" :itemStyle="{ height: '80rpx', width: `200rpx` }" @change="tabsChange"> </u-tabs>

    <view class="mt5 myContainer">
      <consumeMaterialLot v-if="current === 0" :paramsOption="paramsOption"></consumeMaterialLot>
      <RetrospectiveLot v-if="current === 1" :paramsOption="paramsOption"></RetrospectiveLot>
      <ProductParamInfoLot v-if="current === 2" :paramsOption="paramsOption"></ProductParamInfoLot>
      <MachineParamInfoLot v-if="current === 3" :paramsOption="paramsOption"></MachineParamInfoLot>
      <ReasonCodeLot v-if="current === 4" :paramsOption="paramsOption"></ReasonCodeLot>
      <FirstInspectionInfoLot v-if="current === 5" :paramsOption="paramsOption"></FirstInspectionInfoLot>
    </view>
  </view>
</template>
<script>
import consumeMaterialLot from './component/consumeMaterialLot.vue'
import RetrospectiveLot from './component/RetrospectiveLot.vue'
import ProductParamInfoLot from './component/ProductParamInfoLot.vue'
import MachineParamInfoLot from './component/MachineParamInfoLot.vue'
import ReasonCodeLot from './component/ReasonCodeLot.vue'
import FirstInspectionInfoLot from './component/FirstInspectionInfoLot.vue'

// 物料信息    consumeMaterialLot
// 过站记录   RetrospectiveLot
// 产品参数  ProductParamInfoLot
// 设备参数   MachineParamInfoLot
// 缺陷信息   ReasonCodeLot
// 首检  FirstInspectionInfoLot
export default {
  name: 'rollerConfirmDetail',
  mixins: [],
  components: {
    consumeMaterialLot,
    RetrospectiveLot,
    ProductParamInfoLot,
    MachineParamInfoLot,
    ReasonCodeLot,
    FirstInspectionInfoLot,
  },
  data() {
    return {
      paramsOption: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {},
      tabList: [
        {
          name: '物料信息',
        },
        {
          name: '过站记录',
        },

        {
          name: '产品参数',
        },
        {
          name: '设备参数',
        },
        {
          name: '缺陷信息',
        },
        {
          name: '首检信息',
        },
      ],
      current: 0, // tab
    };
  },

  onLoad(options) {
    // let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    // this.pageTitle = nlsMap.detailTitle // 标题
    // this.nlsMap = nlsMap
    // console.log('====================================');
    // console.log('====================================');
    this.paramsOption = options && JSON.parse(options.params)
    // console.log('options',this.paramsOption);
  },
  methods: {
    tabsChange(e) {
      this.current = e.index
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';


/* 页面结构 */
.myContainerPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  .myContainer {
    flex: 1;
  }
}
</style>