<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="上料信息查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>材料:</view>
        <view class="ml6">{{ simpleTrackProduct.consumableSpecName ? simpleTrackProduct.consumableSpecName + `(${simpleTrackProduct.description})` : '' }}</view>
      </view>
    </view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>标签条码</view>
              <view>{{ ele.consumableName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>当前数量(m)</view>
              <view>{{ ele.quantity }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>接收时间</view>
              <view>{{ ele.accpetTime }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>上料时间</view>
              <view class="flex hcenter">
                <view>{{ ele.createTime }}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      processOperationName: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.processOperationName = e & e.processOperationName
    this.getData(e.machineName, e.processOperationName)
  },
  methods: {
    getData(machineName, processOperationName) {
      let params = {
        machineName,
        processOperationName
      }
      this.$service.Size.GetFeedingmaterialList(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.model = res.datas
            if (res.datas.length > 0) {
              this.simpleTrackProduct.consumableSpecName = res.datas[0].consumableSpecName
              this.simpleTrackProduct.description = res.datas[0].description
            }
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>