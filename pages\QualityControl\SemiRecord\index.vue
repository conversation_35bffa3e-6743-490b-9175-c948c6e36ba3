<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="在制品不良记录" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="在制品条码" borderBottom required labelWidth="100">
          <u--input v-model="model.zzptm" border="none" :focus="zzptmFocus" placeholder="请扫描在制品条码" @focus="focusEvent('zzptm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('zzptm')"></view>
        </u-form-item>

        <u-form-item label="设备" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.sb }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.scgd }} </view>
        </u-form-item>

        <u-form-item label="品种名" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.pzm }} </view>
        </u-form-item>

        <template v-if="radiolist1.length > 0">
          <u-form-item>
            <view>
              <view class="h30 lin30 fb pl10">此工序已使用物料</view>
              <u-checkbox-group style="padding-left: 30px" v-model="checkboxValue1" placement="column">
                <u-checkbox :customStyle="{ marginBottom: '8px' }" v-for="(item, index) in radiolist1" :key="index" :label="item.name" :name="item.value"> </u-checkbox>
              </u-checkbox-group>
            </view>
          </u-form-item>
        </template>
        <u-form-item label="不良现象" borderBottom labelWidth="130">
          <view class="w100x flex right" @click="selectReasonCodeType('blxx')">
            <view>{{ model.blxx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="不良数量" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="SemiRecordNum" border="none" readonly></u--input>
            </view>
          </view>
        </u-form-item>

        <!-- <u-form-item label="待提交不良明细" borderBottom labelWidth="200">
          <view class="w100x flex right"> {{ model.lotQuantity }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item> -->
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
        <view class="mr5">待提交不良明细</view>
        <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
      </view>
      <view class="pl10">{{ tipContent }}</view>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        zzptm: '',
        sb: '',
        sbType: '',
        gx: '',
        gxType: '',
        scgd: '',
        pzm: '',
        blxx: '',
        blxxType: '',
        blxxText: '',
        reasonCodeType: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      selectType: '',
      searchData: [],
      oldGxType: '',
      tipContent: '',
      radiolist1: [
      ],
      checkboxValue1: [],
      hasCheck: false,
      zzptmFocus: true
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.SemiRecordList;
      },
      set(value) {
        this.$store.commit('changeSemiRecordList', value);
      }
    },
    SemiRecordNum: {
      get() {
        // let arr = []
        // this.list.forEach((item) => {
        //   if(arr.indexOf(item.zzptm) == -1) {
        //     arr.push(item.zzptm)
        //   }
        // })
        return this.list.length;
      },
      set(value) {
        // this.$store.commit('setCount', value);
      }
    }
  },
  watch: {
    'model.zzptm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.blxxType': {
      handler(val) {
        // 切换不良现象，录入明细中
        // this.changeDurableName(val)
        if (this.SemiRecordNum == 0) {
          // 
        }
      }
    },
    // 'model.lotName': {
    //   handler(res) {
    //     this.changeLotName(res)
    //   }
    // }
  },
  onLoad() {
    this.initModel()
    this.list = []
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        processOperationName: this.model.gxType
      }
      this.$service.QualityControl.getReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            label: item.reasonCode + '/' + item.reasonCodeDesc,
            value: item.reasonCode,
            reasonCodeDesc: item.reasonCodeDesc,
            reasonCodeType: item.reasonCodeType
          }
          // return item.reasonCode
        })
        // this.GetReasonCodeTypeList.forEach(item => {
        //   item = item.reasonCodeType + '/' + item.typeDesc
        // })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      // this.columns = this.GetReasonCodeTypeList
      // this.$set(this.columns, 0, this.GetReasonCodeTypeList)
      if (type == 'blxx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      // this.model.productRequestName = e.value[0].productRequestName

      // this.focusObj.saveNo = true
      if (this.selectType == 'blxx') {
        // 选完不良现象, 有在制品条码添加进明细中
        let flag = false
        this.list.forEach(item => {
          if (item.zzptm == this.model.zzptm) {
            // 在制品条码已存在待提交不良明细中，不允许添加
            flag = true
          }
        })
        if (flag) {
          this.$Toast('在制品条码已存在待提交不良明细中！')
          return
        }

        this.model.blxx = e.value[0].label
        this.model.blxxType = e.value[0].value
        this.model.blxxText = e.value[0].reasonCodeDesc
        this.model.reasonCodeType = e.value[0].reasonCodeType

        let arr = []
        console.log('his.checkboxValue1', this.checkboxValue1, this.radiolist1);
        this.checkboxValue1.forEach(item => {
          this.radiolist1.forEach(item2 => {
            if (item == item2.value) {
              arr.push(item2)
            }
          })
        })
        console.log('arr', arr);
        // this.model.bomComponentList = arr
        if (this.model.zzptm) {
          this.searchData.forEach(item => {
            let obj = {
              ...this.model,
              ...item,
              blxx: this.model.blxx ? this.model.blxx : '',
              blxxType: this.model.blxxType ? this.model.blxxType : '',
              blxxText: this.model.blxxText ? this.model.blxxText : '',
              reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : '',
              bomComponentList: arr
            }
            this.oldGxType = this.model.gxType
            this.list.push(obj)
            this.checkboxValue1 = []
            this.radiolist1 = []
          })
          this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
          this.initModel()
          this.zzptmFocus = true
        }
      }
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/QualityControl/SemiRecord/modules/SemiRecordList`
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        zzptm: '',
        sb: '',
        sbType: '',
        gx: '',
        gxType: '',
        scgd: '',
        pzm: '',
        blxx: this.model.blxx ? this.model.blxx : '',
        blxxType: this.model.blxxType ? this.model.blxxType : '',
        blxxText: this.model.blxxText ? this.model.blxxText : '',
        reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
      }
    },
    submit() {
      if (this.SemiRecordNum == 0) {
        this.$Toast('暂无待提交不良明细！')
        return
      }
      let arr = this.list.map(item => {
        return {
          ...item,
          reasonCodeType: item.reasonCodeType,
          reasonCode: item.blxxType,
          reasonCodeDesc: item.blxxText,
          // reasonCodeDesc: item.blxx,
          ngState: 'N'
        }
      })
      let params = {
        functionType: 'Record',
        ngLotRecordList: arr
      }
      this.zzptmFocus = false
      this.$service.QualityControl.NgLotRecordAndJudgeSubmit(params).then(res => {
        this.$Toast('在制品不良记录提交成功！')
        // 提交成功初始化
        this.model = {
          zzptm: '',
          sb: '',
          sbType: '',
          gx: '',
          gxType: '',
          scgd: '',
          pzm: '',
          blxx: '',
          blxxType: '',
          blxxText: '',
          reasonCodeType: ''
        }
        // 提交成功清空明细
        this.list = []
        this.tipContent = ''
        this.checkboxValue1 = []
        this.radiolist1 = []
        this.hasCheck = false
        this.zzptmFocus = true
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 在制品条码 */
    async changeDurableName(value) {
      if (!value) return
      this.hasCheck = false
      this.radiolist1 = []
      this.checkboxValue1 = []
      this.columns = []
      let params = {
        functionType: 'Record',
        lotName: value
      }
      this.zzptmFocus = false
      try {
        let res = await this.$service.QualityControl.getLotListData(params)
        this.searchData = res.datas ? res.datas : []
        if (res.datas.length > 0) {
          let data = res.datas[0]

          // 在制品条码是否存在明细中
          let flag = false
          this.list.forEach(item => {
            this.searchData.forEach(item2 => {
              if (item.lotName == item2.lotName) {
                // 在制品条码有一条已存在待提交不良明细中，不允许添加
                flag = true
              }
            })
          })
          if (flag) {
            this.$Toast('在制品条码已存在待提交不良明细中！')
            this.initModel()
            return
          }

          // // 不存在明细中，判断是否有明细
          // if(this.SemiRecordNum == 0) {
          //   // 明细中没有，则获取不良现象下拉
          //   this.GetReasonCodeType()
          // } else {
          //   // 明细中有数据，则判断工序是否一致，不一致则清空数据提示工序不一致，保留之前的不良现象下拉
          //   this.GetReasonCodeType()
          // }
          if (data.bomComponentList && data.bomComponentList.length > 0) {
            this.hasCheck = true
            this.model.blxx = ''
            this.model.blxxType = ''
            this.model.blxxText = ''
            this.radiolist1 = data.bomComponentList.map(item => {
              if (item.description) {
                return {
                  ...item,
                  name: item.consumableSpecName + '/' + item.description,
                  value: item.consumableSpecName
                }
              }
              return {
                ...item,
                name: item.consumableSpecName,
                value: item.consumableSpecName
              }
            })
          }
          this.model = {
            ...res.datas[0],
            // zzptm: data.lotName,
            zzptm: this.model.zzptm,
            sb: data.machineName + '/' + data.machineDesc,
            sbType: data.machineName,
            gx: data.processOperationName + '/' + data.processOperationDesc,
            gxType: data.processOperationName,
            scgd: data.productOrderName,
            pzm: data.productSpecName + '/' + data.productSpecDesc,
            pzmType: data.productSpecName,
            blxx: this.model.blxx ? this.model.blxx : '',
            blxxType: this.model.blxxType ? this.model.blxxType : '',
            blxxText: this.model.blxxText ? this.model.blxxText : '',
            reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
          }

          if (this.SemiRecordNum == 0) {
            if (this.model.blxxType) {
              // 无明细，有不良现象，判断老工序：同工序加入明细中，否则清空
              if (this.oldGxType == data.processOperationName) {
                // 工序一致，并且有不良现象选择，添加进明细中
                this.searchData.forEach(item => {
                  let obj = {
                    ...this.model,
                    ...item,
                    blxx: this.model.blxx ? this.model.blxx : '',
                    blxxType: this.model.blxxType ? this.model.blxxType : '',
                    blxxText: this.model.blxxText ? this.model.blxxText : '',
                    reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
                  }
                  this.oldGxType = this.model.gxType
                  if (!this.hasCheck) {
                    this.list.push(obj)
                  }
                })
                this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
                this.initModel()
                this.zzptmFocus = true
              }
            } else {
              // 无明细，无不良现象，不良现象下拉
              this.GetReasonCodeType()
            }
          } else {
            let flag2 = false
            this.list.forEach(item => {
              if (item.gxType != data.processOperationName) {
                // 工序不一致，清空数据，保留之前的不良现象
                flag2 = true
              }
            })
            if (flag2) {
              this.$Toast('当前条码工序不一致，请重新扫描！')
              this.tipContent = `在制品条码${this.model.zzptm}提交明细失败`
              this.initModel()
              return
            }
            if (this.model.blxxType) {
              // 工序一致，并且有不良现象选择，添加进明细中
              this.searchData.forEach(item => {
                let obj = {
                  ...this.model,
                  ...item,
                  blxx: this.model.blxx ? this.model.blxx : '',
                  blxxType: this.model.blxxType ? this.model.blxxType : '',
                  blxxText: this.model.blxxText ? this.model.blxxText : '',
                  reasonCodeType: this.model.reasonCodeType ? this.model.reasonCodeType : ''
                }
                this.oldGxType = this.model.gxType
                if (!this.hasCheck) {
                  this.list.push(obj)
                }
              })
              this.tipContent = `在制品条码${this.model.zzptm}加入不良明细成功`
              this.initModel()
              this.zzptmFocus = true
            }
          }

          // 有不良现象，无明细
        } else {
          this.model.zzptm = ''
          this.$Toast('在制品不存在！')
        }
      } catch (error) {
        this.zzptmFocus = true
        console.log(error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
