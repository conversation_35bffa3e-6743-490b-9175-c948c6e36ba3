/**
 *
 * @description: 提供给使用scroll-view的页面使用;必须实现getData, initSearchModel 方法
 *
 * @author: ouyangqing
 *
*/
export default {
	data() {
		return {
			model: [],
			scrollTop: 0, // 竖向滚动条位置
			refresherTriggered: false, // 下拉刷新是否被触发
			status: 'loadmore', // 加载更多组件的状态
			pageNumber: 1,
			pageSize: 10,
			// totalCount: 0,
			searchModel: {}, // 所有请求参数
			old: {
				scrollTop: 0,
			},
		}
	},

	methods: {
		/**
		 * 下拉刷新
		 */
		refresherrefresh() {
			if (!this.refresherTriggered) {
				this.refresherTriggered = true;
				this.pageNumber = 1;
				this.initSearchModel();
				this.model = [];
				this.getData();
			}
		},

		/**
		 * 滚动到底部刷新数据 
		 */
		lower() {
			if (this.status !== 'nomore' && this.status !== 'loading') {
				this.status = 'loading'
				this.pageNumber += 1
				this.getData();
			}
		},

		/**
		 * 滚动时触发
		 */
		onScroll(e) {
			this.old.scrollTop = e.detail.scrollTop;
		},

		/**
		 * 返回顶部
		 */
		goTop() {
			this.scrollTop = this.old.scrollTop;
			this.$nextTick(function () {
				this.scrollTop = 0;
				this.old.scrollTop = 0;
			});
		},
	},


	/* 
	 	原生方式： getData必须采用asyn await 
		          pages.json  配置 "enablePullDownRefresh":true
							<scroll-view  需要添加 @touchmove.stop

		组件方式添加 ：refresher-background="#f3f3f7" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh"

		<view class="listContainer pa10">
			<scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
				<view v-for="(item, index) in model" :key="index">
				</view>
				<NoData v-if="!model || model.length === 0"></NoData>
				<u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
			</scroll-view>
			<view @click="goTop">
				<u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
			</view>
		</view>


		getData(clearOldData = false, refresh = false) {
			clearOldData && (this.pageNumber = 1)
			refresh && (this.model = [])
			this.searchModel.pageNo = this.pageNumber
			this.searchModel.limit = this.pageSize
			// setTimeout(() => {
			//   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
			//   this.model = clearOldData ? res : [...this.model, ...res]
			//   this.refresherTriggered = false
			//   if (this.model.length > 30) {
			//     this.status = 'nomore'
			//   } else {
			//     this.status = 'loadmore'
			//   }
			// }, 1000)
			// return
			let params = JSON.parse(JSON.stringify(this.searchModel))
			this.$service.materialFeeding.queryConsumableItemPageList(params).then((res) => {
				if (res && res.success) {
					this.model = clearOldData ? res.data.records : [...this.model, ...res.data.records]
					if (this.searchModel.limit = res.data.pages) {
						this.status = 'nomore'
					} else {
						this.status = 'loadmore'
					}
					uni.stopPullDownRefresh() 原生方式
					this.refresherTriggered = false  非原生方式
				}
			}).catch((e) => {
				uni.stopPullDownRefresh() 原生方式
				this.refresherTriggered = false 非原生方式
			})
		},
	
	*/

	// onPullDownRefresh() {
	// 	this.getData(true, true)
	// }
}
