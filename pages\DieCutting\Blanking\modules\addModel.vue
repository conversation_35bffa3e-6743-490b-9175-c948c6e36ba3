<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切下料—记录不良" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input readonly v-model="model.machineName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="产品描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="弹夹" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.durableName }} </view>
        </u-form-item>

        <u-form-item label="弹夹容量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.capacity }} </view>
        </u-form-item>

        <u-form-item label="批次ID" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="设备计数(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ computedQty }} </view>
        </u-form-item>

        <u-form-item label="人工确认(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.uesrConfirm }} </view>
        </u-form-item>

        <u-form-item label="报废数量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.ngQuantity }} </view>
        </u-form-item>

        <!-- <u-form-item label="代码类型" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType">
            <view>{{ model.reasonCodeType }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item> -->

        <u-form-item label="不合格代码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCode">
            <view>{{ model.reasonCode }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="数量" borderBottom required labelWidth="130">
          <view style="display: flex" class="w100x flex right">
            <u--input type="number" placeholder="请输入数量" v-model="ngQuantityInput" border="none" @change="(val) => changeQtyInput(val)"></u--input>
            <view>KG</view>
          </view>
        </u-form-item>

        <u-form-item label="数量" borderBottom required labelWidth="130">
          <view class="w100x flex right"> {{ ngQuantity }} ea</view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>

    <view class="btnContainer" @click="submit">保存</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeQtyInput = this.$debounce(this.changeQtyInput, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        reasonCode: '不合格代码不能为空',
        // ngQuantity: '数量不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      GetReasonCodeTypeList: [],
      GetReasonCodeList: [],
      uesrConfirm: null,
      computedQty: null,
      ngQuantity: null,
      ngQuantityInput: null,
      show: false,
      content: '',
      radio: 0
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad(e) {
    this.initModel()
    this.model.machineName = e && e.machineName
    this.computedQty = e && e.computedQty
    this.uesrConfirm = e && e.uesrConfirm
  },
  methods: {
    confirm() {
      this.ngQuantity = this.ngQuantityInput * 1
      this.show = false
    },
    async changeQtyInput(val) {
      if (!val) {
        this.ngQuantity = null
        return
      }
      try {
        let res = await this.$service.DieCutting.listUnitConversion({
          processOperationName: this.model.processOperationName,
          productSpecName: this.model.productSpecName,
          sourceUnit: "Kg",
          targetUnit: "ea"
        })
        if (res.datas.length == 1) {
          this.ngQuantity = parseInt(this.$utils.calculate_mul(this.ngQuantityInput, res.datas[0].ratio))
        } else {
          this.content = `该物料${this.model.productSpecName}, 在该工序${this.model.processOperationName}没有单位转换, 现默认按1:1转换，是否继续执行该操作？`
          this.show = true
          this.ngQuantity = null
        }
      } catch (error) {
        this.ngQuantity = null
      }
      // if(!ele.sourceUnit || !ele.targetUnit) {
      //   // 弹窗提示
      //   this.content = `该物料${ele.consumableSpecName}, 在该工序${ele.processOperationName}没有维护或维护多条单位转换, 现默认按1:1转换，是否继续执行该操作？`
      //   this.show = true
      // } else {
      //   this.simpleTrackProduct[this.index1].data[this.index2].qty = this.simpleTrackProduct[this.index1].data[this.index2].qtyInput * Number(this.simpleTrackProduct[this.index1].data[this.index2].ratio)
      //   this.simpleTrackProduct[this.index1].data[this.index2].canSub = true
      // }
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备
        machineSpecDesc: null, // 设备描述
        productSpecName: null, // 产品编码
        productSpecDesc: null, // 产品描述
        durableName: null, // 弹夹
        capacity: null, // 弹夹容量
        lotName: null, // 批次号
        uesrConfirm: null, // 人工确认
        ngQuantity: null, // 报废数量
        productQuantity: null, // 良品数量
        computedQty: null // 计算数量
      }
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.saveNo = false
      this.select = true
    },
    selectFirm(e) {
      if (e.value[0].reasonCodeType) {
        this.model.reasonCodeType = e.value[0].reasonCodeType
        this.GetReasonCode(this.model.reasonCodeType)
      } else {
        this.model.reasonCode = e.value[0].reasonCode
      }
      this.select = false
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (!this.ngQuantity) {
        this.$Toast('数量不能为空')
        return
      }
      let params = {
        ...this.model
      }
      params.ngQuantity = this.ngQuantity
      this.$service.DieCutting.DieCuttingSaveNgLotRecord(params).then(res => {
        this.$Toast('保存成功')
        uni.$emit('updateData', { machineName: this.model.machineName, uesrConfirm: this.uesrConfirm })
        setTimeout(() => {
          uni.navigateBack({
            delta: 1
          });
        }, 600);
      })
    },

    /* 搅拌机 */
    changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      this.$service.DieCutting.DieCuttingByMachineName(params).then(res => {
        this.model = {
          ...res.datas[0],
          ngQuantity: res.datas[0].ngQuantity || 0
        }
        if (!this.model.uesrConfirm) this.model.uesrConfirm = this.uesrConfirm
        this.GetReasonCode(this.model.processOperationName)
      }).catch(() => {
        this.model.machineName = null
      })
    },
    GetReasonCodeType() {
      const params = {
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas
        this.GetReasonCodeTypeList.forEach(item => {
          item.labelText = item.reasonCodeType + '/' + item.typeDesc
        })
      })
    },
    GetReasonCode(processOperationName) {
      const params = {
        processOperationName
      }
      this.$service.DieCutting.GetReasonCode(params).then(res => {
        this.GetReasonCodeList = res.datas
        this.GetReasonCodeList.forEach(item => {
          item.labelText = item.reasonCode + '/' + item.description
        })
      })
    },
    selectReasonCodeType() {
      this.columns = this.GetReasonCodeTypeList
      this.select = true
    },
    selectReasonCode() {
      this.columns = this.GetReasonCodeList
      this.select = true
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'GF20-01-FQ01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/uform.scss';
@import '../../../../styles/publicStyle.scss';
</style>
