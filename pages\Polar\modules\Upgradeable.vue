<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="可上极卷查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>设备号:</view>
        <view class="ml6">{{ machineName }}</view>
      </view>
      <view class="fs16 c_00b17b mt10">可上极卷信息</view>
    </view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>极卷条码</view>
              <view>{{ ele.poleRollLotName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>极卷工序</view>
              <view>{{ ele.processOperationName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>完工时间</view>
              <view>{{ ele.poleRollLoadingTime ? moment(ele.poleRollLoadingTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>极卷数量(m)</view>
              <view class="flex hcenter">
                <view>{{ ele.poleRollQuantity }}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      machineName: '',
      productOrderName: '',
      type: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.machineName = e && e.machineName
    this.productOrderName = e && e.productOrderName
    this.type = e && e.type
    this.getData()
  },
  methods: {
    moment,
    getData() {
      if (this.type == 'PolarRollUp') {
        let params = {
          machineName: this.machineName,
        }
        this.$service.Polar.getPoleRollAvailableData(params).then((res) => {
          if (res && res.success) {
            if (res.datas.length > 0) {
              this.model = res.datas
              if (res.datas.length > 0) {
                this.simpleTrackProduct.consumableSpecName = res.datas[0].consumableSpecName
                this.simpleTrackProduct.description = res.datas[0].description
              }
            }
          }
        })
      } else {
        let params = {
          machineName: this.machineName,
          productOrderName: this.productOrderName,
        }
        this.$service.Polar.getPoleRollAvailableDataByMachineNameAndWorkOrderName(params).then((res) => {
          if (res && res.success) {
            if (res.datas.length > 0) {
              this.model = res.datas
              if (res.datas.length > 0) {
                this.simpleTrackProduct.consumableSpecName = res.datas[0].consumableSpecName
                this.simpleTrackProduct.description = res.datas[0].description
              }
            }
          }
        })
      }

    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
</style>