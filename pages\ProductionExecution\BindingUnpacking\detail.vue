<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="topContainer"> </view>
    <view class="listContainer ma5">
      <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
        <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
        <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码</view>
        <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w100">槽位号</view>
      </view>
      <view class="table_content">
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
          <view v-for="(item, index) in model" :key="index" class="flex bl_e1e1e1 h40">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.lotName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w100"> {{ item.slot }}</view>
          </view>

          <view class="pt100" v-if="!model || model.length === 0">
            <u-empty mode="data"></u-empty>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      model: [],
      // model: Array.from({ length: 50 }, (v, i) => i),
      paramsOption: {},
      params_durableName: ''
    };
  },

  onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题
    this.nlsMap = nlsMap
    this.params_durableName = options.durableName
    // this.initSearchModel()
    this.getList()
    // this.getData()
  },
  methods: {
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },

    getList() {
      let parmas = {
        container: this.params_durableName
      }
      this.$service.BindingUnpacking.BindOrUnBindGetLotList(parmas).then(res => {
        this.model = res.datas
      })
    },

    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      // this.$service.common.queryPagePrintLotInfo(params).then((res) => {
      //   if (res && res.success) {
      //     this.model = clearOldData ? res.data[0].content : [...this.model, ...res.data[0].content]
      //     if (res.data[0].last) {
      //       this.status = 'nomore'
      //     } else {
      //       this.status = 'loadmore'
      //     }
      //     this.refresherTriggered = false
      //   }
      // }).catch((e) => {
      //   this.refresherTriggered = false
      // })
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_header {
      flex-shrink: 0;
    }
    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>