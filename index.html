<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        document.documentElement.style.fontSize =
          document.documentElement.clientWidth / 20 + "px";
      });
      var coverSupport =
        "CSS" in window &&
        typeof CSS.supports === "function" &&
        (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"));
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ", viewport-fit=cover" : "") +
          '" />'
      );
    </script>
    <link
      rel="stylesheet"
      href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css"
    />
    <link rel="icon" type="image/png" sizes="32x32" href="/static/fav.png" />
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <noscript>
      <strong>Please enable JavaScript to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- <script type="module" src="/main.js"></script> -->
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
    <script>
      let vConsole = new VConsole();
    </script> -->
  </body>
</html>
