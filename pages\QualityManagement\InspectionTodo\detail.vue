<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma10">
      <view class="ml5 flex between flex_shrink0" style="color: #409eff">
        <view> 检验类型: {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }}</view>
      </view>
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <!--  公共 -->
        <template v-if="InspectionTypes !== 'ProdcutWarehouseInspection'">
          <u-form-item label="设备号" borderBottom required labelWidth="100">
            <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备号"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="设备号描述" borderBottom labelWidth="100">
            <u--input readonly v-model="model.machineDesc" border="none"></u--input>
          </u-form-item>
          <!-- <u-form-item label="工序" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
            </view>
          </u-form-item> -->

          <u-form-item label="工序" required borderBottom labelWidth="100">
            <view class="w100x flex right" @click="checkSelect('processOperationName')">
              <view v-if="model.processOperationName">{{ $utils.filterObjLabel(dicts.processOperationNameList, model.processOperationName) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" :style="{ transform: select && selectType === 'processOperationName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>
        </template>

        <!-- FQC检验 -->
        <template v-if="InspectionTypes == 'ProdcutWarehouseInspection'">
          <u-form-item label="FQC检验类型" required borderBottom labelWidth="120">
            <view class="w100x flex right" @click="checkSelect('fqcInspectType')">
              <view v-if="model.fqcInspectType">{{ $utils.filterObjLabel(dicts.fqcInspectTypeList, model.fqcInspectType) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" :style="{ transform: select && selectType === 'fqcInspectType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="条码号" borderBottom required labelWidth="100">
            <template v-if="model.fqcInspectType">
              <u--input v-model="model.warehousingNo" border="none" focus placeholder="请扫描"></u--input>
              <view class="iconfont icon-saoma" @click="scan('warehousingNo')"></view>
            </template>
          </u-form-item>
        </template>

        <u-form-item label="工单" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('productOrderName')">
            <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList, model.productOrderName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <!-- 只有首检才有 -->
        <!-- <template v-if="InspectionTypes == 'FirstInspection'">
          <u-form-item label="场景说明" borderBottom required labelWidth="100">
            <view class="w100x flex right" @click="checkSelect('scenceDesc')">
              <view v-if="model.scenceDesc">{{ $utils.filterObjLabel(dicts.scenceDescList, model.scenceDesc) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" :style="{ transform: select && selectType === 'scenceDesc' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>
        </template> -->

        <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      </u--form>

      <view class="mt10 flex between flex_shrink0" style="color: #409eff">
        <view>任务列表</view>
      </view>
      <view class="mt10">
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="flex between mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex1 mr10">
              <view class="flex h30 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">任务编码:</view>
                <view>{{ ele.taskNo }}</view>
              </view>
              <view class="flex h30 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">任务创建方式:</view>
                <view
                  >{{ ele.createTypeDictText }}
                  <span v-if="ele.createTypeDictText == '手动创建'">{{ '(' + ele.createUser + ')' }}</span>
                </view>
              </view>
              <view class="flex h30 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">任务状态:</view>
                <!-- <view>{{ ele.inspectStateDictText }}</view> -->
                <view>
                  {{ $utils.filterObjLabel(dicts.inspectStateList, ele.inspectState) }}
                </view>
              </view>
              <view class="flex h30 hcenter c_999">
                <view class="mr10 w100 txt_r c_000">创建时间:</view>
                <view>{{ ele.createTime }}</view>
              </view>
            </view>
            <view class="flex flex_column wcenter">
              <view class="btn pl10 pr10 mb10" :class="canJunmpAction(1, ele) ? 'cantBtn' : ''" @click="delAction(ele)">取消任务</view>
              <view class="btn pl10 pr10 mb10" :class="canJunmpAction2(1, ele) ? 'cantBtn' : ''" @click="junmAction(1, ele)">开始取样</view>
              <view class="btn pl10 pr10" :class="canJunmpAction2(2, ele) ? 'cantBtn' : ''" @click="junmAction(2, ele)">参数录入</view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view>
      </view>
    </view>

    <my-dialog ref="myDialog">
      <template v-slot:content>
        <view class="flex flex_column center pa20">
          <view class="flex center fs18 c_333 fb">提示</view>
          <view class="flex center w100x pt10">
            <u--textarea v-model="cancelReason" placeholder="请输入取消原因..."></u--textarea>
          </view>
        </view>
      </template>
    </my-dialog>
    <!-- <view class="btnContainer" @click="submit">创建任务</view> -->
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeWarehousingNo = this.$debounce(this.changeWarehousingNo, 1000)
    return {
      cancelReason: '',
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      InspectionTypes: '',
      rulesTip: {
        // machineName: '设备编号不能为空',
        // productOrderName: '工单号不能为空',
        // scenceDesc: '场景说明不能为空'
      },
      columns: [],
      select: false,
      selectType: '',
      model: {},
      list: [],
      // list: Array.from({ length: 10 }, (ele, i) => i),
      dicts: {
        InspectionTypesList: [], // 检验类型
        processOperationNameList: [], // 工序列表
        productOrderNameList: [], // 工单列表
        scenceDescList: [], // 场景说明
        fqcInspectTypeList: [], // FQC检验类型
        inspectStateList: [], // 任务状态
      },
    }
  },
  computed: {},
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.warehousingNo': {
      handler(val) {
        this.changeWarehousingNo(val)
      }
    },
    'model.processOperationName': {
      handler(val) {
        // this.getInspectionTaskList(val)
      }
    },
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.InspectionTodoTitle // 标题
    this.nlsMap = nlsMap
    this.InspectionTypes = options.InspectionTypes
    this.getEnumValue('InspectionTypes', 'InspectionTypesList') // 单据类型
    this.getEnumValue('SceneDesc', 'scenceDescList') // 单据类型
    this.getEnumValue('FqcInspectType', 'fqcInspectTypeList') // FQC检验类型
    this.getEnumValue('InspectState', 'inspectStateList') // FQC检验类型
    this.initModel()
  },
  methods: {
    delAction(item) {
      if (item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return
      }
      this.$refs.myDialog.showDialog({
        cancelText: '取消',
        confirmText: '确定',
        onCancel: () => { },
        onConfirm: async (myDialogVm) => {
          if (!this.cancelReason) {
            this.$Toast('请输入取消原因')
            return
          }
          try {
            let params = {
              cancelReason: this.cancelReason,
              taskNo: item.taskNo,
            }
            let res = await this.$service.QualityManagement.cancelTask(params)
            if (res.success) {
              myDialogVm.close()
              this.$Toast('操作成功')
              setTimeout(() => {
                this.getInspectionTaskList()
              }, 500);
            }
          } catch (error) {
            this.$Toast('操作失败')
          }
        }
      });
    },
    junmAction(type, item) {
      if (type == 1) {
        if (item.inspectStateDictText == '待取样') {
          let params = {
            taskNo: item.taskNo,
            createTypeDictText: item.createTypeDictText,
            inspectStateDictText: item.inspectStateDictText,
            machineName: item.machineName,
            processOperationName: item.processOperationName,
            lotName: item.lotName, //  样品条码
            workOrderName: item.workOrderName, // 工单号
            productSpecName: item.productSpecName, //  产品编码
            // fqc
            fqcInspectType: item.fqcInspectType, //  
            warehousingNo: this.model.warehousingNo,
          }
          uni.navigateTo({
            url: `/pages/QualityManagement/InspectionSampling/detail?InspectionTypes=${this.InspectionTypes}&params=${JSON.stringify(params)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
          })
        }
      } else if (type == 2) {
        if (item.inspectStateDictText == '检验中') {
          // 只有检验，待判定可以参数录入
          let params = {
            taskNo: item.taskNo,
            createTypeDictText: item.createTypeDictText,
            inspectStateDictText: item.inspectStateDictText,
            machineName: item.machineName,
            processOperationName: item.processOperationName,
            // lotName: item.lotName, //  样品条码
            // productOrderName: item.workOrderName, // 工单号
            // productSpecName: item.productSpecName, //  产品编码
            // productSpecDesc: item.productSpecDesc, //  产品编码
          }
          uni.navigateTo({
            url: `/pages/QualityManagement/InspectionExecution/detail?InspectionTypes=${this.InspectionTypes}&params=${JSON.stringify(params)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,

          })
        }
      } else if (type == 3) {
        if (item.inspectStateDictText == '待判定') {
          // 只有待判定中可以整体判定
          // uni.navigateTo({
          //   url: `/pages/QualityControl/FirstInspectionDecision/index?processOperationName=${this.form.gx}&machineName=${this.form.sbh}&taskNo=${item.taskNo}&createTypeDictText=${item.createTypeDictText}&inspectStateDictText=${item.inspectStateDictText}`,
          // })
        }
      }
    },
    canJunmpAction2(type, item) {
      if (type == 1) {
        if (item.inspectStateDictText == '待取样') {
          return false
        } else {
          return true
        }
      } else if (type == 2) {
        if (item.inspectStateDictText == '检验中' || item.inspectStateDictText == '待判定') {
          // 只有检验，待判定可以参数录入
          return false
        } else {
          return true
        }
      } else if (type == 3) {
        // if (item.inspectStateDictText == '待判定') {
        //   // 只有待判定中可以整体判定
        //   return false
        // } else {
        //   return true
        // }
      }
    },
    canJunmpAction(type, item) {
      if (item.inspectStateDictText != '待取样') {
        // 只有待取样可以删除
        return true
      } else {
        return false
      }
    },
    async changeWarehousingNo(value) {
      this.model.productOrderName = ''
      this.dicts.productOrderNameList = []
      this.list = []
      if (!value) return
      try {
        let params = {
          inspectType: this.InspectionTypes,
          fqcInspectType: this.model.fqcInspectType,
          warehousingNo: value,
        }
        let res = await this.$service.QualityManagement.getMachineForInspection(params)
        this.dicts.productOrderNameList = res.datas[0].productOrderNameList.map((item) => ({
          label: item,
          value: item,
        }))
      } catch (error) {
        this.model.warehousingNo = ''
      }

    },
    /* 设备号 */
    async changeMachineName(value) {
      this.model.machineDesc = ''
      this.model.processOperationName = ''
      this.model.productOrderName = ''
      this.dicts.productOrderNameList = []
      this.dicts.processOperationNameList = []
      if (!value) return
      this.list = []
      let params = {
        inspectType: this.InspectionTypes,
        machineName: value,
      }
      try {
        let res = await this.$service.QualityManagement.getMachineForInspection(params)
        this.model.machineDesc = res.datas[0].machineDesc
        // this.model.processOperationName = res.datas[0].processOperationName
        this.dicts.productOrderNameList = res.datas[0].productOrderNameList.map((item) => ({
          label: item,
          value: item,
        }))
        this.dicts.processOperationNameList = res.datas[0].processOperationList.map((item) => ({
          label: item.processOperationText,
          value: item.processOperationName,
        }))
      } catch (error) {
        this.model.machineName = ''
      }
    },
    /* 任务列表 */
    async getInspectionTaskList() {
      let params = {
        inspectType: this.InspectionTypes,
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
        workOrderName: this.model.productOrderName,
        // fqc
        warehousingNo: this.model.warehousingNo,
        fqcInspectType: this.model.fqcInspectType,
      }
      try {
        let res = await this.$service.QualityManagement.getInspectionTaskList(params)
        if (res.datas.length > 0) {
          this.list = res.datas
        } else {
          this.list = []
        }
      } catch (error) {
        console.log('error', error);
      }
    },


    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        case 'scenceDesc':
          this.columns = this.dicts.scenceDescList
          break;
        case 'fqcInspectType':
          this.columns = this.dicts.fqcInspectTypeList
          break;
        case 'processOperationName':
          this.columns = this.dicts.processOperationNameList
          break;

        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'fqcInspectType') {
        this.list = []
      }
      if (this.selectType == 'processOperationName') {
        if (this.model.processOperationName && this.model.productOrderName) {
          //   需要条件工序工单都有值
          this.getInspectionTaskList()
        }
      }
      if (this.selectType == 'productOrderName') {
        if (this.InspectionTypes == 'ProdcutWarehouseInspection') {
          //  FQC 没有工序
          this.getInspectionTaskList()
        } else {
          if (this.model.processOperationName && this.model.productOrderName) {
            //   需要条件工序工单都有值
            this.getInspectionTaskList()
          }
        }
      }
    },
    initModel() {
      this.model = {
        InspectionTypes: '', //检验类型
        machineName: '', // 设备编码
        warehousingNo: '',
        machineDesc: '', // 设备编码
        scenceDesc: '', // 场景说明
        processOperationName: '', // 工序
        productOrderName: '',
      }
    },

    // 设置
    submit() {
      // for (let key in this.rulesTip) {
      //   if (_.isEmpty(this.model[key])) {
      //     this.$Toast(this.rulesTip[key])
      //     return
      //   }
      // }
      if (this.InspectionTypes == 'ProdcutWarehouseInspection') {
        if (!this.model.fqcInspectType) {
          return this.$Toast('FQC检验类型不能为空')
        }
        if (!this.model.warehousingNo) {
          return this.$Toast('条码号码不能为空')
        }
        if (!this.model.productOrderName) {
          return this.$Toast('工单号不能为空')
        }
      } else {
        if (!this.model.machineName) {
          return this.$Toast('设备编号不能为空')
        }
        if (!this.model.productOrderName) {
          return this.$Toast('工单号不能为空')
        }
        //  首检 检验 场景说明
        if (this.InspectionTypes == 'FirstInspection') {
          if (_.isEmpty(this.model['scenceDesc'])) {
            this.$Toast('场景说明不能为空')
            return
          }
        }
      }
      let params = {
        inspectType: this.InspectionTypes,
        fqcInspectType: this.model.fqcInspectType,
        scenceDesc: this.model.scenceDesc,
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
        workOrderName: this.model.productOrderName,
        warehousingNo: this.model.warehousingNo,
      }
      this.$service.QualityManagement.startInspectionTask(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.getInspectionTaskList()
        }, 500)
      })
    },


    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'NEX-LK1-1F-013'
          break;
        case 'warehousingNo':
          this.model.warehousingNo = 'SCRKM0000012'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.btn {
  height: 34px;
  line-height: 34px;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 13px;
  text-align: center;
  border-radius: 6px;
}
.cantBtn {
  background-color: #eee;
}
</style>

