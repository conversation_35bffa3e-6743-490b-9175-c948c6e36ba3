<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="冲壳上料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="冲壳机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入冲壳机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="冲壳机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="铝塑膜批次号" borderBottom required labelWidth="130">
          <u--input v-model="model.consumableName" border="none" focus placeholder="请扫描或输入铝塑膜批次号" @focus="focusEvent('consumableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.materialLocationName) }}
          </view>
        </u-form-item>

        <u-form-item required label="上料数量(m)" labelWidth="140">
          <view class="w100x flex right"> {{ model.quantity }} </view>
        </u-form-item>
      </u--form>
    </view>

    <view class="btnContainer" @click="submit">上料</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeConsumableName = this.$debounce(this.changeConsumableName, 1000)
    return {
      rulesTip: {
        machineName: '烘烤机编号不能为空',
        consumableName: '铝塑膜批次号不能为空',
      },
      model: {},
    }
  },
  computed: {
    // selectProduct() {
    //   let obj = {}
    //   if (this.model.productRequestName) {
    //     obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
    //   }
    //   return obj
    // }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.consumableName': {
      handler(res) {
        this.changeConsumableName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 冲壳机
        description: null, // 冲壳机描述
        consumableName: null, // 铝塑膜批次号
        consumableSpecName: null, // 物料编号
        materialLocationName: null, // 物料描述
        quantity: null
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        consumableName: this.model.consumableName,
        machineName: this.model.machineName
      }
      this.$service.punchingShell.monut(params).then(res => {
        if (res.success) {
          this.$Toast('上料成功!')
          this.initModel()
        }
      })
    },

    /* 冲壳机 */
    async changeMachineName(value) {
      if (!value) return
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.punchingShell.validateMount(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的冲壳机编码!')
          }
          this.model.description = res.datas[0].description
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    // 批次号
    async changeConsumableName(value) {
      if (!value) return
      let params = {
        consumableName: value
      }
      try {
        let res = await this.$service.punchingShell.findAllWithEquals(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.consumableName = ''
            return this.$Toast('请扫描正确的批次号编码!')
          }
          this.model.quantity = res.datas[0].quantity
          this.model.consumableSpecName = res.datas[0].consumableSpecName
          this.model.materialLocationName = res.datas[0].materialLocationName
        }
      } catch (error) {
        this.model.consumableName = ''
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.CKJB.01.01'
          break;
        case 'consumableName':
          this.model.consumableName = '1000003'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
