<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="物料箱标签" borderBottom required labelWidth="100">
          <u--input v-model="model.boxNo" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('boxNo')"></view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecText) }}
          </view>
        </u-form-item>

        <u-form-item label="物料数量" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.quantity }} {{ model.consumableUnit }} </view>
        </u-form-item>

        <u-form-item label="工单编码" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.productOrderName }}</view>
        </u-form-item>

        <u-form-item label="批次号" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.dateCode }}</view>
        </u-form-item>

        <u-form-item label="最小包标签" labelWidth="100">
          <u--input v-model="model.consumableName" border="none" placeholder="请扫描或输入"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>
      </u--form>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">最小包标签</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">是否打印</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 txt_c">
              <view class="flex w100x hcenter pl10">
                <u--input class="" v-model="ele.receiveQuantity" border="none" type="number" placeholder="请输入"></u--input>
              </view>
            </view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70">
              <u-checkbox-group v-model="ele.isPrint" @change="(value) => checkboxChange(value, ele)">
                <u-checkbox name="1"> </u-checkbox>
              </u-checkbox-group>
            </view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>

        <!-- refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower" -->
        <!-- <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view class="">最小包标签</view>
              <view>{{ ele.consumableName }}</view>
            </view>

            <view class="flex between h40 hcenter c_999">
              <view class="mr5">数量</view>
              <view class="w50x">
                <u--input class="" inputAlign="right" v-model="ele.receiveQuantity" border="bottom" type="number" placeholder="请输入"></u--input>
              </view>
            </view>
            <view>
              <u-button type="error" text="删除" @click="deleteItem(ele, index)"></u-button>
            </view>
          </view>
        </scroll-view>

        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view> -->
      </view>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changeconsumableName = this.$debounce(this.changeconsumableName, 1000)
    this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },

      rulesTip: {
        boxNo: '物料箱标签不能为空',
      },
      model: {},
      list: [], // 
      // list: Array.from({ length: 10 }, (v, i) => i),
    }
  },
  computed: {},
  watch: {
    'model.boxNo': {
      handler(val) {
        this.changeboxNo(val)
      }
    },
    'model.consumableName': {
      handler(val) {
        this.changeconsumableName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
  },
  methods: {
    checkboxChange(value, record) {
      if (value.length > 0) {
        record.isPrint = ['1']
      } else {
        record.isPrint = []
      }
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    initModel() {
      this.model = {
        boxNo: '',  //  	 物料箱标签
        consumableSpecName: '', //   物料编码
        consumableSpecText: '',//  	 物料描述
        quantity: '',//  	 物料数量
        consumableUnit: '',//  	 工单编码
        productOrderName: '',//  	 物料描述
        dateCode: '',//  	 批次号
        consumableName: '',//  	最小包装条码
      }
    },

    // 设置
    async submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let flag = this.list.every(item => item.receiveQuantity && item.receiveQuantity > 0)
      if (!flag) {
        this.$Toast('请输入数量且数量大于0')
        return
      }
      let sum = this.list.reduce((total, curr) => {
        return total + Number(curr.receiveQuantity)
      }, 0)
      if (sum > this.model.quantity) {
        this.$Toast(` 物料拆包后最小包装条码数量之和[${sum}], 大于该物料箱标签[${this.model.boxNo}]库存数量[${this.model.quantity}], 不能进行拆包`)
        return
      }
      try {
        let params = {
          boxNo: this.model.boxNo,
          consumableList: this.list.map((ele) => ({
            ...ele,
            isPrint: ele.isPrint.length > 0 ? '1' : '0'
          })),
        }
        await this.$service.TrayWarehousingItemController.submit(params).then(res => {
          this.$Toast('操作成功!')
          this.initModel()
          this.list = []
          if (res.datas.length > 0) {
            setTimeout(() => {
              this.myprintPackage(res.datas) // 打印
            }, 800);
          }
        })
      } catch (error) {
      }
    },

    async changeconsumableName(value) {
      if (!value) return
      let params = {
        consumableName: value,
      }
      let findIndex = this.list.findIndex(item => item.consumableName == value)
      if (findIndex > -1) {
        this.$Toast('该标签已存在!')
        this.model.consumableName = ''
        return
      }
      try {
        let res = await this.$service.TrayWarehousingItemController.scanConsumable(params)
        if (res.success) {
          this.list.push({
            consumableName: value,
            receiveQuantity: this.list.length > 0 ? this.list[0].receiveQuantity : '',
            isPrint: [],
          })
        }
        this.model.consumableName = ''
      } catch (error) {
        this.model.consumableName = ''
        // this.initModel()
      }
    },
    async changeboxNo(value) {
      this.model.consumableSpecName = ''
      this.model.consumableSpecText = ''
      this.model.quantity = ''
      this.model.consumableUnit = ''
      this.model.productOrderName = ''
      this.model.dateCode = ''

      if (!value) return
      try {
        let params = {
          boxNo: value,
        }
        let res = await this.$service.TrayWarehousingItemController.scanBoxNo(params)
        if (res.success) {
          this.model.consumableSpecName = res.datas[0].consumableSpecName
          this.model.consumableSpecText = res.datas[0].consumableSpecText
          this.model.quantity = res.datas[0].quantity
          this.model.consumableUnit = res.datas[0].consumableUnit
          this.model.productOrderName = res.datas[0].productOrderName
          this.model.dateCode = res.datas[0].dateCode
        }
      } catch (error) {
        this.model.boxNo = ''
      }
    },


    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
