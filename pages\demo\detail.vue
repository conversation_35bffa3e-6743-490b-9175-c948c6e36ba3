<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma10">
      


      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
                
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <!-- <view class="flex between h40 hcenter c_999">
            <view>设备</view>
            <view>
          </view> -->
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },

      model:[],

      paramsOption: {},
    };
  },

  onLoad(options) {

    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题
    this.nlsMap = nlsMap

    // this.paramsOption = e && JSON.parse(e.params)
    this.initSearchModel()
    this.getData()
  },
  methods: {
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      // this.$service.common.queryPagePrintLotInfo(params).then((res) => {
      //   if (res && res.success) {
      //     this.model = clearOldData ? res.data[0].content : [...this.model, ...res.data[0].content]
      //     if (res.data[0].last) {
      //       this.status = 'nomore'
      //     } else {
      //       this.status = 'loadmore'
      //     }
      //     this.refresherTriggered = false
      //   }
      // }).catch((e) => {
      //   this.refresherTriggered = false
      // })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>