// 公共部分
import commomZhcn from '@/common/i18n/common/zh-cn.js';
import commomEn from '@/common/i18n/common/en.js';
import commomZhtw from '@/common/i18n/common/zh-tw.js';

// 各业务（页面）
import pagesIndexZhcn from '@/common/i18n/pages/index/zh-cn.js';
import pagesIndexEn from '@/common/i18n/pages/index/en.js';
import pagesIndexZhtw from '@/common/i18n/pages/index/zh-tw.js';


import pagesSettingZhcn from '@/common/i18n/pages/setting/zh-cn.js';
import pagesSettingEn from '@/common/i18n/pages/setting/en.js';
import pagesSettingZhtw from '@/common/i18n/pages/setting/zh-tw.js';


// 定义语言国际化内容
export const messages = {
	'zh-Hans': {
		...commomZhcn,
		...pagesIndexZhcn,
		...pagesSettingZhcn
	},
	en: {
		...commomEn,
		...pagesIndexEn,
		...pagesSettingEn
	},
	'zh-Hant': {
		...commomZhtw,
		...pagesIndexZhtw,
		...pagesSettingZhtw
	}
};