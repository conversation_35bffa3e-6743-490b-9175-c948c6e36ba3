import host from './host.js'
import { getLocalStore, removeStore } from '../utils/common/localStore'
import { TOKEN, REFRESHTOKEN, USER_ID, DEFAULT_LANGUAGE } from '../utils/common/evtName.js'
import Toast from '../utils/common/toast'
import utils from '../utils/common/utils.js';
// import moment from 'moment'

const url_all = {
  DEV: host.dev,
  BUILD: host.build,
}
/*
  option = {
      url:'', 必填 请求地址
      data:{} 可选 参数
      method:'GET' 可选 默认GET  OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
      config: {
        handleSucess:false 是否自行处理异步请求
        isFormData:false 默认使用 true application/x-www-form-urlencoded,false application/json
        showLoading:false 默认false 是否显示加载中
      }
  }
*/
let server_url = ''
console.log('运行环境-----', process.env.NODE_ENV, url_all['DEV'])
process.env.NODE_ENV === 'development' ? (server_url = url_all['DEV']) : (server_url = url_all['BUILD'])

const getParamOption = (option = {}) => {
  !option.config && (option.config = {});
  if (option.config.showLoading != false) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
  }
  let url = `${server_url.api}${option.url}`

  let header = {
    'content-type': 'application/json',
    'userId': getLocalStore(USER_ID),
  };
  
  if (option.config.isFormData) {
    header['content-type'] = 'application/x-www-form-urlencoded'
  }
  const token = getLocalStore(TOKEN)
  const refreshToken = getLocalStore(REFRESHTOKEN)
  if (token) {
    header['Authorization'] = `Bearer ${token}`
  }

  if (refreshToken) {
    header['refreshToken'] = `${refreshToken}`
  }

  let requestData = option.param || {};
  const param = {
    url: url || '',
    data: requestData,
    method: option.method,
    header: header
  };
  return param;
};

const authFailed = () => {
  uni.setStorageSync('isLogin', false)
  uni.$emit('user-logout')
  removeStore(TOKEN)
  removeStore(REFRESHTOKEN)
  let curRoute = utils.getCurPageRoute()
  const loginRoute = 'pages/login/login'
  /* 如果不在登录页面 */
  if (curRoute && curRoute !== loginRoute) {
    Toast('登录失效，请重新登录')
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login',
      })
    }, 2000);
  }
}
// let serve_flag = false
// const SERVE_212 = 'http://10.231.31.212:7890'
// const SERVE_211 = 'http://10.231.31.211:7890' // 改用ngxin转发
const service = (option) => {
  let promise = new Promise((resolved, rejected) => {
    const param = getParamOption(option)
    uni.request({
      ...param,
      success: (res) => {
        if (option.config.showLoading != false) {
          uni.hideLoading()
        }
        if (res.statusCode === 200) {
          if (res.data) {
            if (option.config.handleSucess) { /* 自行处理 */
              return resolved(res.data)
            }
            let code = Number(res.data.code)
            if (code == '0000') { // 耀能接口
              resolved(res.data)
              return
            }
            if (code === 0) {
              if (typeof res.data === 'object' && !res.data.success) {
                !!res.data && uni.showToast({
                  title: res.data.msg || res.data.msgCode,
                  icon: 'none',
                  duration: 6000,
                })
              } else {
                resolved(res.data)
              }
            } else if (code === 401) {
              authFailed();
            } else if (code === 400) { // 用于校验去除
              let s = res.data.msg || res.data.msgCode
              let errMsg = s && s.match(/:(\S+)/)[1] // 正则匹配"：" 后的文案
              if (errMsg) {
                errMsg && Toast(errMsg)
              } else {
                Toast(s)
              }
            } else {
              let errMsg = res.data.msg || res.data.msgCode
              if (errMsg === 'fail') {
                return Toast('数据异常')
              }
              Toast(errMsg)
            }
          } else {
            Toast('服务器返回空数据');
          }
        } else if (res.statusCode === 404) {
          Toast('请求接口不存在');
        } else if (res.statusCode === 500) {
          Toast('服务器异常,请稍后重试');
        } else {
          Toast('服务器异常,请稍后重试');
        }
        rejected(res.data);
      },
      fail: (err) => {
        if (option.config.showLoading != false) {
          uni.hideLoading()
        }
        let msg = err.errMsg;
        if (msg.includes('request:fail')) {
          msg = '请求失败,请检查网络或服务器'
        }
        Toast(msg);
        rejected(err);
      }
    })
  })
  return promise
}
export default service
