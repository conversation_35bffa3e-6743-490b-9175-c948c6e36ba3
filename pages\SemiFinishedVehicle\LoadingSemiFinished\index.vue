<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="半成品载具上料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备号描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDescription" border="none"></u--input>
        </u-form-item>

        <u-form-item label="安装点" required borderBottom labelWidth="100">
          <u--input v-model="model.materialLocationName" border="none" placeholder="扫描设备上的材料安装点条码" @focus="focusEvent('materialLocationName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('materialLocationName')"></view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.processOperationName ? model.processOperationName + '/' + model.processOperationDesc : '' }}
          </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectProductSpecName('productSpecName')">
            <view>{{ model.productSpecName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectProductOrderName('productOrderName')">
            <view>{{ model.productOrderName }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="BOM" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view>{{ model.bomID }}</view>
          </view>
        </u-form-item>

        <u-form-item label="载具" required borderBottom labelWidth="100">
          <u--input v-model="model.durableName" border="none" :focus="focusObj.durableName" placeholder="扫描载具二维码" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <u-form-item label="批次号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.lotName }}
          </view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.productSpecDesc }}
          </view>
        </u-form-item>

        <u-form-item label="数量" required borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.quantity }}
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>
    <view class="btnContainer2">
      <view @click="submit">上料</view>
      <view @click="gotoQuery">查看已上料明细</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productOrderName: '工单编码不能为空',
        quantity: '数量不能为空',
        materialLocationName: '安装点不能为空',
        durableName: '载具二维码不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        durableName: false,
        materialPosition: false
      },
      show: false,
      content: '',
      modelKey: '',
      productSpecNameList: [],
      productOrderNameList: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productOrderName) {
        obj = this.columns.find(item => item.productOrderName === this.model.productOrderName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.durableName': {
      handler(res) {
        this.changeDurableName(res)
      }
    },
    'model.materialLocationName': {
      handler(res) {
        this.changeMaterialLocationName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    async changeMaterialLocationName(value) {
      if (!value || value == 'NA') return
      let params = {
        portName: value
      }
      try {
        let res = await this.$service.MaterialLoading.getMachineNameByPortName(params)
        if (res.datas.length > 0) {
          this.model.machineName = res.datas[0].machineName
        }
      } catch (error) {
        this.model.materialLocationName = null
      }
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/SemiFinishedVehicle/LoadingSemiFinished/modules/LoadedDetails?machineName=${this.model.machineName}`,
      })
    },
    focusEvent(type) {
      this.model[type] = ''
    },
    initModel() {
      this.model = {
        bomID: null, //	BOM号	string	
        bomVersion: null, //	BOM版本	string	
        durableName: null, //标签条码	string	
        productSpecDesc: null, //	物料	string	
        loadingQuantity: null, //上料数量	number	
        loadingTime: null, //上料时间	string(date-time)	
        machineDescription: null, //	设备描述	string	
        machineName: null, //	设备名	string	
        materialLocationName: 'NA', //安装点	string	
        processOperationName: null, //工序	string	
        productOrderName: null, //工单号	string	
        quantity: null, //	数量
        wordShop: null
      }
    },
    // 选择
    selectFirm(e) {
      if (this.modelKey == 'productOrderName') {
        this.model.productOrderName = e.value[0]
        this.select = false
        const params = {
          productOrderName: this.model.productOrderName
        }
        this.$service.SemiFinishedVehicle.getBomData(params).then(res => {
          if (res) {
            this.model.bomID = res.datas[0]
          }
        })
      } else {
        this.model.productSpecName = e.value[0]
        this.select = false
        const params = {
          productSpecName: this.model.productSpecName,
          workShop: this.model.wordShop
        }
        this.$service.SemiFinishedVehicle.getProductOrderList(params).then(res => {
          if (res) {
            this.productOrderNameList = res.datas
          }
        })
      }
    },
    // 上料开始
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.SemiFinishedVehicle.HALLoading(params).then(res => {
        this.$Toast('上料成功！')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.SemiFinishedVehicle.getMachineData(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, description, processOperationName, processOperationDesc, wordShop } = res.datas[0]
          this.model.machineDescription = description
          this.model.machineName = machineName
          this.model.processOperationName = processOperationName
          this.model.processOperationDesc = processOperationDesc
          this.model.wordShop = wordShop
          // 获取产品列表
          let data = {
            machineName
          }
          let resData = await this.$service.SemiFinishedVehicle.getProductSpecList(data)
          // resData.datas.forEach(item => {
          //   item.labelText = item
          // });
          this.productSpecNameList = resData.datas
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 标签条码 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        productSpecName: this.model.productSpecName,
        durableName: value,
        productOrderName: this.model.productOrderName,
        processOperationName: this.model.processOperationName
      }
      this.$service.SemiFinishedVehicle.getDurableNameData(params).then(res => {
        if (res.success && res.message) {
          this.show = true
          this.content = res.message
        } else {
          if (res.datas.length > 0) {
            let { productSpecDesc, quantity, lotName } = res.datas[0]
            this.model.lotName = lotName
            this.model.productSpecDesc = productSpecDesc
            this.model.quantity = quantity
          }
        }
      }).catch(res=>{
        this.model.durableName = ''
      })
    },

    // 选择产品编码
    selectProductSpecName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.productSpecNameList
    },

    // 选择工单编号
    selectProductOrderName(key) {
      this.modelKey = key
      this.select = true
      this.columns = this.productOrderNameList
    },

    async confirm() {
      this.show = false
      let params = {
        durableName: this.model.durableName,
        productOrderName: this.model.productOrderName,
        processOperationName: this.model.processOperationName
      }
      this.$service.SemiFinishedVehicle.getDurableNameData(params).then(res => {
        if (res.datas.length > 0) {
          let { productSpecDesc, quantity, lotName } = res.datas[0]
          this.model.lotName = lotName
          this.model.productSpecDesc = productSpecDesc
          this.model.quantity = quantity
        }
      })
    },

    cancel() {
      this.show = false
      this.model.durableName = ''
      this.model.lotName = ''
      this.model.productSpecDesc = ''
      this.model.quantity = ''
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.LCDP.01.01'
          break;
        case 'durableName':
          this.model.durableName = 'DJ00001'
          break;
        case 'materialLocationName':
          this.model.materialLocationName = 'P02'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'durableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
