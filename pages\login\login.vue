  <template>
  <view class="register_login">
    <view class="imgwindtest">
      <image src="../../static/login.png" alt="">
      <!-- <text class="fs30 c_fff mb20"> 耀能 </text> -->
    </view>
    <view class="formwindow ma15 bc_fff">
      <view class="h50 flex hcenter flex-jcse">
        <view :class="loginType==='1'?'active':''" @click="changeLoginType('1')">{{$t('login.accountLogin')}}</view>
        <!-- <view :class="loginType==='2'?'active':''" @click="changeLoginType('2')">域账号登录</view> -->
      </view>
      <form @submit="gcLogin" class="form">
        <!-- <view class="info_wrapper password_wrapper">
          <view class="formtitle">{{$t('login.language')}}</view>
          <view class="flex between h40 hcenter"  @click="select=true">
            <view v-if="applicationLocale">  {{ $utils.filterObjLabel($LANGUAGE_ARRAY, applicationLocale) }}</view>

            <view class="c_c0c4cc" v-else>{{$t('common.form.pleaseSelect')}}{{$t('login.language')}}</view>
            <view class="ml5" :style="{ transform: select  ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </view> -->
        <view class="info_wrapper password_wrapper">
          <view class="formtitle">{{$t('login.account')}}</view>
          <view class="flex hcenter">
            <input class="flex1  myinput" type="text" name="username" v-model="username" />
          </view>
        </view>
        <view class="info_wrapper password_wrapper">
          <view class="formtitle">{{$t('login.password')}}</view>
          <view class="flex hcenter w100x">
            <input class="flex1  myinput" name="password" :password="!isPassword" :type="!isPassword?'password':'text'" v-model="password"  />
            <view  @click="isPassword=!isPassword" v-if="password.length>0" class="w25">
               <text class="iconfont" :class="isPassword?'icon-eye':'icon-eye-off'"></text>
            </view>
          </view>
        </view>
        <button type="primary" form-type="submit" class="send_btn">{{$t('login.login')}}</button>
      </form>
    </view>
     <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import { myCrypto } from '../../utils/des_encrypt/crypto.js'
import { USER_ID, USER_NAME, USER_PSW, TOKEN, REFRESHTOKEN, MENUS, CURRENT_LOCALE, POROS_SYSTEMID, DEFAULT_LANGUAGE } from '@/utils/common/evtName.js'
export default {
  data() {
    return {
      select: false,
      columns: this.$LANGUAGE_ARRAY,
      applicationLocale: '',
      username: '',
      password: '',
      isPassword: false,
      loginType: '1',
      globalMap: getApp().globalData.globalMap // 获取全局数据
    }
  },
  onLoad() {
    this.username = ''
    this.password = ''
    // if (this.$getLocal(USER_ID)) {
    //   this.username = this.$getLocal(USER_ID)
    // }
    // if (this.$getLocal(DEFAULT_LANGUAGE)) {
    //   this.applicationLocale = this.$getLocal(DEFAULT_LANGUAGE)
    // }
  },
  methods: {
    async initCurrentLocal() {
      await this.$service.nls.getqueryCurrentLocal()
      // return new Promise((res, rej) => {
      //   const onSuccess = (data) => {
      //     if (data.datas.length > 0) {
      //       return res(data.datas[0])
      //     }
      //   }
      //   const onFail = (data) => {
      //     messageAlert(data.message || data.msg, 'warning')
      //     return rej(data)
      //   }
      //   let params = {}
      //   useAxiosGet('/LocaleController/queryCurrentLocal', params, onSuccess, onFail, null)
      // })
    },

    selectFirm(e) {
      this.select = false
      this.$i18n.locale = e.value[0].value;
      if (this.applicationLocale !== e.value[0].value) {
        uni.showToast({
          icon: 'none',
          title: this.$t('setting.languageChangeConfirm'),
          duration: 3000
        });
        this.$getLocal(DEFAULT_LANGUAGE)
        setTimeout(() => {
          this.applicationLocale = e.value[0].value
          this.$i18n.locale = e.value[0].value;
          this.$setLocal(DEFAULT_LANGUAGE, e.value[0].value)
          // #ifdef APP-PLUS
          if (!this.isAndroid) {
            plus.runtime.restart();
          }
          // #endif
        }, 0)
      }
    },
    changeLoginType(type) {
      this.loginType = type
    },
    async ynLogin() {
      if (!this.username) {
        return this.$Toast('请输入账号')
      }
      if (!this.password) {
        return this.$Toast('请输入密码')
      }
      let params = {
        accountType: 'BUSINESS',
        loginType: this.loginType === '1' ? "USER_NAME_PASSWORD" : 'LDAP_AUTO',
        password: this.password,
        username: this.username,
        lang: this.applicationLocale,
      }
      this.$removeLocal(TOKEN)
      this.$removeLocal(REFRESHTOKEN)
      let tokenRes = await this.$service.common.ynLogin(params)
      this.$setLocal(TOKEN, tokenRes.data.accessToken)
      this.$setLocal(REFRESHTOKEN, tokenRes.data.refreshToken)


      let userInfoRes = await this.$service.common.ynUserInfo(params)
      this.$setLocal(USER_ID, userInfoRes.result.account)
      this.$setLocal(USER_NAME, userInfoRes.result.name)
      this.$service.common.saveOrUpdate({
        userId: userInfoRes.result.account,
        userName: userInfoRes.result.name
      })


      let menuObj = {
        appId: "********"
      }
      let menuRes = await this.$service.common.ynMenu(menuObj)
      let arr = menuRes.result.find((item) => item.name === 'pda')
      let PDAMenu = arr && arr.children
      this.$setLocal(MENUS, PDAMenu)
      this.$refs.uToast.show({
        type: 'success',
        title: '成功',
        message: '登陆成功',
        iconUrl: '@/static/success.png',
        complete() {
          uni.redirectTo({
            url: '/pages/home/<USER>',
          })
        },
      })
    },
    gcLogin() {
      let vm = this
      // 先获取loginKey
      console.log('========================')
      if (!this.username) {
        return this.$Toast('请输入账号')
      }
      if (!this.password) {
        return this.$Toast('请输入密码')
      }
      this.$service.common.loginKey(this.username).then((res) => {
        let data = {
          grant_type: 'password',
          isSerialize: true,
          username: this.username,
          password: myCrypto.encryptByDES(this.password, res.data), //用loginkey加盐加密登录
        }
        vm.$service.common.login(data, { isFormData: true }).then((res) => {
          if (res.data.accessToken) {
            this.$setLocal(USER_PSW, this.password)
            this.$setLocal(TOKEN, res.data.accessToken)
            this.$setLocal(REFRESHTOKEN, res.data.refreshToken)
            // 获取用户信息
            vm.$service.common.usermessage().then((res) => {
              this.$setLocal(USER_ID, res.data.uid)
              this.$setLocal(USER_NAME, res.data.name)
            })
            // 获取菜单
            vm.$service.common.mianmenu({}, { isFormData: true }).then(async (res) => {
              try {
                if (res.data && res.data.menus.length > 0) {
                  let params = {
                    systemId: res.data.systemId,
                  }
                  // 存储id for国际化菜单更新
                  this.$setLocal(POROS_SYSTEMID, res.data.systemId)
                  
                  //  开发国际化接口有权限
                  if (process.env.NODE_ENV === 'development') {
                    let PDAMenu = res.data.menus.filter((item) => item.mask === 'PDA_menus')[0].children
                    this.$setLocal(MENUS, PDAMenu)
                  } else {
                    //  使用国际化菜单
                    let findNlsPorosMenuRes = await vm.$service.nls.findNlsPorosMenuTree(params)
                    let PDAMenu = findNlsPorosMenuRes.data.find((item) => item.mask === 'PDA_menus').children
                    this.$setLocal(MENUS, PDAMenu)
                  }

                  let currentLocaleRes = await this.$service.nls.getqueryCurrentLocal({})
                  let currentLocale = currentLocaleRes.datas[0]
                  this.$setLocal(CURRENT_LOCALE, currentLocale)

                  this.$refs.uToast.show({
                    type: 'success',
                    title: '成功',
                    message: '登陆成功',
                    iconUrl: '@/static/success.png',
                    complete() {
                      uni.redirectTo({
                        url: '/pages/home/<USER>',
                      })
                    },
                  })
                  uni.setStorageSync('isLogin', true)
                  uni.$emit('login-success', true) // 登录成功后添加心跳回调
                }
              } catch (error) {
                console.log('error', error);
                this.$Toast('用户没有MES权限，请找管理员开通')
              }
            })
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../styles/publicStyle.scss';

.register_login {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #f3f3f7;

  .face_wrapper {
    position: absolute;
    top: 250rpx;
    left: 50%;
    margin: 20rpx auto;
    padding-bottom: 65rpx;

    .face {
      width: 300rpx;
      height: 100rpx;
      margin-left: -150rpx;
    }
  }
  .imgwindtest {
    width: 100%;
    height: 700rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }

  .formwindow {
    position: absolute;
    left: 20rpx;
    right: 20rpx;
    top: 400rpx;
    z-index: 10;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0px 2px 16px 0px rgba(124, 119, 237, 0.08);
    overflow: hidden;
  }

  .form {
    width: 85%;
    margin-top: 50%;
    transform: translateY(-50%);

    .info_wrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
      padding-bottom: 6rpx;
      .formtitle {
        overflow-wrap: break-word;
        color: rgba(3, 0, 5, 0.88);
        font-size: 16px;
        font-family: PingFangSC-Regular;
        white-space: nowrap;
        line-height: 16px;
        text-align: left;
        margin-bottom: 2%;
      }
    }
    .password_wrapper {
      margin-top: 40rpx;
    }
  }
}
.myinput {
  border: none; //去除边框
  height: 70rpx;
  outline: none;
}
.send_btn {
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #fff;
  width: 100%;
  margin-top: 60rpx;
  border-radius: 22px;
  background-color: $Style-Color;
}
.active {
  font-weight: bold;
}

.icon-eye {
  font-size: 40rpx;
  color: black;
}

.icon-eye-off {
  font-size: 40rpx;
  color: black;
}
</style>
