<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="材料不良记录" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="材料标签条码" borderBottom required labelWidth="120">
          <u--input v-model="model.clbqtm" border="none" focus placeholder="请扫描材料标签条码" @focus="focusEvent('clbqtm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('clbqtm')"></view>
        </u-form-item>

        <u-form-item label="不良材料" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.blcl }} </view>
        </u-form-item>

        <u-form-item label="单位" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.dw }} </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.scgd }} </view>
        </u-form-item>

        <u-form-item label="不良现象" required borderBottom labelWidth="130">
          <view class="w100x flex right" @click="selectReasonCodeType('blxx')">
            <view>{{ model.blxx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="不良数量" required borderBottom labelWidth="100">
          <!-- <view class="w100x flex right"> {{ model.blsl }} </view> -->
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.blsl" border="none" placeholder="请输入不良数量" type="number"></u--input>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="物料数量" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.wlsl }} </view>
        </u-form-item>

        <u-form-item label="设备号" borderBottom required labelWidth="170">
          <u--input v-model="model.sbh" border="none" focus placeholder="请扫描设备二维码" @focus="focusEvent('sbh')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('sbh')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.sbms }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        clbqtm: '',
        blcl: '',
        dw: '',
        blxx: '',
        blxxType: '',
        blxxText: '',
        reasonCodeType: '',
        blsl: '',
        wlsl: '',
        sbh: '',
        sbms: '',
        gx: '',
        scgd: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: []
    }
  },
  watch: {
    'model.clbqtm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.sbh': {
      handler(res) {
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        consumableSpecName: this.model.consumableSpecName
      }
      this.$service.QualityControl.getClReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            label: item.reasonCode + '/' + item.reasonCodeDesc,
            value: item.reasonCode,
            reasonCodeDesc: item.reasonCodeDesc,
            reasonCodeType: item.reasonCodeType
          }
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      if(type == 'blxx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if(this.selectType == 'blxx') {
        this.model.blxx = e.value[0].label
        this.model.blxxType = e.value[0].value
        this.model.blxxText = e.value[0].reasonCodeDesc
        this.model.reasonCodeType = e.value[0].reasonCodeType
      }
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/BindingAndUnbinding/modules/ProductList?durableName=${this.model.durableName}`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        clbqtm: '',
        blcl: '',
        dw: '',
        blxx: '',
        blxxType: '',
        blxxText: '',
        reasonCodeType: '',
        blsl: '',
        wlsl: '',
        sbh: '',
        sbms: '',
        gx: '',
        scgd: ''
      }
    },
    submit(value, type) {
      if(!this.model.clbqtm) {
        this.$Toast('请输入材料标签条码！')
        return
      }
      if(!this.model.blxxType) {
        this.$Toast('请选择不良现象！')
        return
      }
      if(!this.model.sbh) {
        this.$Toast('请输入设备号！')
        return
      }
      if(!this.model.blsl) {
        this.$Toast('请输入不良数量！')
        return
      }
      let params = {
        functionType: 'Record',
        ngState: 'N',
        ...this.model,
        consumableName: this.model.clbqtm,
        machineName: this.model.sbh,
        reasonCodeType: this.model.reasonCodeType,
        reasonCode: this.model.blxxType,
        reasonCodeDesc: this.model.blxxText,
        // quantity: this.model.blsl
        ngQuantity: this.model.blsl
      }
      this.$service.QualityControl.NgConsumableRecordAndJudgeSubmit(params).then(res => {
        this.$Toast('材料不良记录提交成功！')
        // 提交成功初始化
        this.model = {
          clbqtm: '',
          blcl: '',
          dw: '',
          blxx: '',
          blxxType: '',
          blxxText: '',
          reasonCodeType: '',
          blsl: '',
          wlsl: '',
          sbh: '',
          sbms: '',
          gx: '',
          scgd: ''
        }
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 材料标签条码信息 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        functionType: 'Record',
        consumableName: value
      }
      try {
        let res = await this.$service.QualityControl.getConsumableData(params)
        console.log('getConsumableData', res);
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...res.datas[0],
            clbqtm: this.model.clbqtm,
            blxx: '',
            blxxType: '',
            blxxText: '',
            reasonCodeType: '',
            sbh: this.model.sbh ? this.model.sbh : '',
            sbms: this.model.sbms ? this.model.sbms : '',
            gx: this.model.gx ? this.model.gx : '',
            scgd: data.productOrderName,
            blsl: data.ngQuantity,
            wlsl: data.quantity,
            blcl: data.consumableSpecName + '/' + data.description,
            dw: data.consumableUnit
          }
          // 获取不良现象
          this.GetReasonCodeType()
        } else {
          this.model.clbqtm = ''
          this.$Toast('材料标签不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.QualityControl.getMachineList(params)
        console.log('getMachineList', res);
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...this.model,
            ...data,
            sbms: data.machineDesc,
            gx: data.processOperationName + '/' + data.processOperationSpec
          }
          // 获取不良现象
          // this.GetReasonCodeType()
        } else {
          this.model.sbh = ''
          this.$Toast('设备不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.model.sbh = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
