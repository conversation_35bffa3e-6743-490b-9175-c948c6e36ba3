<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="补打印详情" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="myContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>设备</view>
            <view>
              {{ $utils.optionShowConfig(ele.printMachineName, ele.printMachineDesc) }}
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>产品</view>
            <view>
              {{ $utils.optionShowConfig(ele.printProductSpecName, ele.printProductSpecDesc) }}
            </view>
          </view>
          <!-- <view class="flex between h40 hcenter c_999">
            <view>工单</view>
            <view>{{ ele.printProductOrderName }}</view>
          </view> -->
          <view class="flex between h40 hcenter c_999">
            <view>工序</view>
            <view>
              {{ $utils.optionShowConfig(ele.printProcessOperationName, ele.printProcessOperationDesc) }}
            </view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>批次编码</view>
            <view>{{ ele.printLotName }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>标签类型</view>
            <view>{{ ele.printerType == 'Label' ? '小标签' : '出货牌' }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>打印时间</view>
            <view>{{ ele.createTime }}</view>
          </view>
          <view>
            <u-button type="success" text="补打印" @click="summit(ele)"></u-button>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import printPackage from "@/mixins/printPackage";
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [printPackage, ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      paramsOption: {},
    };
  },

  onLoad(e) {
    this.paramsOption = e && JSON.parse(e.params)
    this.initSearchModel()
    this.getData()
  },
  methods: {
    summit(item) {
      const data = {
        machineName: item.printMachineName,
        processOperationName: item.printProcessOperationName,
        lotName: item.printLotName
      }
      let otherParams = {
        consumableSpecName: item.printProductSpecName,
      }
      this.printPackage(data, item.printerType, otherParams)
    },
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      this.$service.common.queryPagePrintLotInfo(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.data[0].content : [...this.model, ...res.data[0].content]
          if (res.data[0].last) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>