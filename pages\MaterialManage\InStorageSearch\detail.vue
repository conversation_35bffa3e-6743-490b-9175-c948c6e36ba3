<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="入库明细" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <u--form labelPosition="left" :model="formModel" labelWidth="100">
        <u-form-item label="扫描条码" borderBottom labelWidth="100">
          <u--input v-model="formModel.barCode" border="none" :focus="barCodefocus" placeholder="请扫描或输入扫描条码"></u--input>
          <view class="iconfont icon-saoma" @click="scan('barCode')"></view>
        </u-form-item>
        <u-form-item label="入库单号" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ paramsOption.warehouseNo }}</view>
        </u-form-item>
        <u-form-item label="已扫描批次数量/总数量" labelWidth="200">
          <view class="w100x flex right">{{ model.consumableScanQuantity || 0 }}/{{ model.consumableQuantity || 0 }}</view>
        </u-form-item>
        <!-- <view v-if="show">
          <u-form-item label="单据类型" borderBottom labelWidth="100">
            <view class="w100x flex right">{{ paramsOption.warehouseType }}</view>
          </u-form-item>
          <u-form-item label="采购单号" borderBottom labelWidth="100">
            <view class="w100x flex right">{{ paramsOption.procureCode }}</view>
          </u-form-item>
        </view> -->
      </u--form>
    </view>
    <!-- <view class="flex center" @click="show = !show">
      <view :style="{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
        <u-icon name="arrow-down"></u-icon>
      </view>
    </view> -->
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model.warehouseOrderParameterDetailDtoList" :key="index" :class="getClass(ele, index)">
            <view class="flex flex_wrap">
              <view>
                <!-- 批次号#物料编码#供应商编码#入库单号#数量  -->
                {{ ele.consumableName }}#{{ ele.materialCode }}#{{ ele.supplierCode }}#{{ paramsOption.warehouseNo }}#{{ ele.quantity }}
              </view>
              <view class="flex w100x hcenter h40 c_999">
                <view class="w70">物料:</view>
                <view style="margin-right: auto">{{ $utils.optionShowConfig(ele.materialCode, ele.materialText) }}</view>
                <view class="w50" @click="cancel(ele)" v-if="ele.status == 'Qualified' && ele.scanFlag == '1'">
                  <u-button type="error" text="撤销" :customStyle="{ height: '50rpx' }"> </u-button>
                </view>
              </view>
              <view class="flex w100x hcenter h40 c_999">
                <view class="w70">批次号:</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <!-- <view class="flex w100x hcenter h40 c_999">
                <view class="w70">供应商:</view>
                <view >{{ $utils.optionShowConfig(ele.supplierCode, ele.supplierText) }}</view>
              </view> -->
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">入库数量:</view>
                <view>{{ ele.quantity }}</view>
              </view>
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">物料单位:</view>
                <view>{{ ele.unit }}</view>
              </view>
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">供应商:</view>
                <view class="fontmore fontrow-1 flex1">{{ ele.supplierText }}</view>
              </view>
              <!-- <view class="flex w50x hcenter h40 c_999">
                <view class="w70">有效期:</view>
                <view>{{ ele.deadTime }}</view>
              </view> -->
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">创建人:</view>
                <view>{{ ele.createUser }}</view>
              </view>
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">品质状态:</view>
                <view> {{ $utils.filterObjLabel(dicts.warehouseOrderParameterStatusList, ele.status) }}</view>
              </view>
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">创建时间:</view>
                <view class="fs10 nowrap"> {{ ele.createTime }}</view>
              </view>
              <view class="flex w50x hcenter h40 c_999">
                <view class="w70">扫描状态:</view>
                <view>{{ ele.scanFlag == '1' ? '已扫描' : ele.scanFlag == '0' ? '未扫描' : '' }}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>

    <view class="btnContainer" @click="submit">入库</view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerStartDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    //  
    this.changebarCode = this.$debounce(this.changebarCode, 1000)
    return {
      formModel: {
        barCode: '',
      },
      barCodefocus: true,
      model: {},
      dicts: {},
      paramsOption: {},
      machineName: '',
      simpleTrackProduct: {},
      show: false,
    };
  },
  watch: {
    'formModel.barCode': {
      handler(val) {
        this.changebarCode(val)
      }
    },
  },
  async onLoad(e) {
    this.paramsOption = e && JSON.parse(e.params)
    await this.getEnumValue('WarehouseOrderParameterStatus', 'warehouseOrderParameterStatusList') // 状态
    await this.getData()
  },
  methods: {
    moment,
    async submit() {
      if (!this.model.warehouseOrderParameterDetailDtoList) {
        return this.$Toast(`该入库明细中暂无数据，不允许入库!`)
      }
      let flag2 = this.model.warehouseOrderParameterDetailDtoList.some(item => item.status == 'Unqualified')
      if (flag2) {
        return this.$Toast(`该入库明细中存在不合格的批次号，不允许入库!`)
      }
      let flag = this.model.warehouseOrderParameterDetailDtoList.every(item => item.status == 'Qualified')
      if (!flag) {
        return this.$Toast(`该入库单中存在待检的批次号，请先质检!`)
      }
      let params = {
        warehouseNo: this.paramsOption.warehouseNo
      }
      await this.$service.WarehouseOrdersController.pdaWare(params).then((res) => {
        if (res && res.success) {
          this.$Toast('操作成功！')
        }
      })
    },
    async changebarCode(value) {
      if (!value) return
      this.barCodefocus = false
      let params = {
        barCode: value,
        warehouseNo: this.paramsOption.warehouseNo
      }
      try {
        let res = await this.$service.WarehouseOrdersController.scan(params)
        if (res.success) {
          this.$Toast('操作成功！')
          this.formModel.barCode = null
          this.barCodefocus = true
          this.getData()
        }
      } catch (error) {
        this.formModel.barCode = null
        this.barCodefocus = true
      }
    },
    leftClick() {
      this.$utils.backAndUpdata('returnRefresh')
    },

    async getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      await this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    getClass(item, index) {
      // 合格
      if (item.status == 'Qualified') {
        // 已扫码
        if (item.scanFlag == '1') {
          return 'bc_ddf7c0'
        } else {
          // 未扫码
          return 'bc_ddddda'
        }
      }
      // 待检
      if (item.status == 'WaitCheck') {
        return 'bc_fbf9b0'
      }
      // 不合格
      if (item.status == 'Unqualified') {
        return 'bc_f9bdb3'
      }

    },
    initSearchModel(val) {
      this.searchModel = {
        // machineName: this.machineName,
        pageNo: this.pageNumber,
        limit: this.pageSize,
      }
    },
    cancel(item) {
      uni.showModal({
        title: '提示',
        content: `是否确认撤批次号？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              consumableName: item.consumableName,
              warehouseNo: this.paramsOption.warehouseNo
            }
            this.$service.WarehouseOrdersController.cancel(params).then((res) => {
              if (res.success) {
                this.$Toast('撤销成功！')
                this.getData()
              }
            })
          }
          if (res.cancel) { }
        },
      })


    },
    async getDetail() {
      let params = {
        warehouseNo: this.paramsOption.warehouseNo
      }
      await this.$service.WarehouseOrdersController.queryDetail(params).then((res) => {
        if (res && res.success) {
          this.model = res.datas[0]
        }
      })
    },
    async getData(clearOldData = false, refresh = false) {
      // clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      // this.searchModel.pageNo = this.pageNumber
      // this.searchModel.limit = this.pageSize
      // setTimeout(() => {
      //   // let res = Array.from({ length: 20 }, (v, i) => i)
      //   let res = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      //   this.model = clearOldData ? res : [...this.model, ...res]
      //   this.refresherTriggered = false
      //   if (this.model.length > 30) {
      //     this.status = 'nomore'
      //   } else {
      //     this.status = 'loadmore'
      //   }
      // }, 1000)
      // return

      let params = {
        warehouseNo: this.paramsOption.warehouseNo
      }
      await this.$service.WarehouseOrdersController.queryDetail(params).then((res) => {
        if (res && res.success) {
          this.model = res.datas[0]
          this.status = 'nomore'
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })

      // let params = JSON.parse(JSON.stringify(this.searchModel))
      // this.$service.roller.outQueryPageList(params).then((res) => {
      //   if (res && res.success) {
      //     let { simpleTrackProductRequest, trackProductRequestPage } = res.data
      //     this.simpleTrackProduct = simpleTrackProductRequest
      //     this.model = clearOldData ? trackProductRequestPage.records : [...this.model, ...trackProductRequestPage.records]
      //     if (this.searchModel.pageNo == trackProductRequestPage.pages) {
      //       this.status = 'nomore'
      //     } else {
      //       this.status = 'loadmore'
      //     }
      //     this.refresherTriggered = false
      //   }
      // }).catch((e) => {
      //   this.refresherTriggered = false
      // })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.bc_fbf9b0 {
  background: #fbf9b0;
}

.bc_ddddda {
  background: #ddddda;
}

.bc_f9bdb3 {
  background: #f9bdb3;
}

.bc_ddf7c0 {
  background: #ddf7c0;
}
</style>