import host from './host.js'
import { getLocalStore, removeStore } from '../utils/common/localStore'
import { TOKEN, REFRESHTOKEN, USER_ID } from '../utils/common/evtName.js'
import Toast from '../utils/common/toast'
import utils from '../utils/common/utils.js'

let server_url = ''
process.env.NODE_ENV === 'development' ? (server_url = host.dev) : (server_url = host.build)

const authFailed = () => {
    uni.setStorageSync('isLogin', false)
    uni.$emit('user-logout')
    removeStore(TOKEN)
    removeStore(REFRESHTOKEN)
    let curRoute = utils.getCurPageRoute()
    const loginRoute = 'pages/login/login'
    if (curRoute && curRoute !== loginRoute) {
        Toast('登录失效，请重新登录')
        setTimeout(() => {
            uni.reLaunch({
                url: '/pages/login/login',
            })
        }, 2000)
    }
}

const uploadFileService = ({ filePath, name = 'file', formData = {}, config = {}, url }) => {
    if (!filePath) return Toast('未选择上传文件')

    if (config.showLoading !== false) {
        uni.showLoading({ title: '上传中...', mask: true })
    }

    const token = getLocalStore(TOKEN)
    const refreshToken = getLocalStore(REFRESHTOKEN)

    const header = {
        Authorization: `Bearer ${token}`,
        userId: getLocalStore(USER_ID),
    }

    if (refreshToken) {
        header['refreshToken'] = `${refreshToken}`
    }

    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: `${server_url.api}${url}`,
            filePath,
            name,
            formData,
            header,
            success: (res) => {
                if (config.showLoading !== false) uni.hideLoading()
                if (res.statusCode === 200) {
                    let data = JSON.parse(res.data)
                    if (data.code === 0 || data.code === '0000') {
                        resolve(data)
                    } else if (data.code === 401) {
                        authFailed()
                    } else {
                        Toast(data.msg || data.msgCode || '上传失败')
                        reject(data)
                    }
                } else {
                    Toast(`上传失败 (${res.statusCode})`)
                    reject(res)
                }
            },
            fail: (err) => {
                if (config.showLoading !== false) uni.hideLoading()
                Toast('上传失败，请检查网络')
                reject(err)
            },
        })
    })
}

export default uploadFileService
