<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="可用物料查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex between h40 hcenter c_999">
        <view>产品编码:</view>
        <view>{{this.consumableSpecName}}</view>
      </view>
      <view class="fs16 c_00b17b">可投物料信息</view>
    </view>
    <view class="listContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view>
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>标签条码</view>
              <view>{{ele.consumableName}}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>接收时间</view>
              <view>{{ele.createTime}}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>可用数量(kg)</view>
              <view class="flex hcenter">
                <view>{{ele.quantity}}</view>
              </view>
            </view>
          </view>
          <NoData v-if="!model || model.length === 0"></NoData>
        </view>
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      consumableSpecName: '',
      simpleTrackProduct: {}
    };
  },

  onLoad(e) {
    this.consumableSpecName = e && e.consumableSpecName
    this.getData()
  },
  methods: {
    getData() {
      const params = {
        consumableSpecName: this.consumableSpecName
      }
      this.$service.materialunFeeding.getConsumableList(params).then((res) => {
        if (res && res.success) {
          this.model = res.datas
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
</style>