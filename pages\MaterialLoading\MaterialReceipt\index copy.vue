<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="物料接收确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10">
      <view class="myContainer ml10 mr10 mb4">
        <u--form labelPosition="left" :model="form" labelWidth="110">
          <u-form-item label="配送人员编号:" required labelWidth="110">
            <u--input v-model="form.machineName" border="none" focus placeholder="请扫描或输入配送人员" @focus="focusEvent('machineName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="接收人员编号:" required labelWidth="110">
            <u--input v-model="form.machineName" border="none" focus placeholder="请扫描或输入接收人员" @focus="focusEvent('machineName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="ERP发料单号:" required labelWidth="110">
            <u--input v-model="form.machineName" border="none" focus placeholder="请扫描或输入ERP发料单号" @focus="focusEvent('machineName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="MES领料单号:" labelWidth="130">
            <view class="w100x flex right pr30">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
          <u-form-item label="工单编号:" labelWidth="100">
            <view class="w100x flex right pr30">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
          <u-form-item label="产品名称:" labelWidth="100">
            <view class="w100x flex right pr30">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
          <u-form-item label="需求日期:" labelWidth="100">
            <view class="w100x flex right pr30">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">物料明细</view>
      <!-- <scroll-view class="h300" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(item, index) in simpleTrackProduct" :key="index" class="bt_e1e1e1 pa10">
            <view class="flex between h30 hcenter c_000">
              <view>物料编码:</view>
              <view>{{ item.consumableName }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>使用工序:</view>
              <view>{{ item.createTime }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>申请数量(PCS):</view>
              <view>{{ item.createQuantity }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>发料数量(PCS):</view>
              <view class="flex hcenter">
                <view>{{ item.quantity }}</view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view> -->
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">参数编码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">参数名称</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验状态</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">操作</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in model" :key="index" class="flex bl_e1e1e1 h40">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.code }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.name }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.status }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2"><view class="btn" :class="{ 'c-active': item.isSelect }">数据录入</view></view>
            </view>
            <view class="pt100" v-if="!model || model.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
      </view>
    </view>
    <view class="btnContainer2 bottomBtn">
      <view @click="mixingStart">接收</view>
      <view class="bc_e9283e" @click="mixingEnd">拒收</view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'MaterialReceipt',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      form: {},
      simpleTrackProduct: [{ consumableName: '1111' }, { consumableName: '2222' }, { consumableName: '3333' }, { consumableName: '4444' }, { consumableName: '5555' }],
      model: [
        {
          code: 'C00001',
          name: '面密度',
          status: '已完成'
        },
        {
          code: 'C00002',
          name: '单面削薄',
          status: '未开始'
        },
        {
          code: 'C00003',
          name: '失重率',
          status: '未开始'
        },
      ]
    };
  },

  onLoad(e) {
    this.initModel()
  },
  methods: {
    initModel() {
      this.form = {
        machineName: null, // 设备号
        machineDescription: null, // 搅拌机描述
        quantity: null // 数量
      }
    },
    focusEvent(type) {
      this.form[type] = ''
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.MaterialLoading.UnLoadingQueryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, machineDescription } = res.datas[0]
          this.form.machineDescription = machineDescription
          this.form.machineName = machineName
          this.getData(value)
        } else {
          this.$Toast('设备不存在！')
        }
      } catch (error) {
        this.form.machineName = null
      }
    },
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      return resArr
    },
    getData(machineName) {
      let params = {
        machineName,
      }
      this.$service.MaterialLoading.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            res.datas.forEach(item => item.qty = 0)
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          } else {
            this.simpleTrackProduct = []
          }
        }
      })
    },
    submit(item) {
      if (item.qty == '') return this.$Toast('数量不能为空！')
      let params = {
        ...item
      }
      params.quantity = params.qty
      this.$service.MaterialLoading.ConsumableUnLoading(params).then((res) => {
        if (res && res.success) {
          this.$Toast('卸料成功！')
          setTimeout(() => {
            this.getData(this.form.machineName)
          }, 800);
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.form.machineName = 'YN-GZ-WG-ZP-401'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.form, key, res.result)
          },
        })
      }
      // #endif
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  .topContainer {
    flex-shrink: 0;
  }
  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
  /deep/ .u-form-item__body {
    padding: 13rpx;
  }
  .bottomBtn {
    position: fixed;
    bottom: -28rpx;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .table_header {
    flex-shrink: 0;
  }
  .table_content {
    flex: 1;
    overflow-y: scroll;
  }
  .btn {
    width: 120rpx;
    height: 50rpx;
    border-radius: 10%;
    background-color: #0285be;
    border: 2rpx solid #b1c2db;
    text-align: center;
    line-height: 50rpx;
    font-size: 24rpx;
    color: #fff;
  }
}
</style>