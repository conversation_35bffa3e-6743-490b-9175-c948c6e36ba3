<template>
  <view class="myContainerPage">
    <u-navbar title="设备清洗" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="130">
        <u-form-item label="设备编码" borderBottom required labelWidth="130">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备编码" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>
        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineDesc }} </view>
        </u-form-item>
        <u-form-item label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.processOperationName }} </view>
        </u-form-item>
        <u-form-item label="上次清洗时间" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.lastCleanTime }} </view>
        </u-form-item>
        <u-form-item label="设备清洗履历" borderBottom labelWidth="120">
          <view class="w100x flex right"></view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <view class="h30 lin30 fb pl10 mb4 c_00b17b fs16">设备清洗明细</view>
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">清洗位置</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w70">是否清洗</view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in model.machineCleanInfoDtoList" :key="index" class="flex bl_e1e1e1 h40">
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.machineLocationName }}</view>
              <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w70 pl4 pr4 pt2 pb2">
                <u-checkbox-group @change="value => checkboxChange(value, item)">
                  <u-checkbox :name="true" :customStyle="{ marginBottom: '8px' }"> </u-checkbox>
                </u-checkbox-group>
              </view>
            </view>
            <view class="pt100" v-if="!model.machineCleanInfoDtoList || model.machineCleanInfoDtoList.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
      </view>
    </view>
    <view class="btnContainer" @click="submit">清洗完成</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  mixins: [ScrollMixin],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编码不能为空',
      },
      model: {},
      tableList: []
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 工装
        description: null, // 工装描述
        lastCalibrationTime: null,
        machineCleanInfoDtoList: []
      }
      this.tableList = []
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let index = this.model.machineCleanInfoDtoList.findIndex(item=> !item.isClean)
      if (index > -1) {
        let data = this.model.machineCleanInfoDtoList[index]
        this.$Toast(`设备${this.model.machineName}清洗位置${data.machineLocationName}还未清洗！`)
        return
      }
      let params = {
        ...this.model
      }
      this.$service.EquipmentManagement.HALUnLoading(params).then(res => {
        if (res.success) {
          this.$Toast('清洗完成!')
          this.initModel()
        }
      })
    },

    /* 工装 */
    async changeMachineName(value) {
      if (!value) return
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.EquipmentManagement.getMachineCleanInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的设备编码!')
          }
          this.model = uni.$u.deepClone(res.datas[0])
          this.model.machineCleanInfoDtoList.forEach(item => {
            item.isClean = false
          });
          // this.model.machineCleanInfoDtoList = []
          // this.tableList = uni.$u.deepClone(res.datas[0].machineCleanInfoDtoList)
        }
      } catch (error) {
        this.model.machineName = null
        this.model.machineCleanInfoDtoList = []
      }
    },
    checkboxChange(value, record) {
      if (value.length > 0) {
        // this.model.machineCleanInfoDtoList.push(record)
        record.isClean = true
      } else {
        record.isClean = false
        // let index = this.model.machineCleanInfoDtoList.findIndex(item => item.machineLocationName == record.machineLocationName)
        // this.model.machineCleanInfoDtoList.splice(index, 1)
      }
    },
    gotoQuery() {
      if(!this.model.machineName) {
        this.$Toast('请先填写编码！')
        return
      }
      uni.navigateTo({
        url: `/pages/EquipmentManagement/EquipmentCleaning/modules/EquipmentCleaningList?machineName=${this.model.machineName}`,
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.COAT02.01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
