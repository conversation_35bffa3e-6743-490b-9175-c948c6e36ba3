<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="托盘号" borderBottom required labelWidth="150">
          <u--input v-model="model.container" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('container')"></view>
        </u-form-item>

        <u-form-item label="条码号" borderBottom required labelWidth="150">
          <u--input v-model="model.serialno1" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('serialno1')"></view>
        </u-form-item>

        <u-form-item label="条码类型"  required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('location1')">
            <view v-if="model.location1">{{ $utils.filterObjLabel(dicts.packCellLactionList, model.location1) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'location1' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
        <!-- 
        <u-form-item label="电芯2" borderBottom required labelWidth="150">
          <u--input v-model="model.serialno2" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('serialno2')"></view>
        </u-form-item>

        <u-form-item label="电芯位置2" required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('location2')">
            <view v-if="model.location2">{{ $utils.filterObjLabel(dicts.packCellLactionList, model.location2) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'location2' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item> -->
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>

import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, PrintPackageMixin],
  components: {
  },
  data() {
    this.changetrayNo = this.$debounce(this.changetrayNo, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      columns: [],
      select: false,
      selectType: '',
      rulesTip: {
        container: '托盘号不能为空',
        serialno1: '条码号不能为空',
        location1: '条码类型不能为空',
        // location1: '电芯位置1不能为空',
        // location2: '电芯位置2不能为空'
      },
      trayNoParmas: {},
      model: {},
      serialnoList: [
        { serialno: '', location: '', },
        { serialno: '', location: '', }
      ],

      dicts: {
        packCellLactionList: [
          { label: '电池包', value: 'p' },
          { label: '模组', value: 'm' },
        ],  //电芯位置
        // packContainUnlockUrlList: [],  //url
      },

    }
  },
  computed: {
  },
  watch: {
    'model.trayNo': {
      handler(val) {
        this.changetrayNo(val)
      }
    },

  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    // this.getEnumValue('packContainUnlockUrl', 'packContainUnlockUrlList') // 退库类型
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text,
          ...item,
        }))
      })
    },

    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'location1':
          this.columns = this.dicts.packCellLactionList
          break;
        case 'location2':
          this.columns = this.dicts.packCellLactionList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false

    },
    initModel() {
      this.model = {
        container: '',
        serialno1: '',
        location1: '',
        // serialno2: '',
        // location2: '',
      }
    },
    getStockCodeList() {
      this.$service.common.getDictByQueryId('GetStockCodeList').then(res => {
        this.dicts.stockCodeList = res.datas.map(item => ({
          label: item.stockName,
          value: item.stockCode,
        }))
      })
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        // container: this.model.container,
        // serialnoList: [
        //   { serialno: this.model.serialno1, location: this.model.location1 },
        //   { serialno: this.model.serialno2, location: this.model.location2 }
        // ]
        palletCode: this.model.container,
        code: this.model.serialno1,
        type: this.model.location1,
      }
      // let url = this.dicts.packContainUnlockUrlList && this.dicts.packContainUnlockUrlList[0].constanValue
      // if (!url) {
      //   return this.$Toast('请先配置请求地址!')
      // }
      uni.showLoading({
        title: '加载中...',
        mask: true
      });
      uni.request({
        url: 'http://************:20010/api/result',
        data: params,
        method: 'POST',
        success: (res) => {
          uni.hideLoading()
            if (res.code == 0) {
              this.$Toast('操作成功')
              this.model.container = ''
              this.model.serialno1 = ''
            }
          else {
            uni.showToast({
              title: res.message,
              icon: 'none',
              duration: 6000,
            })
          }
        },
        fail: (err) => {
          uni.hideLoading()
          let msg = err.errMsg;
          if (msg.includes('request:fail')) {
            msg = '请求失败,请检查网络或服务器'
          }
          this.$Toast(msg)
        }
      })
    },

    async changetrayNo(value) {
      if (!value) return

    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
