<template>
  <view class="myContainerPage pl5 pr5 pb5">
    <view class="myContainer">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="识别条码" borderBottom required labelWidth="100">
          <u--input v-model="model.lotName" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>
        <u-form-item label="物料编码" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productSpecName }}
          </view>
        </u-form-item>

        <u-form-item label="物料名称" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productSpecDesc }}
          </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productOrderName }}
          </view>
        </u-form-item>

        <u-form-item label="打印模板" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('printTemplateName')">
            <view v-if="model.printTemplateName">{{ $utils.filterObjLabel(dicts.printTemplateNameList, model.printTemplateName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'printTemplateName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="打印机" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('printMachineName')">
            <view v-if="model.printMachineName">{{ $utils.filterObjLabel(dicts.printMachineNameList, model.printMachineName) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'printMachineName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="打印数量" labelWidth="100">
          <u--input v-model="model.printNumber" @input="inputChange($event, 'printNumber')" number border="none"></u--input>
        </u-form-item>
        <!-- <u-form-item label="打印数量" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('printNumber')">
            <view v-if="model.printNumber">{{ $utils.filterObjLabel(dicts.printNumberList, model.printNumber) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'printNumber' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item> -->
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import _ from "lodash";
export default {
  name: 'binding',
  mixins: [],
  props: {
    nlsMap: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    this.changelotName = this.$debounce(this.changelotName, 1000)
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      // nlsMap: {

      // },
      rulesTip: {
        lotName: '识别条码不能为空',
        printTemplateName: '打印模板不能为空',
        printMachineName: '打印机不能为空',
      },

      model: {},
      columns: [],
      select: false,
      selectType: '',
      dicts: {
        printTemplateNameList: [], // 打印模板
        printMachineNameList: [], // 打印机
        printNumberList: [
          { label: '1', value: '1' },
          { label: '2', value: '2' },
          { label: '3', value: '3' },
        ],
      },
    };
  },
  watch: {
    'model.lotName': {
      handler(val) {
        this.changelotName(val)
      }
    },
  },
  created() {
    this.initModel()
    this.GetPrintMachine()
    this.GetPrintTemplate()
  },
  methods: {
    inputChange(e, type) {
      // e = e && (e.match(/^\d*(\.?\d{0,2})/g)[0])
      e = Math.floor(e)
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },
    initModel() {
      this.model = {
        lotName: '', // 识别条码
        productSpecName: '', // 物料编码
        productSpecDesc: '', // 物料名称
        productOrderName: '', // 生产工单
        productSpecName: '', // 物料编码
        printMachineName: '', // 打印机
        printTemplateName: '', // 打印模板
        printNumber: '1', // 打印数量
      }
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'printTemplateName':
          this.columns = this.dicts.printTemplateNameList
          break;
        case 'printMachineName':
          this.columns = this.dicts.printMachineNameList
          break;
        case 'printNumber':
          this.columns = this.dicts.printNumberList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },


    async changelotName(value) {
      if (!value) return
      try {
        let params = {
          lotName: value,
        }
        let res = await this.$service.Labeling.GetLotInfo(params)
        if (res.datas.length > 0) {
          this.model.productSpecName = res.datas[0].productSpecName
          this.model.productSpecDesc = res.datas[0].productSpecDesc
          this.model.productOrderName = res.datas[0].productOrderName
        }
      } catch (error) {
        this.model.lotName = null
        this.model.productSpecName = ''
        this.model.productSpecDesc = ''
        this.model.productOrderName = ''
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      uni.showModal({
        title: '提示',
        content: `是否确定提交打印？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.handleSumbit()
          }
          if (res.cancel) { }
        },
      })
    },
    async handleSumbit() {
      try {
        let params = {
          ...this.model,
          userId: this.$getLocal(USER_ID)
        }
        let res = await this.$service.Labeling.printLabel(params)
        this.$Toast('操作成功')
        // let res = {
        //   datas: [
        //     {
        //       lotName: '123456789',
        //       ip: '***********',
        //       port: '9100',
        //       zplCommand: '^XA^GFA,10000,10000,100,:Z64:eNrtvP9/8FwUgLf/8/+/+/+/+/+'
        //     }
        //   ]
        // }
        if (res.datas.length > 0) {
          let { printMachineName, ip, port, zplCommand, printData } = res.datas[0]
          let parasmArray = []
          for (const key in printData) {
            const element = printData[key];
            parasmArray.push({
              type: "",
              name: key,
              value: element,
              required: false
            })
          }
          let option = {
            ip: ip,
            port: port,
            printName: printMachineName,
            ReportName: zplCommand,
            printNumber: this.model.printNumber,
            priParameterntKey: JSON.stringify(parasmArray),
            // priParameterntKey: [
            //   { type: "", name: "lotName", value: this.model.lotName || '', required: false },
            // ]
          }
          uni.showLoading({
            title: '打印中...',
            mask: true
          });
          let promises = []
          // for (let i = 0; i < 1; i++) {
          // 每次请求之间等待 i 秒 避免打印速度过快
          // await new Promise(resolve => setTimeout(resolve, 1000 * i));
          // 发送请求
          const result = await this.printPackage(option);
          promises.push(result)
          // }
          let c = await Promise.all(promises);
          // 全部打印执行完成
          uni.hideLoading()
          this.$Toast('打印成功')
          this.initModel()
        } else {
          this.$Toast('无法查询打印信息!')
        }
      } catch (error) {
        uni.hideLoading()
        this.$Toast('无法查询打印信息!')
        console.log('error', error);
      }
    },

    printPackage(options = {}) {
      // let p = new Promise((res, rej) => {
      //   setTimeout(() => {
      //     let a = Math.floor(Math.random() * 10 + 1)
      //     if (a > 5) {
      //       res('res')
      //     } else {
      //       rej('rej')
      //     }
      //   }, 1000);
      // })
      // return p
      let promise = new Promise((resolved, rejected) => {
        let obj = {
          ReportType: "gridreport",     /*报表类型 gridreport fastreport reportmachine 为空 将默认为gridreport  */
          // ReportName: "fenQie.grf",     /*报表文件名 条形码 */
          ReportName: options.ReportName,     /*报表文件名 条形码 */
          PrinterName: `${options.printName}`,      /*可选。指定打印机，为空的话 使用默认打印机, 请在 控制面板 -> 设备和打印机 中查看您的打印机的名称 */
          Copies: options.printNumber, //份数
          Parameter: JSON.stringify(options.priParameterntKey),
        }
        if (!options.ip || !options.port) {
          this.$Toast('请检查打印机ip和端口')
          return
        }
        let printerUrl = `http://${options.ip}:${options.port}/printreport`
        uni.request({
          url: printerUrl,
          data: obj,
          header: {
            'content-type': 'application/x-www-form-urlencoded', //自定义请求头信息
          },
          method: 'POST',
          success: (res) => {
            if (res.statusCode == 200) {
              resolved(res)
            } else {
              rejected(res)
            }
          },
          fail: (err) => {
            rejected(err);
          }
        })
      })
      return promise
    },
    initSearchModel() {
      this.searchModel = {
        operateNo: '10001',
        trayName: null,
        pageNo: this.pageNumber,
        limit: 99999,
      }
    },
    GetPrintMachine() {
      this.$service.common.getDictByQueryId('Common_C_GetPrintMachine').then(res => {
        this.dicts.printMachineNameList = res.datas.map(item => ({
          label: item.description,
          value: item.printMachineName,
        }))
      })
    },
    GetPrintTemplate() {
      this.$service.common.getDictByQueryId('Common_C_GetPrintTemplate').then(res => {
        this.dicts.printTemplateNameList = res.datas.map(item => ({
          label: item.description,
          value: item.printTemplateName,
        }))
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'lotName':
          this.model.lotName = 'C20240730164218527'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.formModel, key, res.result)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.myContainerPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;
  .myContainer {
    flex: 1;
    overflow-y: scroll;
  }
}

// .listPage {
//   display: flex;
//   flex-direction: column;
//   overflow: hidden;
//   width: 100vw;
//   height: 100%;
//   .topContainer {
//     flex-shrink: 0;
//   }

//   .listContainer {
//     flex: 1;
//     display: flex;
//     flex-direction: column;
//     overflow: hidden;
//     margin-top: 20rpx;
//     .table_header {
//       flex-shrink: 0;
//     }
//     .table_content {
//       flex: 1;
//       overflow-y: scroll;
//     }
//   }
// }
</style>