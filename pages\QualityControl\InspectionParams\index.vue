<template>
  <view class="bc_fff myContainerPage">
    <u-navbar title="巡检参数录入" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"></u-navbar>
    <view class="myContainer ma5">
      <u--form class="ml10 mr10" labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="巡检任务编码:" required labelWidth="120">
          <view class="w100x flex right" @click="selectReasonCodeType('sjrwbm')">
            <view>{{ model.sjrwbm }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>
        <u-form-item label="设备号:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.sbh }}
          </view>
        </u-form-item>
        <u-form-item label="工序:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.gx }}
          </view>
        </u-form-item>
        <u-form-item label="样品条码:" required labelWidth="120">
          <!-- <view class="w100x flex right" @click="selectReasonCodeType('yptm')">
            <view>{{ model.yptm }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view> -->
          <view class="w100x flex right">
            {{ model.yptm }}
          </view>
        </u-form-item>
        <u-form-item label="工单号:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.gdh }}
          </view>
        </u-form-item>
        <u-form-item label="产品编码:" labelWidth="100">
          <view class="w100x flex right">
            {{ model.cpbm }}
          </view>
        </u-form-item>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">参数录入明细</view>
      <view class="listContainer">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1 pa2">参数编码</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">参数名称</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验结果值</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">检验结果</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50"></view>
        </view>
        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
            <view v-for="(item, index) in list" :key="index">
              <view class="flex bl_e1e1e1 mh40">
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 pa2 txt_c">{{ item.itemNo }}</view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.itemName }}</view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                  <!-- {{ item.productOrderName }} -->
                  <view v-if="item.paramValueType == 'Boolean' || item.paramValueType == 'String'" class="w100x flex around" @click="selectReasonCodeType('jgz', index)">
                    <view>{{ item.jgz }}</view>
                    <u-icon name="arrow-down" color="black" size="18"></u-icon>
                  </view>
                  <view v-else class="w100x flex right">
                    <view class="w100x flex right">
                      <view class="flex w100x hcenter pl10">
                        <u--input v-model="item.jgz" border="none" placeholder="请输入" @change="(val) => jgzChange(val, item, index)"></u--input>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ getResultText(item, index) }}</view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">
                  <u-button v-if="item.paramValueType != 'Boolean' && item.paramValueType != 'String'" type="success" text="新增" @click="addAction(item, index)" style="height: 30px; margin: 0 5px"></u-button>
                </view>
              </view>
              <view v-for="(item2, index2) in item.children" :key="index2 + 999" class="flex bl_e1e1e1 mh40">
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"></view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 pa2 txt_c"></view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1"></view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                  <view class="w100x flex right">
                    <view class="w100x flex right">
                      <view class="flex w100x hcenter pl10">
                        <u--input v-model="item2.jgz" border="none" placeholder="请输入"></u--input>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">
                  {{ getResultText2(item, item2, index) }}
                </view>
                <view class="fs14 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"></view>
              </view>
            </view>
            <view class="pt100" v-if="!list || list.length === 0">
              <u-empty mode="data"></u-empty>
            </view>
            <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
          </scroll-view>
        </view>
        <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      </view>
    </view>
    <view class="btnContainer" @click="submit">保存</view>
  </view>
</template>
<script>
import _ from "lodash";
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    // 'model.yptm': {
    //   handler(val) {
    //     // 获取已填写的参数项
    //     this.GetInspectiontemplatedetails(val)
    //   }
    // },
  },
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
      },
      model: {
        sjrwbm: '',
        sbh: '',
        gx: '',
        yptm: '',
        gdh: '',
        cpbm: '',
      },
      simpleTrackProduct: [],
      columns: [
        []
      ],
      select: false,
      GetReasonCodeTypeList: [],
      GetLotNameList: [],
      resultList: [
        {
          label: 'Y',
          value: 'Y',
        },
        {
          label: 'N',
          value: 'N',
        }
      ],
      chooseIndex: 0,
      // Inspectiontemplatedetails: [],
      list: [
        // {
        //   lotName: 'adsddasd',
        //   productOrderName: 'sad12312312'
        // }
      ],
      options: {},
    };
  },
  onLoad(e) {
    // let { taskNo } = options
    // this.model.sjrwbm = taskNo
    console.log('e',e);
    let options = e && e.params && JSON.parse(e.params)
    this.options = options
    if (options) {
      let { taskNo, machineName, processOperationName, lotName, workOrderName, productSpecName } = options
      this.model.sbh = machineName
      this.model.gx = processOperationName
      this.model.yptm = lotName
      this.model.gdh = workOrderName
      this.model.cpbm = productSpecName
      this.model.sjrwbm = taskNo
    }
    this.GetReasonCodeType()
    if (this.model.sjrwbm) {
      // 获取条码
      this.GetLotName()
      // 获取参数
      this.GetInspectiontemplatedetails()
    }
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('getInspectionTaskList')
    },
    addAction(item, index) {
      console.log('this.list', this.list, item, index);
      this.list[index].children.push({})
    },
    GetReasonCodeType() {
      const params = {
        inspectType: 'ProcessInspection',
      }
      this.$service.QualityControl.getTaskNoList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            ...item,
            label: item.taskNo,
            value: item.taskNo
          }
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    GetLotName() {
      const params = {
        inspectType: 'ProcessInspection',
        taskNo: this.model.sjrwbm
      }
      this.$service.QualityControl.getLotNameList(params).then(res => {
        this.GetLotNameList = res.datas.map((item) => {
          return {
            ...item,
            label: item.lotName,
            value: item.lotName
          }
        })
        console.log('this.GetLotNameList', this.GetLotNameList);
      })
    },
    GetInspectiontemplatedetails(lotName) {
      let params = {
        inspectType: 'ProcessInspection',
        taskNo: this.model.sjrwbm
      }
      if (lotName) params.lotName = lotName
      this.$service.QualityControl.getInspectiontemplatedetails(params).then(res => {
        // this.list = res.datas.map((item) => {
        //   return {
        //     ...item,
        //     jgz: item.actualValue,
        //     label: item.lotName,
        //     value: item.lotName
        //   }
        // })
        this.list = res.datas.map((item) => {
          let arr = []
          let arr2 = []
          if (item.actualValue) {
            arr = item.actualValue.split(',')
            if (arr.length > 1) {
              arr2 = arr.splice(1)
              arr2.forEach((item2, index2) => {
                arr2[index2] = {
                  jgz: item2
                }
              })
            }
          }
          // if(arr.length > 1) {}
          return {
            ...item,
            // jgz: item.actualValue,
            jgz: arr[0],
            label: item.lotName,
            value: item.lotName,
            children: arr2
          }
        })
        console.log('this.list', this.list);
      })
    },
    selectReasonCodeType(type, index) {
      if (type == 'yptm') {
        this.columns[0] = [].concat(this.GetLotNameList)
      }
      if (type == 'sjrwbm') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      if (type == 'jgz') {
        this.columns[0] = [].concat(this.resultList)
        this.chooseIndex = index
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if (this.selectType == 'sjrwbm') {
        this.model.sjrwbm = e.value[0].label
        this.model.sbh = e.value[0].machineName
        this.model.gx = e.value[0].processOperationName
        this.model.yptm = e.value[0].lotName
        this.model.gdh = e.value[0].workOrderName
        this.model.cpbm = e.value[0].productSpecName
        // 获取条码
        this.GetLotName()
        // 获取参数
        this.GetInspectiontemplatedetails()
      }
      if (this.selectType == 'jgz') {
        this.$set(this.list[this.chooseIndex], 'jgz', e.value[0].label)
      }
      if (this.selectType == 'yptm') {
        this.model.yptm = e.value[0].label
        this.model.gdh = e.value[0].workOrderName,
          this.model.cpbm = e.value[0].productSpecName
        // this.model.productSpecName = e.value[0].productSpecName
        // 获取参数
        // this.GetInspectiontemplatedetails()
      }
      this.select = false
    },
    initModel() {
      this.model = {
        sjrwbm: '',
        sbh: '',
        gx: '',
        yptm: '',
        gdh: '',
        cpbm: '',
      }
    },
    focusEvent(type) {
      // this.form[type] = ''
    },
    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        inspectType: 'ProcessInspection',
        machineName: this.model.sbh
      }
      try {
        let res = await this.$service.QualityControl.getMachineDataForFirstInspection(params)
        console.log('getMachineDataForFirstInspection', res);
        if (res.datas.length > 0) {
          let data = res.datas[0]
          let changeFlag = false
          if (this.paramSbh != this.model.sbh) {
            // 和参数相同的设备号，不更改任务编码和工序
            changeFlag = true
          }
          this.model = {
            ...res.datas[0],
            sbh: this.model.sbh,
            sbms: data.description,
            gx: changeFlag ? '' : this.model.gx,
            sjrwbm: changeFlag ? '' : this.model.sjrwbm,
            rwcjfs: changeFlag ? '' : this.model.rwcjfs,
            rwzt: changeFlag ? '' : this.model.rwzt,
            yptm: '',
            gdh: '',
            cpbm: ''
          }
          this.GetReasonCodeTypeList = data.machineGroupName.split(',').map(item => {
            return {
              label: item,
              value: item
            }
          })
        } else {
          this.model.sbh = ''
          this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        inspectType: 'ProcessInspection',
        taskNo: this.model.taskNo
      }
      try {
        let res = await this.$service.QualityControl.getFirstLotData(params)
        console.log('getFirstLotData', res);
        if (res.datas.length > 0) {
          let data = res.datas[0]
          this.model = {
            ...this.model,
            yptm: data.lotName,
            gdh: data.productOrderName,
            cpbm: data.productSpecName,
          }
        } else {
          this.model.yptm = ''
          this.$Toast('样品不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.model.yptm = ''
        // this.initModel()
      }
    },
    submit(value, type) {
      if (!this.model.sjrwbm) {
        this.$Toast('请选择对应的巡检任务编码！')
        return
      }
      if (!this.model.yptm) {
        this.$Toast('请选择对应的样品条码')
        return
      }
      let flag = false
      let arr = this.list.map((item, index) => {
        if (!item.jgz) flag = true
        let result = item.jgz
        let singleResult = this.getResultText(item, index)
        if (item.children.length > 0) {
          item.children.forEach(item2 => {
            result += ',' + item2.jgz
            if (this.getResultText2(item, item2, index) == '不合格') {
              singleResult = '不合格'
            }
          })
        }
        return {
          ...item,
          actualValue: result,
          singleResult: singleResult
        }
      })
      // let arr = this.list.map((item, index)=> {
      //   if(!item.jgz) flag = true
      //   return {
      //     ...item,
      //     actualValue: item.jgz,
      //     singleResult: this.getResultText(item, index)
      //   }
      // })
      if (flag) {
        this.$Toast('检验结果值不能为空！')
        return
      }
      // 取样完成
      let params = {
        inspectType: 'ProcessInspection',
        machineName: this.model.sbh,
        processOperationName: this.model.gx,
        taskNo: this.model.sjrwbm,
        lotName: this.model.yptm,
        workOrderName: this.model.gdh,
        productSpecName: this.model.cpbm,
        inspectionTaskDetailList: arr
      }
      this.$service.QualityControl.paramInputSave(params).then((res) => {
        console.log('paramInputSave', res);
        this.$Toast('巡检参数录入保存成功！')
        if (_.isEmpty(this.options)) {
          setTimeout(() => {
            this.model.yptm = ''
            this.model.gdh = ''
            this.model.cpbm = ''
            this.list = []
            // 重新获取条码
            this.GetLotName()
            // 重新获取参数
            this.GetInspectiontemplatedetails()
          }, 500)
        } else {
          setTimeout(() => {
            this.leftClick()
          }, 1500);
        }
      })
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
    getResultText(item, index) {
      if (item.paramValueType == 'Boolean' || item.paramValueType == 'String') {
        if (item.jgz == 'Y') {
          return '合格'
        } else if (item.jgz == 'N') {
          return '不合格'
        } else {
          return '不合格'
        }
      } else if (item.paramValueType == 'Integer' || item.paramValueType == 'BigDecimal') {
        if (!item.jgz && String(item.jgz) != '0') {
          return '不合格'
        }
        let lowerLimit = item.lowerLimit
        let upperLimit = item.upperLimit
        let standardValue = item.standardValue
        if (item.jgz == standardValue || (lowerLimit <= item.jgz && item.jgz <= upperLimit)) {
          return '合格'
        } else {
          return '不合格'
        }
      } else {
        return '不合格'
      }
    },
    getResultText2(item, item2, index) {
      // if(item.paramValueType == 'Boolean' || item.paramValueType == 'String') {
      //   if(item.jgz == 'Y') {
      //     return '合格'
      //   } else if(item.jgz == 'N') {
      //     return '不合格'
      //   } else {
      //     return '不合格'
      //   }
      // } else if(item.paramValueType == 'Integer' || item.paramValueType == 'BigDecimal'){
      if (!item2.jgz && String(item2.jgz) != '0') {
        return '不合格'
      }
      let lowerLimit = item.lowerLimit
      let upperLimit = item.upperLimit
      let standardValue = item.standardValue
      if (item2.jgz == standardValue || (lowerLimit <= item2.jgz && item2.jgz <= upperLimit)) {
        return '合格'
      } else {
        return '不合格'
      }
      // } else{
      //   return '不合格'
      // }
    },
    jgzChange() {

    }
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
</style>