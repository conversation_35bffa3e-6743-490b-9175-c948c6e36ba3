<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="设备校准履历" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10 mb10">
      <view class="myContainer ml10 mr10 mb10">
        <u--form labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="设备号:" required labelWidth="100">
            <view class="w100x flex right">
              {{ machineName }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10">设备校准履历</view>
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="list.length > 0">
          <view v-for="(ele, index) in list" :key="index">
            <view class="flex between ma10 mb10 bb_eee hcenter">
              <view class="label_circle mr20">{{index + 1}}</view>
              <view class="flex1 mr10 pb10">
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">操作人:</view>
                  <view>{{ ele.eventUser }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">校准时间:</view>
                  <view>{{ ele.eventTime }}</view>
                </view>
                <!-- <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验进度:</view>
                  <view>{{ ele.lotStateDictText || '-' }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验结果:</view>
                  <view>{{ ele.inspectResult || '-' }}</view>
                </view>
                <view class="flex h40 hcenter c_999">
                  <view class="mr10 w100 txt_r c_000">检验完成时间:</view>
                  <view>{{ ele.inspectionFinishime || '-' }}</view>
                </view> -->
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
    <!-- <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker> -->
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    // 'form.machineName': {
    //   handler(val) {
    //     this.changeMachineName(val)
    //   }
    // },
  },
  data() {
    // this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      // form: {
      // },
      // taskNo: '',
      machineName: '',
      list: []
    };
  },
  computed: {
    // list: {
    //   get() {
    //     return this.$store.state.FinshSampleList;
    //   },
    //   set(value) {
    //     this.$store.commit('changeFinshSampleList', value);
    //   }
    // },
  },
  onLoad(options) {
    let { machineName } = options
    this.machineName = machineName
    this.getData()
  },
  methods: {
    getData() {
      let params = {
        eventName: 'MachineCalibration',
        machineName: this.machineName,
      }
      let query = {
        page: this.pageNumber,
        size: this.pageSize,
      }
      this.$service.EquipmentManagement.findAllHistWithEqualsOnPage(params, query).then((res) => {
        console.log('res', res);
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.list = res.datas[0].content
          } else {
            this.list = []
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
  .label_circle {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    border: 1px solid #000;
    text-align: center;
    line-height: 70rpx;
    font-size: 24px;
  }
}
</style>