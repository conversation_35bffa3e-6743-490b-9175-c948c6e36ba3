<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="报废分类记录" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input readonly v-model="model.machineName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="产品描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="170">
          <view class="w100x flex right"> {{ model.poleRollName }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.poleRollProductOrderName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.mountQuantity }} </view>
        </u-form-item>

        <u-form-item label="上卷作业员" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.mountUser }} </view>
        </u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.mountTime }} </view>
        </u-form-item>

        <u-form-item label="已消耗数量(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.consumedQuantity }} </view>
        </u-form-item>

        <u-form-item label="不良计数器(片)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.ngQuantity }} </view>
        </u-form-item>

        <u-form-item label="人工确认(片)" borderBottom required labelWidth="130">
          <u--input placeholder="请输入数量(片)" v-model="model.uesrConfirm" border="none"></u--input>
        </u-form-item>

        <u-form-item label="报废分类记录(片)" borderBottom required labelWidth="150">
          <!-- <view class="w100x flex right"> {{ model.scrapQuantity }} </view> -->
          <u--input placeholder="请输入" v-model="model.scrapQuantity" border="none"></u--input>
        </u-form-item>

        <!-- <u-form-item label="代码类型" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType">
            <view>{{ model.reasonCodeType }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item> -->

        <u-form-item label="不合格代码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCode">
            <view>{{ model.reasonCode }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="数量(片)" borderBottom required labelWidth="130">
          <u--input type="number" placeholder="请输入数量(片)" v-model="model.quantity" border="none"></u--input>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">保存</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        reasonCode: '不合格代码不能为空',
        // ngQuantity: '数量不能为空',
      },
      model: {},
      columns: [],
      GetReasonCodeTypeList: [],
      GetReasonCodeList: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad(e) {
    this.initModel()
    this.model.machineName = e && e.machineName
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/Polar/modules/Upgradeable`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备
        consumedQuantity: null, //已消耗数量	integer(int32)	
        description: null, //	设备描述	string	
        eventUser: null, //		string	
        factoryName: null, //	string	
        messageName: null, //	string	
        messagePath: null, //	string	
        mountQuantity: null, //上卷数量	integer(int32)	
        mountTime: null, //上卷时间	string(date-time)	
        mountUser: null, //	上卷操作员	string	
        ngQuantity: null, //	不良计数	integer(int32)	
        poleRollName: null, //极卷名	string	
        poleRollProductOrderName: null, //	极卷工单	string	
        processOperationName: null, //	工序名	string	
        productSpecDesc: null, //	产品描述	string	
        productSpecName: null, //产品编码	string	
        remainingQuantity: null, //	剩余数量	integer(int32)	
        returnCode: null, //	string	
        returnMessage: null, //	string	
        scrapQuantity: null, //	报废分类记录	integer(int32)	
        transactionId: null, //	string	
        uesrConfirm: null, //	人工确认
        quantity: null
      }
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.saveNo = false
      this.select = true
    },
    selectFirm(e) {
      if (e.value[0].reasonCodeType) {
        this.model.reasonCodeType = e.value[0].reasonCodeType
        this.GetReasonCode(this.model.reasonCodeType)
      } else {
        this.model.reasonCode = e.value[0].reasonCode
      }
      this.select = false
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.DieCutting.insert(params).then(res => {
        this.$Toast('保存成功！')
        this.model.reasonCodeType = ''
        this.model.reasonCode = ''
        this.model.quantity = ''
      })
    },

    /* 设备号 */
    changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      this.$service.DieCutting.getFinishDataByMachineName(params).then(res => {
        this.model = res.datas[0]
        this.GetReasonCode(this.model.processOperationName)
      })
    },
    GetReasonCodeType() {
      const params = {
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas
        this.GetReasonCodeTypeList.forEach(item => {
          item.labelText = item.reasonCodeType + '/' + item.typeDesc
        })
      })
    },
    GetReasonCode(reasonCodeType) {
      const params = {
        processOperationName: reasonCodeType
      }
      this.$service.DieCutting.GetReasonCode(params).then(res => {
        this.GetReasonCodeList = res.datas
        this.GetReasonCodeList.forEach(item => {
          // item.labelText = item.reasonCode + '/' + item.codeDesc
          item.labelText = item.reasonCode + '/' + item.description
        })
      })
    },
    selectReasonCodeType() {
      this.columns = this.GetReasonCodeTypeList
      this.select = true
    },
    selectReasonCode() {
      this.columns = this.GetReasonCodeList
      this.select = true
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/uform.scss';
@import '../../../../styles/publicStyle.scss';
</style>
