<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="分切产出确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" class="flex_shrink0"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="130">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入设备编号" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom>
          <u--input readonly v-model="model.palletDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="分切卷数" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productionqty }}</view>
        </u-form-item>

        <u-form-item label="生产阶段" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="select = true">
            <text class="nowrap">{{ productionstageShow(model.productionStage) }}</text>
            <view :style="{ transform: select ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="第一卷产出(设备)" borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.device_productQuantity1" border="none" type="number" readonly></u--input>m</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.device_lotWeight1" border="none" type="number" readonly></u--input>ea</view>
          </view>
        </u-form-item>
        <u-form-item label="第一卷产出(人工)" required borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.productQuantity1" border="bottom" type="number" @input="inputChange($event, 'productQuantity1')"></u--input>{{ model.productQuantityUnit1 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.lotWeight1" border="bottom" type="number" @input="intChange($event,'lotWeight1')" :readonly="ratioFlag"></u--input>{{ model.lotWeightUnit1 }}</view>
          </view>
        </u-form-item>

        <u-form-item label="第一卷亏损" required borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.lossProductQuantity1" border="bottom" type="number" @input="inputChange($event, 'lossProductQuantity1')"></u--input>{{ model.lossProductQuantityUnit1 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.lossLotWeight1" border="bottom" type="number" @input="intChange($event,'lossLotWeight1')" :readonly="ratioFlag"></u--input>{{ model.lossLotWeightUnit1 }}</view>
          </view>
        </u-form-item>

        <u-form-item label="第一卷待判定" required borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.waitjudgmentproductqty1" border="bottom" type="number" @input="inputChange($event, 'waitjudgmentproductqty1')"></u--input>{{ model.waitjudgmentproductunit1 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.waitjudgmentlotweight1" border="bottom" type="number" @input="intChange($event,'waitjudgmentlotweight1')"  :readonly="ratioFlag"></u--input>{{ model.waitjudgmentlotweightunit1 }}</view>
          </view>
        </u-form-item>

        <u-form-item label="第二卷产出(设备)" borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.device_productQuantity2" border="none" type="number" readonly></u--input>m</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.device_lotWeight2" border="none" type="number" readonly></u--input>ea</view>
          </view>
        </u-form-item>

        <u-form-item label="第二卷产出(人工)" required borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.productQuantity2" border="bottom" type="number" @input="inputChange($event, 'productQuantity2')"></u--input>{{ model.productQuantityUnit2 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.lotWeight2" border="bottom" type="number" @input="intChange($event,'lotWeight2')"  :readonly="ratioFlag"></u--input>{{ model.lotWeightUnit2 }}</view>
          </view>
        </u-form-item>

        <u-form-item label="第二卷亏损" required borderBottom labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.lossProductQuantity2" border="bottom" type="number" @input="inputChange($event, 'lossProductQuantity2')"></u--input>{{ model.lossProductQuantityUnit2 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.lossLotWeight2" border="bottom" type="number" @input="intChange($event,'lossLotWeight2')"  :readonly="ratioFlag"></u--input>{{ model.lossLotWeightUnit2 }}</view>
          </view>
        </u-form-item>

        <u-form-item label="第二卷待判定" required labelWidth="140">
          <view class="flex">
            <view class="flex hcenter flex1"> <u--input v-model="model.waitjudgmentproductqty2" border="bottom" type="number" @input="inputChange($event, 'waitjudgmentproductqty2')"></u--input>{{ model.waitjudgmentproductunit2 }}</view>
            <view class="flex hcenter flex1 ml10"> <u--input v-model="model.waitjudgmentlotweight2" border="bottom" type="number" @input="intChange($event,'waitjudgmentlotweight2')"  :readonly="ratioFlag"></u--input>{{ model.waitjudgmentlotweightunit2 }}</view>
          </view>
        </u-form-item>
      </u--form>
      <view class="lin40 dib fs16 pl20" style="color: #409eff" @click="onSkip">极卷上卷信息</view>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="description" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer2">
      <view @click="readData">读取设备数据</view>
      <view @click="submit">确认</view>
    </view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID, LOCALHOST_PRINT } from '@/utils/common/evtName.js'
export default {
  name: 'rollerConfirmIndex',
  mixins: [RedScan],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productQuantity1: '第一卷产出数量不能为空',
        lotWeight1: '第一卷产出电芯计量数量不能为空',
        lossProductQuantity1: '第一卷亏损数量不能为空',
        lossLotWeight1: '第一卷亏损电芯计量数量不能为空',
        waitjudgmentproductqty1: '第一卷判定数量不能为空',
        waitjudgmentlotweight1: '第一卷判定电芯计量数量不能为空',

        productQuantity2: '第二卷产出数量不能为空',
        lotWeight2: '第二卷产出电芯计量数量不能为空',
        lossProductQuantity2: '第二卷亏损数量不能为空',
        lossLotWeight2: '第二卷亏损电芯计量数量不能为空',
        waitjudgmentproductqty2: '第二卷判定数量不能为空',
        waitjudgmentlotweight2: '第二卷判定电芯计量数量不能为空',
      },
      model: {},
      columns: [],
      select: false,
      machineNameFlag: false,

      ratio: null,  // 单位转换
      ratioFlag: false,
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  computed: {
    productionstageShow() {
      return function (val) {
        let a = this.columns.find(item => item.enumValue === val)
        return a && a.description || ''
      }
    }
  },
  onLoad() {
    this.initModel()
    this.listEnum()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        palletDesc: null, // 极性
        palletName: null, // 极性C
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述,
        productionqty: '2', // 卷数
        productionStage: 'M', //生产阶段
        lineSideWareHouse: null, // 线边仓地址

        productQuantity1: null,
        lotWeight1: null,
        device_productQuantity1: null, // 设备生产数量 
        device_lotWeight1: null, // 设备电芯计量数量 
        productQuantityUnit1: 'm',
        lotWeightUnit1: 'ea',
        lossProductQuantity1: null,
        lossLotWeight1: null,
        lossProductQuantityUnit1: 'm',
        lossLotWeightUnit1: 'ea',
        waitjudgmentproductqty1: null,
        waitjudgmentlotweight1: null,
        waitjudgmentproductunit1: 'm',
        waitjudgmentlotweightunit1: 'ea',

        productQuantity2: null,
        lotWeight2: null,
        device_productQuantity2: null, // 设备生产数量 
        device_lotWeight2: null, // 设备电芯计量数量 
        productQuantityUnit2: 'm',
        lotWeightUnit2: 'ea',
        lossProductQuantity2: null,
        lossLotWeight2: null,
        lossProductQuantityUnit2: 'm',
        lossLotWeightUnit2: 'ea',
        waitjudgmentproductqty2: null,
        waitjudgmentlotweight2: null,
        waitjudgmentproductunit2: 'm',
        waitjudgmentlotweightunit2: 'ea',
      }
      this.machineNameFlag = false
    },
    listEnum() {
      let params = {
        "enumName": "ProductionStage"
      }
      this.$service.cutting.listEnum(params).then(res => {
        this.columns = res.data
      })
    },
    selectFirm(e) {
      this.$set(this.model, 'productionStage', e.value[0].enumValue)
      this.select = false
    },
    onSkip() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: `/pages/cellFront/cuttingConfirm/detail?machineName=${this.model.machineName}`
      })
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.model.machineDesc = null
      this.model.palletDesc = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null
      this.model.lineSideWareHouse = null
      this.model.productRequestName = null
      this.trackOutProductRequest = null

      this.model.device_productQuantity1 = null
      this.model.device_lotWeight1 = null
      this.model.device_productQuantity2 = null
      this.model.device_lotWeight2 = null


      this.model.productQuantity1 = null
      this.model.productQuantity2 = null

      this.model.lotWeight1 = null
      this.model.lotWeight2 = null

      this.model.lossProductQuantity1 = null
      this.model.lossProductQuantity2 = null
      this.model.lossLotWeight1 = null
      this.model.lossLotWeight2 = null

      this.model.waitjudgmentproductqty1 = null
      this.model.waitjudgmentproductqty2 = null
      this.model.waitjudgmentlotweight1 = null
      this.model.waitjudgmentlotweight2 = null

      this.ratio = null
      this.ratioFlag = false

      let params = {
        operateNo: '40', // 后台区分固定字段
        machineName: value,
      }
      try {
        let res = await this.$service.roller.trackOut(params)
        this.machineNameFlag = true
        this.model.machineName = res.data.machineName
        this.model.machineDesc = res.data.machineDesc
        this.model.palletDesc = res.data.palletDesc
        this.model.processOperationName = res.data.processOperationName
        this.model.processOperationDesc = res.data.processOperationDesc
        this.model.areaName = res.data.areaName
        this.model.areaDesc = res.data.areaDesc

        this.model.lineSideWareHouse = res.data.lineSideWareHouse
        this.trackOutProductRequest = res.data.trackOutProductRequest

        this.ratio = res.data.unitconversionDto && res.data.unitconversionDto.ratio
        this.ratioFlag = res.data.unitconversionDto && !!res.data.unitconversionDto.ratio || false
      } catch (error) {
        this.model.machineName = null
      }
    },
    dataHandle(orginData, ratioData) {
      if (this.model[orginData] || this.model[orginData] == '0') {
        this.model[ratioData] = parseInt(this.$utils.calculate_mul(this.model[orginData], this.ratio))
      } else {
        this.model[ratioData] = null
      }
    },
    intChange(e, type) {
      if(e) {
        let str = e &&  e + ""
        let result = str && (str.match(/^\d*/g)[0])
        this.$nextTick(() => {
          this.$set(this.model, type, result)
        })
      }
    },
    inputChange(e, type) {
      e = e && (e.match(/^\d*(\.?\d{0,2})/g)[0])
      if (this.ratioFlag) {
        if (type === 'productQuantity1') {
          this.dataHandle('productQuantity1', 'lotWeight1')
        }
        if (type === 'lossProductQuantity1') {
          this.dataHandle('lossProductQuantity1', 'lossLotWeight1')
        }
        if (type === 'waitjudgmentproductqty1') {
          this.dataHandle('waitjudgmentproductqty1', 'waitjudgmentlotweight1')
        }
        if (type === 'productQuantity2') {
          this.dataHandle('productQuantity2', 'lotWeight2')
        }
        if (type === 'lossProductQuantity2') {
          this.dataHandle('lossProductQuantity2', 'lossLotWeight2')
        }
        if (type === 'waitjudgmentproductqty2') {
          this.dataHandle('waitjudgmentproductqty2', 'waitjudgmentlotweight2')
        }
      }
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },
    async submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != '0') {
          this.$Toast(this.rulesTip[key])
          return
        }
      }

      if (!this.model.lineSideWareHouse) {
        this.$Toast(`该产线[${this.model.areaDesc}]没有维护线边仓，请联系管理员配置`)
        return
      }
      if (this.checkMachineOutput) {
        if (Number(this.model.device_productQuantity1) <= 0) {
          this.$Toast(`第一卷未采集到设备[${this.model.machineName}]的产出数量`)
          return
        }
        if (Number(this.model.device_productQuantity2) <= 0) {
          this.$Toast(`第二卷未采集到设备[${this.model.machineName}]的产出数量`)
          return
        }
      }

      let randomNum = '';
      for (var i = 0; i < 3; i++) {
        randomNum += Math.floor(Math.random() * 10);
      }
      let time = new Date().getTime()
      let printTimeStr = this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
      let timeKey = `${this.model.machineName}_${time}_${randomNum}`
      let dto = {
        actionId: 'TrackOutSlitting', // 消息名称
        menuId: 'TrackOutSlitting', // 消息名称
        requestId: timeKey, // 时间戳
        reqMap: {
          userId: this.$getLocal(USER_ID), // 登陆人
          DS_TIBCO_REPLY: [],
          // 暂定写死
          DS_TIBCO_MAIN: [
            {
              DEST_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_VALUE: 'GET_RV_XMLDATA',
              MAX_TIME: '60',
            },
          ],
          langCd: '',
          DS_TIBCO_MSG: [
            {
              COMMAND_ID: 'TrackOutSlitting', // 消息名称
              FILE_ID: 'TrackOutSlitting', // 消息名称
              MESSAGE_NAME: 'TrackOutSlitting', // 消息名称
              TRANSACTION_ID: timeKey, // 时间戳
              EVENT_USER: this.$getLocal(USER_ID), // 登陆人
              EVENT_COMMENT: '', // 功能名称
              REPLYSUBJECT_NAME: 'TrackOutSlitting', // 界面名称
              TERMINAL: 'PDA', // PDA请求，WEB请求
              // FACTORYNAME:'',

              MACHINENAME: this.model.machineName,
              POLARITYTYPE: this.model.palletName,
              PROCESSOPERATIONNAME: this.model.processOperationName,
              PRODUCTREQUESTNAME: this.trackOutProductRequest.productRequestName,
              PRODUCTSPECNAME: this.trackOutProductRequest.productSpecName,
              LOTNAME: this.trackOutProductRequest.lotName,

              PRODUCTIONQTY: this.model.productionqty,
              PRODUCTIONSTAGE: this.model.productionStage,
            },
          ],
          DS_TIBCO_REPEAT: [
            {
              PRODUCTNO: 1,
              PRODUCTQUANTITY: this.model.productQuantity1,
              PRODUCTQUANTITYUNIT: this.model.productQuantityUnit1,
              LOTWEIGHT: this.model.lotWeight1,
              LOTWEIGHTUNIT: this.model.lotWeightUnit1,
              LOSSPRODUCTQUANTITY: this.model.lossProductQuantity1,
              LOSSPRODUCTQUANTITYUNIT: this.model.lossProductQuantityUnit1,
              LOSSLOTWEIGHT: this.model.lossLotWeight1,
              LOSSLOTWEIGHTUNIT: this.model.lossLotWeightUnit1,

              WAITJUDGMENTPRODUCTQTY: this.model.waitjudgmentproductqty1,
              WAITJUDGMENTPRODUCTUNIT: this.model.waitjudgmentproductunit1,
              WAITJUDGMENTLOTWEIGHT: this.model.waitjudgmentlotweight1,
              WAITJUDGMENTLOTWEIGHTUNIT: this.model.waitjudgmentlotweightunit1,
            },
            {
              PRODUCTNO: 2,
              PRODUCTQUANTITY: this.model.productQuantity2,
              PRODUCTQUANTITYUNIT: this.model.productQuantityUnit2,
              LOTWEIGHT: this.model.lotWeight2,
              LOTWEIGHTUNIT: this.model.lotWeightUnit2,
              LOSSPRODUCTQUANTITY: this.model.lossProductQuantity2,
              LOSSPRODUCTQUANTITYUNIT: this.model.lossProductQuantityUnit2,
              LOSSLOTWEIGHT: this.model.lossLotWeight2,
              LOSSLOTWEIGHTUNIT: this.model.lossLotWeightUnit2,
              WAITJUDGMENTPRODUCTQTY: this.model.waitjudgmentproductqty2,
              WAITJUDGMENTPRODUCTUNIT: this.model.waitjudgmentproductunit2,
              WAITJUDGMENTLOTWEIGHT: this.model.waitjudgmentlotweight2,
              WAITJUDGMENTLOTWEIGHTUNIT: this.model.waitjudgmentlotweightunit2,
            }
          ],
        },
      }

      try {
        let res = await this.$service.common.commonExec(dto)
        if (res.success) {
          this.$Toast('操作成功')
        }
        let params = {
          machineName: this.model.machineName,
          operateNo: '40',
          printTimeKey: timeKey,
          printTimeStr: printTimeStr,
          userId: this.$getLocal(USER_ID)
        }
        let pres = await this.$service.common.printLot(params, { showLoading: false })
        this.initModel()
        for (let i = 0; i < pres.data.basicLotLabelList.length; i++) {
          const ele = pres.data.basicLotLabelList[i]
          setTimeout(() => {
            let option = {
              ip: pres.data.aoiPrintIp,
              port: pres.data.aoiPrintPort,
              printName: pres.data.printName,
              priParameterntKey: [
                { type: "", name: "machineName", value: ele.machineName || '', required: false }, // 设备
                { type: "", name: "processOperationDesc", value: ele.processOperationDesc || '', required: false }, // 工序名称
                { type: "", name: "productRequestName", value: ele.productRequestName || '', required: false }, // 工单编号
                { type: "", name: "productSpecDesc", value: ele.productSpecDesc || '', required: false },// 型号
                { type: "", name: "originalLotName", value: ele.originalLotName || '', required: false }, // 批次 待确定
                { type: "", name: "lotName", value: ele.lotName || '', required: false }, // 卷号，二位码
                { type: "", name: "specification", value: ele.specification || '', required: false }, // '规格
                { type: "", name: "productQuantity", value: ele.productQuantity || '', required: false },
                { type: "", name: "productQuantityUnit", value: ele.productQuantityUnit || '', required: false },
                { type: "", name: "lotWeight", value: ele.lotWeight || '', required: false },
                { type: "", name: "lotWeightUnit", value: ele.lotWeightUnit || '', required: false },
                { type: "", name: "createUserValue", value: ele.createUserValue || '', required: false }, // 操作
                { type: "", name: "releaseUserValue", value: ele.releaseUserValue || '', required: false },// 确认
                { type: "", name: "releaseTime", value: ele.releaseTime || '', required: false },// 生产日期
                { type: "", name: "dueDate", value: ele.dueDate || '', required: false },// 有效期限
              ]
            }
            this.printPackage(option)
          }, 1000 * i)
        }
      } catch (error) {
        console.log('error', error);
      }
    },
    printPackage(options = {}) {
      let obj = {
        ReportType: "gridreport",     /*报表类型 gridreport fastreport reportmachine 为空 将默认为gridreport  */
        ReportName: "fenQie.grf",     /*报表文件名 条形码 */
        PrinterName: `${options.printName}`,      /*可选。指定打印机，为空的话 使用默认打印机, 请在 控制面板 -> 设备和打印机 中查看您的打印机的名称 */
        Parameter: JSON.stringify(options.priParameterntKey),
      }
      if (!options.ip || !options.port) {
        return
      }
      let printerUrl = `http://${options.ip}:${options.port}/printreport`
      if (LOCALHOST_PRINT) {
        printerUrl = `http://${LOCALHOST_PRINT}:${options.port}/printreport`
      }
      uni.request({
        url: printerUrl,
        data: obj,
        header: {
          'content-type': 'application/x-www-form-urlencoded', //自定义请求头信息
        },
        method: 'POST',
        success: (res) => { },
      })

    },
    scan(key) {
      // #ifdef H5
      this.model.machineName = 'C1Z001006'
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
    async readData() {
      if (!this.model.machineName) {
        this.$Toast('设备编号不能为空')
        return
      }
      if (!this.machineNameFlag) {
        return this.$Toast('请重新输入或扫描设备编号')
      }
      this.model.device_productQuantity1 = null
      this.model.device_lotWeight1 = null
      this.model.device_productQuantity2 = null
      this.model.device_lotWeight2 = null
      let params = {
        machineName: this.model.machineName,
        operateNo: '40',
      }
      let res = await this.$service.common.queryMachineOutput(params)
      if (res.data && res.data.length) {

        this.model.device_productQuantity1 = res.data[0].machineProductQuantity
        this.model.device_lotWeight1 = res.data[0].machineLotWeight
        this.model.productQuantity1 = res.data[0].machineProductQuantity
        this.model.lotWeight1 = res.data[0].machineLotWeight

        this.model.device_productQuantity2 = res.data[1].machineProductQuantity
        this.model.device_lotWeight2 = res.data[1].machineLotWeight
        this.model.productQuantity2 = res.data[1].machineProductQuantity
        this.model.lotWeight2 = res.data[1].machineLotWeight
      }
    }

  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>