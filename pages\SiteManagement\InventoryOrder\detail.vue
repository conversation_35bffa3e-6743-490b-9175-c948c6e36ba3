<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>

    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="盘点名称" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.inventoryName }}
          </view>
        </u-form-item>

        <u-form-item label="仓库" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockCode, model.stockText) }}
          </view>
        </u-form-item>

        <!-- <u-form-item label="库位" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockLocationCode, model.stockLocationText) }}
          </view>
        </u-form-item> -->

        <u-form-item label="盘点类型" labelWidth="100">
          <view class="w100x flex right">
            {{ model.inventoryTypeText }}
          </view>
        </u-form-item>
      </u--form>
      <view class="mt10">
        <view class="mb10" style="color: #409eff"> 盘点物料清单 </view>
        <!-- <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">仓库</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">工序/库位</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">库存数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">盘点数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">盘点结果</view>
        </view>
        <view class="table_content">
          <view v-for="(item, itemindex) in list" :key="itemindex" class="flex bl_e1e1e1 h40">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ itemindex + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.processOperationText }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.consumableSpecText }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.processOperationText }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.quantity }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.inventoryQuantity }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50"> {{ item.inventoryResultText }}</view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view> -->

        <view class="table_content">
          <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
            <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
              <view class="flex between h30 hcenter c_999">
                <view> 仓库</view>
                <view>
                  {{ $utils.optionShowConfig(ele.stockCode, ele.processOperationText) }}
                </view>
              </view>
              <view class="flex between h30 hcenter c_999">
                <view> 物料</view>
                <view>
                  {{ $utils.optionShowConfig(ele.consumableSpecName, ele.consumableSpecText) }}
                </view>
              </view>

              <view class="flex between h30 hcenter c_999">
                <view> 工序/库位</view>
                <view>
                  {{ $utils.optionShowConfig(ele.processOperationName, ele.processOperationText) }}
                </view>
              </view>

              <view class="flex between h30 hcenter c_999">
                <view> 库存数量</view>
                <view>{{ ele.quantity }} {{ ele.consumableUnit }}</view>
              </view>

              <view class="flex between h30 hcenter c_999" v-if="model.state == 'Processing'">
                <view class="mr5">盘点数量</view>
                <view class="w100 flex hcenter">
                  <u--input class="" inputAlign="right" v-model="ele.inventoryQuantity" border="bottom" type="number" placeholder="请输入"></u--input>
                  {{ ele.consumableUnit }}
                </view>
              </view>

              <view class="flex between h30 hcenter c_999" v-if="model.state == 'Finish'">
                <view> 盘点数量</view>
                <view>{{ ele.inventoryQuantity }} {{ ele.consumableUnit }}</view>
              </view>

              <view class="flex between h30 hcenter c_999" v-if="model.state == 'Finish'">
                <view> 盘点结果</view>
                <view>{{ ele.inventoryResultText }} </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view>
      </view>
    </view>

    <view class="flex pa5">
      <!-- 未开始 -->
      <u-button type="success" text="开始盘点" v-if="model.state == 'NotStart'" @click="inventoryHandle"></u-button>
      <u-button type="success" text="提交" v-if="model.state == 'Processing'" @click="finishHandle"></u-button>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      status: 'nomore',
      model: {},
      list: [],
      orginList: [],
      paramsOption: {
        inventoryOrderNo: '',
        state: '',
        //   'NotStart': '未开始',
        //   'Processing': '进行中',
        //   'Finish': '已完成',
      },
    }
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题

    let paramsOption = options && JSON.parse(options.params)
    this.paramsOption = paramsOption

    this.nlsMap = nlsMap
    this.initModel()
    this.getDetail()
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('returnRefresh')
    },
    initModel() {
      this.model = {
        inventoryName: '',//盘点名称	
        stockCode: '',// 
        stockText: '', // 
        stockLocationCode: '', // 库位
        stockLocationText: '', // 库位
        state: '', // 状态
      }
    },
    async getDetail() {
      let parmas = {
        inventoryOrderNo: this.paramsOption.inventoryOrderNo
      }

      let res = await this.$service.InventoryOrder.listInventoryOrder(parmas)
      this.model.inventoryName = res.datas[0].inventoryName
      this.model.stockCode = res.datas[0].stockCode
      this.model.stockText = res.datas[0].stockText
      this.model.stockLocationCode = res.datas[0].stockLocationCode
      this.model.stockLocationText = res.datas[0].stockLocationText
      this.model.inventoryTypeText = res.datas[0].inventoryTypeText
      this.model.state = res.datas[0].state

      if (res.datas[0].state == 'Processing') { //进行中inventoryQuantity 默认等于quantity
        this.list = res.datas[0].inventoryOrderItemDetailDto.map(ele => ({
          ...ele,
          inventoryQuantity: ele.quantity
        }))
      } else {
        this.list = res.datas[0].inventoryOrderItemDetailDto
      }
    },

    inventoryHandle() {
      let params = {
        inventoryOrderNo: this.paramsOption.inventoryOrderNo,
      }
      this.$service.InventoryOrder.inventory(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.getDetail()
        }, 1500);
      })
    },
    finishHandle() {
      let flag = this.list.every(ele => ele.inventoryQuantity)
      if (!flag) {
        return this.$Toast('请填写盘点数量!')
      }
      uni.showModal({
        title: '提示',
        content: `是否确认提交？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            // this.list.splice(index, 1)
            let params = {
              inventoryOrderNo: this.paramsOption.inventoryOrderNo,
              inventoryOrderItemList: this.list
            }
            this.$service.InventoryOrder.finish(params).then(res => {
              this.$Toast('操作成功')
              setTimeout(() => {
                this.leftClick()
              }, 1500);
            })
          }
          if (res.cancel) { }
        },
      })
    },
    rejectHandle() {
      let params = {
        returnOrderNo: this.params_returnOrderNo,
        returnItemConsumableList: this.orginList,
        stockCode: this.model.stockCode,
        stockLocationCode: this.model.stockLocationCode,
        trayReturnType: this.model.trayReturnType,
      }
      this.$service.ReturnOrderController.reject(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.leftClick()
        }, 1500);
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_header {
      flex-shrink: 0;
    }
    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>
