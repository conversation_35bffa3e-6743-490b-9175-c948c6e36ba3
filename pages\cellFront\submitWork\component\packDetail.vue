<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="报工入库详情" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex between h40 hcenter c_999">
        <view>车间</view>
        <view>{{ workShopSectionDec }}</view>
      </view>
      <view class="flex between h40 hcenter c_999">
        <view>生产线</view>
        <view>{{ model.areaName }}</view>
      </view>
    </view>
    <view class="myContainer ma10">
      <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
        <view class="flex between h40 hcenter c_999">
          <view>生产工单</view>
          <view>{{ ele.productRequestName }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>物料</view>
          <view>{{ ele.productSpecName }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>物料描述</view>
          <view>{{ ele.productSpecDesc }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>报工状态</view>
          <view>{{ ele.reportProductRequestStateValue }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view class="mr5">完工量</view>
          <view>{{ ele.finishedQuantity || 0 }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view class="mr5">良品数量</view>
          <view>{{ ele.assignedQuantity || 0 }}</view>
        </view>
        <view>
          <!-- <u-button v-if="ele.reportProductRequestState === 'ALREADY_REPORT'" color="gray" disabled text="不可录入"></u-button> -->
          <u-button type="success" text="录入报工" @click="summit(ele)"></u-button>
        </view>
      </view>
      <NoData v-if="!list || list.length === 0"></NoData>
      <u-modal :show="showModal2" title="录入报工量" confirmText="确认" @confirm="changeConfirm" @cancel="modalCancel" :showCancelButton="true">
        <view class="listContainer">
          <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
            <!-- <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50"></view> -->
            <view class="flex center w50 bc_f1f1f1 bb_e1e1e1 br_e1e1e1" @click="changAllSelect">
              <view class="btn" :class="{ 'c-active': active_isAllSelect }"></view>
            </view>
            <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">批次号</view>
            <!-- <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">数量</view> -->
          </view>
          <view class="table_content">
            <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
              <view v-for="(item, index) in lotNameList" :key="index" class="flex bl_e1e1e1 h50" @click="changSelect(item)">
                <view class="flex center w50 bc_fff bb_e1e1e1 br_e1e1e1">
                  <view class="btn" :class="{ 'c-active': item.isSelect }"></view>
                </view>
                <!-- <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view> -->
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.lotName }}</view>
                <!-- <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ item.assignedQuantity }}</view> -->
              </view>
              <view v-if="!lotNameList || lotNameList.length === 0">
                <view class="bb_e1e1e1 br_e1e1e1 bl_e1e1e1 h40 flex center">暂无数据</view>
                <!-- <u-empty mode="data"></u-empty> -->
              </view>
            </scroll-view>
          </view>
        </view>
      </u-modal>
    </view>
  </view>
</template>


<script>
import { USER_ID } from '@/utils/common/evtName.js'
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      model: {},
      selectItem: {}, // 选中项
      list: [],
      workShopSection:'',
      workShopSectionDec: '',
      showModal2: false,
      lotNameList: [],
      isAllSelect: false
    };
  },

  onLoad(e) {
    this.model = e && JSON.parse(e.params)
    this.workShopSectionDec = e && e.workShopSectionDec
    this.workShopSection = e && e.workShopSection
    this.getList()
  },
  computed: {
    active_isAllSelect() {
      return this.lotNameList.length && this.lotNameList.every(item => item.isSelect)
    }
  },
  methods: {
    getList() {
      let params = JSON.parse(JSON.stringify(this.model))
      this.$service.submitWork.detail(params).then(res => {
        this.list = res.data
      })
    },
    summit(ele) {
      this.selectItem = ele
      this.showModal2 = true
      let params = {
        productRequestName: ele.productRequestName,
        workShopSection: this.model.workShopSection
      }
      this.$service.submitWork.listProductRequestLot(params).then(res => {
        this.lotNameList = res.data
      })
    },

    changSelect(item) {
      let isSelect = !item.isSelect
      this.$set(item, 'isSelect', isSelect)
    },
    changAllSelect() {
      if (this.isAllSelect) {
        this.lotNameList.forEach(v => {
          this.$set(v, 'isSelect', false)
        })
        this.isAllSelect = false
      } else {
        this.lotNameList.forEach(v => {
          this.$set(v, 'isSelect', true)
        })
        this.isAllSelect = true
      }
    },
    changeConfirm() {
      if (!this.selectItem.lineSideWareHouse) {
        this.$Toast(`该产线[${this.list.areaDesc}]没有维护线边仓，请联系管理员配置`)
        return
      }
      let filterArr = this.lotNameList.filter(item => item.isSelect).map(v => ({
        workOrderName: this.selectItem.workOrderName,
        productRequestName: this.selectItem.productRequestName,
        lineSideWareHouse: this.selectItem.lineSideWareHouse,
        productSpecName: this.selectItem.productSpecName,
        unit: this.selectItem.unit,
        lotName: v.lotName,
        reportQuantity: 1,
        assignedQuantity: 1,
      }))
      let params = {
        erpOwlProductRequestList: filterArr,
        operateValue: '10001',
        userId: this.$getLocal(USER_ID),
        workShopSection: this.workShopSection
      }
      this.$service.submitWork.erpOwlProductRequest(params).then(res => {
        this.$Toast('录入成功')
        this.modalCancel()
        setTimeout(() => {
          this.getList()
        }, 1000);
      })
    },
    // 模态框取消
    modalCancel() {
      this.isAllSelect = false
      this.showModal2 = false
      this.lotNameList = []
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
.listContainer {
  width: 100%;
  // flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // margin-top: 20rpx;
  max-height: 800rpx;
  .table_header {
    flex-shrink: 0;
  }
  .table_content {
    flex: 1;
    overflow-y: scroll;
  }
}

.btn {
  width: 30rpx;
  height: 30rpx;
  border-radius: 100%;
  background-color: #fff;
  border: 2rpx solid #b1c2db;
  &.c-active {
    background-color: #3f85e6;
    border: 2rpx solid #b1c2db;
    // border: 4rpx solid #b1c2db;
  }
}
</style>