<script>
import nlsStore from "@/mixins/nlsStore";
export default {
  mixins: [nlsStore],
  data() {
    return {
      // App.vue文件中获取不到this? 需要调整/
      timer: null,
    }
  },
  onLaunch: function () {
    uni.$on('login-success', this.handleLoginSuccess)
    uni.$on('user-logout', this.userLogout)
    this.initData()
    // console.warn(
    //   '当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！'
    // )
    console.log('App onLoad')
    // #ifdef APP-PLUS
    plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
      console.log('plusruntime')
      this.$service.common.getLastApp().then((res) => {
        console.log('Lastapp')
        console.warn(res)
        if (res.data) {
          const resdata = res.data,
            nowversion = widgetInfo.version
          if (nowversion < resdata.version) {
            uni.showModal({
              title: '更新提示',
              content: `检测到APP有新内容，请点击进行更新！更新内容如下：${resdata.remark}`,
              success: function (res) {
                if (res.confirm) {
                  // plus.runtime.openURL(resdata.downloadUrl) // 整包下载
                  uni.showLoading({
                    title: '正在更新...',
                  })
                  uni.downloadFile({
                    // 热更新
                    url: resdata.downloadUrl,
                    success: (downloadResult) => {
                      if (downloadResult.statusCode === 200) {
                        console.log(downloadResult, '进来了！！！！！')
                        plus.runtime.install(
                          downloadResult.tempFilePath,
                          {
                            force: false, // 强制更新
                          },
                          function () {
                            console.log('成功！！！')
                            plus.runtime.restart()
                            uni.hideLoading()
                          },
                          function (e) {
                            console.error('热更新失败，错误原因：', e)
                          }
                        )
                        console.log('结束了！！！！！')
                      } else {
                        console.error('热更新失败')
                      }
                    },
                  })
                }
              },
            })
          }
        }
      })
    })
    // #endif
  },
  onShow() { },
  onHide: function () { },
  methods: {
    initData() {
      if (uni.getStorageSync('isLogin')) {
        uni.$emit('login-success')
      }
    },
    async handleLoginSuccess() {
      await this.setGlobalNls()
      await this.$service.common.heartBeat({}, { isFormData: true, showLoading: false }).then(res => {
        this.setDetect()
      })
    },
    // 设置心跳检测
    setDetect() {
      // 10分钟一次
      const heartBeatInterval = 6
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.detect()
      }, 10 * 6 * 10000)
    },
    async detect() {
      if (!uni.getStorageSync('isLogin')) {
        this.userLogout()
        return;
      }
      await this.$service.common.heartBeat({}, { isFormData: true, showLoading: false })
    },
    userLogout() {
      clearInterval(this.timer)
      this.timer = null
    },
  },
}
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uview-ui/index.scss';
@import './styles/public.scss';
@import './styles/fonsIconStyle/stylesheet.css';
@import './styles/fontstyle/iconfont.css';

page {
  font-size: 28rpx;
  color: #666;
}
</style>
