<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="材料不良判定" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="材料标签条码" borderBottom required labelWidth="120">
          <u--input v-model="model.clbqtm" border="none" focus placeholder="请扫描材料标签条码" @focus="focusEvent('clbqtm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('clbqtm')"></view>
        </u-form-item>

        <u-form-item label="不良材料" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.blcl }} </view>
        </u-form-item>

        <u-form-item label="单位" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.dw }} </view>
        </u-form-item>

        <u-form-item label="不良现象" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.blxx }} </view>
        </u-form-item>

        <u-form-item label="数量" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.sl }} </view>
        </u-form-item>

        <!-- <u-form-item label="生产工单" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.scgd }} </view>
        </u-form-item> -->

        <u-form-item label="处置方式" borderBottom labelWidth="100">
          <!-- <view class="w100x flex right"> {{ model.durableType }} </view> -->
          <view class="w100x flex" :style="'flex-direction: row-reverse'">
            <u-radio-group
                v-model="model.czfs"
                placement="column"
                style="width: 100%;"
              >
              <view style="display: flex;flex-direction: row-reverse">
                <u-radio
                  v-for="item in radiolist1"
                  :key="item.name"
                  :customStyle="{ marginRight: '10rpx' }"
                  :label="item.name"
                  :name="item.name"
                >
                </u-radio>
              </view>
            </u-radio-group>
          </view>
        </u-form-item>

        <u-form-item label="备注" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.bz" border="none" placeholder="请输入备注"></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        clbqtm: '',
        blcl: '',
        dw: '',
        blxx: '',
        blsl: '',
        czfs: '放行',
        bz: ''
      },
      columns: [],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      radiolist1: [
        {
          name: '退货'
        },
        {
          name: '放行'
        }
      ],
    }
  },
  watch: {
    'model.clbqtm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.lotName': {
      handler(res) {
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    // this.initModel()
    // this.GetReasonCodeType()
  },
  methods: {
    GetReasonCodeType() {
      const params = {
        reasonCodeGroup: 'DieCutting'
      }
      this.$service.DieCutting.GetReasonCodeType(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return item.reasonCodeType + '/' + item.typeDesc
        })
        // this.GetReasonCodeTypeList.forEach(item => {
        //   item = item.reasonCodeType + '/' + item.typeDesc
        // })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType() {
      this.columns = this.GetReasonCodeTypeList
      this.select = true
    },
    selectFirm(e) {
      this.model.productRequestName = e.value[0].productRequestName

      this.focusObj.saveNo = true
      this.select = false
    },
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/BindingAndUnbinding/modules/ProductList?durableName=${this.model.durableName}`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        clbqtm: '',
        blcl: '',
        dw: '',
        blxx: '',
        blsl: '',
        czfs: '',
        bz: ''
      }
    },
    submit(value, type) {
      if(!this.model.clbqtm) {
        this.$Toast('请输入材料标签条码！')
        return
      }
      let params = {
        functionType: 'Judge',
        ...this.model,
        consumableName: this.model.clbqtm,
        ngState: this.model.czfs == '放行' ? 'G' : 'S', // 放行是G，退货是S
        eventComment: this.model.bz
      }
      this.$service.QualityControl.NgConsumableRecordAndJudgeSubmit(params).then(res => {
        this.$Toast('材料不良判定提交成功！')
        // 提交成功初始化
        this.initModel()
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 载具 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        functionType: 'Judge',
        consumableName: value
      }
      try {
        let res = await this.$service.QualityControl.getConsumableData(params)
        console.log('getConsumableData', res);
        if (res.datas.length > 0) {
          let data =  res.datas[0]
          this.model = {
            ...res.datas[0],
            // clbqtm: this.model.clbqtm,
            // blxx: '',
            // blxxType: '',
            // reasonCodeType: '',
            // sbh: this.model.sbh ? this.model.sbh : '',
            // sbms: this.model.sbms ? this.model.sbms : '',
            // gx: this.model.gx ? this.model.gx : '',
            // scgd: data.productOrderName,
            // blsl: data.quantity,
            clbqtm: this.model.clbqtm,
            blcl: data.consumableSpecName + '/' + data.description,
            blxx: data.reasonCode + '/' + data.reasonCodeDesc,
            sl: data.ngQuantity,
            dw: data.consumableUnit,
            czfs: this.model.czfs ? this.model.czfs : '',
          }
          // 获取不良现象
          // this.GetReasonCodeType()
        } else {
          this.model.clbqtm = ''
          this.$Toast('材料标签不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
