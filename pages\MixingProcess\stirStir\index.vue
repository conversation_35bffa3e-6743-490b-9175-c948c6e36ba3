<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="搅拌工序-搅拌" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="搅拌机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入搅拌机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="搅拌机描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDescription" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.machinePolarity }}
          </view>
        </u-form-item>

        <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.lotName }}
          </view>
        </u-form-item>
        <!-- <u-divider style="color: #02a7f0;" text="搅拌信息" textPosition="left"></u-divider> -->
        <div class="fs16 fb ml22 mt10" style="color: #02a7f0">
          <span>搅拌信息</span>
        </div>
        <!-- 搅拌信息 -->
        <u-form-item label="投料搅拌进度" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ model.currentWorkStep }}
          </view>
        </u-form-item>

        <u-form-item label="操作步骤" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.currentWorkStepName }}
          </view>
        </u-form-item>

        <u-form-item label="工艺标准时长(min)" borderBottom labelWidth="160">
          <view class="w100x flex right">
            {{ model.processStandard }}
          </view>
        </u-form-item>

        <u-form-item label="工艺要求说明" borderBottom labelWidth="130">
          <view class="w100x flex right">
            <view class="flex w65x hcenter">
              {{ model.processRequest }}
            </view>
          </view>
        </u-form-item>

        <u-form-item label="搅拌开始时间" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ model.mixingStartTime }}
          </view>
        </u-form-item>

        <u-form-item label="搅拌结束时间" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ model.mixingEndTime }}
          </view>
        </u-form-item>

        <u-form-item label="搅拌搅拌剩余时长(min)" borderBottom labelWidth="200">
          <view class="w100x flex right">
            {{ remainingTime }}
          </view>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer2">
      <view class="disabled" v-if="model.mixingStartTime">搅拌开始</view>
      <view @click="mixingStart" v-else>搅拌开始</view>
      <view class="disabled" v-if="!model.mixingStartTime || model.mixingEndTime">搅拌结束</view>
      <view @click="mixingEnd" v-else>搅拌结束</view>
    </view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '搅拌机编号不能为空'
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      remainingTime: null, // 剩余时间
      content: '搅拌时长小于工艺标准时长，是否提前结束搅拌?',
      time: null,
      show: false
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.mixingStartTime': {
      handler(val) {
        if (val) {
          this.getRemainingTime(val)
          this.time = null
          this.time = setInterval(() => {
            this.getRemainingTime(val)
          }, 60000);
        }
      }
    },
  },
  onLoad() {
    this.time = null
    this.initModel()
  },
  methods: {
    moment,
    focusEvent(type) {
      // this.model[type] = ''
    },
    getRemainingTime(val) {
      let endTime = new Date(moment(val).valueOf())
      let min = endTime.getMinutes();
      endTime.setMinutes(min + Number(this.model.processStandard));
      let stime = moment(new Date()).valueOf();
      let etime = moment(endTime).valueOf();
      let usedTime = etime - stime;  //两个时间戳相差的毫秒数
      let hours = parseInt((usedTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      let minutes = parseInt((usedTime % (1000 * 60 * 60)) / (1000 * 60));
      this.remainingTime = Number(hours) * 60 + Number(minutes)
      console.log(this.remainingTime, 'remainingTimeremainingTime');
    },
    initModel() {
      this.model = {
        machineName: null, // 搅拌机
        machineDescription: null, // 搅拌机描述
        machinePolarity: '', // 极性
        lotName: null, // 出货牌号
        currentWorkStep: null, // 投料搅拌进度
        currentWorkStepName: null, // 操作步骤
        processStandard: null, // 工艺标准时长
        processRequest: null, // 工艺要求说明
        mixingStartTime: null, // 搅拌开始时间
        mixingEndTime: null, // 搅拌结束时间
      }
      this.remainingTime = null
    },
    /* 搅拌机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.stir.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
        }
      } catch (error) {
        // this.model.machineName = null
        this.initModel()
      }
    },
    /* 搅拌开始 */
    mixingStart() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      this.model.mixingStartTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      let params = {
        ...this.model
      }
      this.$service.stir.mixingStart(params).then(res => {
        this.$Toast('搅拌开始成功!')
      })
    },
    /* 搅拌结束 */
    mixingEnd() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.remainingTime > 0) {
        this.show = true
        return
      }
      this.model.mixingEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      let params = {
        ...this.model
      }
      this.$service.stir.mixingEnd(params).then(res => {
        this.$Toast('搅拌结束成功!')
        this.remainingTime = null
        this.changeMachineName(this.model.machineName)
        // this.initModel()
      })
    },

    /* 二次确认 */
    confirm() {
      this.model.mixingEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      let params = {
        ...this.model
      }
      this.$service.stir.mixingEnd(params).then(res => {
        this.$Toast('搅拌结束成功!')
        this.show = false
        this.remainingTime = null
        this.initModel()
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
