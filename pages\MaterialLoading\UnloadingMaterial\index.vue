<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="材料卸料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10 mb10">
      <view class="myContainer ml10 mr10 mb10">
        <u--form labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="设备号:" required labelWidth="100">
            <u--input v-model="form.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="设备描述:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10">已上料明细</view>
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(item, index) in simpleTrackProduct" :key="index">
            <view class="topContainer bc_999 br12 ma10">
              <view class="flex h30 hcenter c_00b17b">
                <view>安装点:</view>
                <view class="ml6">{{ item.key }}</view>
              </view>
            </view>
            <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index2) in item.data" :key="index2">
              <view class="flex between h40 hcenter c_999">
                <view>物料编码:</view>
                <view>{{ ele.consumableSpecName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>标签条码:</view>
                <view>{{ ele.consumableName }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料时间:</view>
                <view>{{ ele.createTime }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>上料数量:</view>
                <view>{{ ele.createQuantity }}{{ ele.targetUnit ? ele.targetUnit : ele.sourceUnit }}</view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>理论剩余数量:</view>
                <view class="flex hcenter">
                  <view>{{ ele.quantity }}{{ ele.targetUnit ? ele.targetUnit : ele.sourceUnit }}</view>
                </view>
              </view>
              <view class="flex between h40 hcenter c_999">
                <view>实际剩余数量:</view>
                <view class="flex hcenter">
                  <u--input type="number" v-model="ele.qtyInput" border="none" placeholder="请输入数量" @change="(val) => changeQtyInput(val, ele, index, index2)"></u--input>
                  <!--  -->
                  <view>{{ ele.sourceUnit }}</view>
                </view>
              </view>
              <view class="flex between h40 hcenter c_999" v-if="ele.targetUnit">
                <view>实际剩余数量(转换后):</view>
                <view>
                  <view>{{ ele.qty }}{{ ele.targetUnit }}</view>
                  <!-- <u--input type="number" v-model="ele.qty" border="none" placeholder="请输入数量"></u--input> -->
                </view>
              </view>
              <u-button class="btn" @click="submit(ele)">卸载</u-button>
            </view>
          </view>
          <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeQtyInput = this.$debounce(this.changeQtyInput, 1000)
    return {
      rulesTip: {
        machineName: '设备号不能为空！',
      },
      form: {},
      simpleTrackProduct: [],
      show: false,
      content: '',
      index1: 0,
      index2: 0
    };
  },

  onLoad(e) {
    this.initModel()
  },
  methods: {
    confirm() {
      this.simpleTrackProduct[this.index1].data[this.index2].qty = this.simpleTrackProduct[this.index1].data[this.index2].qtyInput * Number(this.simpleTrackProduct[this.index1].data[this.index2].ratio)
      this.show = false
      this.simpleTrackProduct[this.index1].data[this.index2].canSub = true
    },
    async changeQtyInput(val, ele, index1, index2) {
      if (!val) {
        ele.qty = 0
        return
      }
      this.index1 = index1
      this.index2 = index2
      this.simpleTrackProduct[this.index1].data[this.index2].qty = (this.$utils.calculate_mul(this.simpleTrackProduct[this.index1].data[this.index2].qtyInput, Number(this.simpleTrackProduct[this.index1].data[this.index2].ratio))).toFixed(3).slice(0, -1)

      // if(!ele.sourceUnit || !ele.targetUnit) {
      //   // 弹窗提示
      //   this.content = `该物料${ele.consumableSpecName}, 在该工序${ele.processOperationName}没有维护或维护多条单位转换, 现默认按1:1转换，是否继续执行该操作？`
      //   this.show = true
      // } else {
      //   this.simpleTrackProduct[this.index1].data[this.index2].qty = this.simpleTrackProduct[this.index1].data[this.index2].qtyInput * Number(this.simpleTrackProduct[this.index1].data[this.index2].ratio)
      //   this.simpleTrackProduct[this.index1].data[this.index2].canSub = true
      // }
    },
    initModel() {
      this.form = {
        machineName: null, // 设备号
        machineDescription: null, // 搅拌机描述
        quantity: null // 数量
      }
    },
    focusEvent(type) {
      this.form[type] = ''
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.MaterialLoading.UnLoadingQueryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { machineName, machineDescription } = res.datas[0]
          this.form.machineDescription = machineDescription
          this.form.machineName = machineName
          this.getData(value)
        } else {
          this.$Toast('设备不存在！')
        }
      } catch (error) {
        this.form.machineName = null
      }
    },
    groupBy(arr, filed) {
      let temObj = {}
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i]
        if (!temObj[item[filed]]) {
          temObj[item[filed]] = [item]
        } else {
          temObj[item[filed]].push(item)
        }
      }
      console.log('temObj', temObj);
      let resArr = []
      Object.keys(temObj).forEach(key => {
        resArr.push({
          key: key,
          data: temObj[key],
        })
      })
      console.log('resArr', resArr);
      return resArr
    },
    getData(machineName) {
      let params = {
        machineName,
      }
      this.$service.MaterialLoading.getConsumableLoadingData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            res.datas.forEach(item => item.qty = 0)
            this.simpleTrackProduct = this.groupBy(res.datas, 'materialLocationName')
          } else {
            this.simpleTrackProduct = []
          }
        }
      })
    },
    submit(item) {
      if (!item.qtyInput && item.qtyInput != 0) return this.$Toast('请输入实际剩余数量')
      uni.showModal({
        title: '提示',
        content: `设备${this.form.machineName}，现卸料${item.consumableSpecName},剩余数量：${item.qtyInput}，请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              ...item
            }
            params.quantity = Number(params.qty)
            if (params.quantity > item.createQuantity) {
              return this.$Toast('剩余数量不能大于上料数量！')
            } else if (params.quantity < 0) {
              return this.$Toast('剩余数量不能小于0！')
            }
            this.$service.MaterialLoading.ConsumableUnLoading(params).then((res) => {
              if (res && res.success) {
                this.$Toast('卸料成功！')
                setTimeout(() => {
                  this.getData(this.form.machineName)
                }, 800);
              }
            })
          }
          if (res.cancel) { }
        },
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.form.machineName = 'G.EQ.LCDP.01.01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.form, key, res.result)
          },
        })
      }
      // #endif
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
</style>