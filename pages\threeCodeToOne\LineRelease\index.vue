<template>
	<view class="bc_f3f3f7 myContainerPage">
		<u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
			leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
		<!-- {{ nlsMap }} -->
		<view class="myContainer ma10">
			<u--form labelPosition="left" :model="model" labelWidth="100">
				<u-form-item label="要投入的设备" borderBottom required labelWidth="120">
					<view class="w100x flex right" @click="checkSelect('machineName')">
						<view v-if="model.machineName">{{ $utils.filterObjLabel(dicts.machineNoList, model.machineName)
						}}
						</view>
						<view class="c_c0c4cc" v-else>请选择</view>
						<view class="ml5"
							:style="{ transform: select && selectType === 'machineName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</u-form-item>

				<u-form-item label="要投入的工单" borderBottom required labelWidth="120">
					<view class="w100x flex right" @click="checkSelect('productOrderName')">
						<view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList,
							model.productOrderName)
						}}
						</view>
						<view class="c_c0c4cc" v-else>请选择</view>
						<view class="ml5"
							:style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</u-form-item>

				<u-form-item label="要投入的工序" borderBottom required labelWidth="120">
					<view class="w100x flex right" @click="checkSelect('oprSequenceNo')">
						<view v-if="model.oprSequenceNo">{{ $utils.filterObjLabel(dicts.processOperationNameList,
							model.oprSequenceNo) }}
						</view>
						<view class="c_c0c4cc" v-else>请选择</view>
						<view class="ml5"
							:style="{ transform: select && selectType === 'oprSequenceNo' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</u-form-item>
			</u--form>
			<u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
				@cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
			<view class="mt10">
				<u--form labelPosition="left" :model="model" labelWidth="100">
					<u-form-item label="载具" labelWidth="100">
						<u--input v-model="model.durableName" border="none" placeholder="请扫描或输入" @focus="handleFocus(false)"  @blur="handleFocus(true)"></u--input>
						<view class="iconfont icon-saoma" @click="scan('durableName')"></view>
					</u-form-item>

					<u-form-item label="载具名称" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ model.durableText }}</view>
					</u-form-item>

					<u-form-item label="已载/容量" borderBottom labelWidth="150">
						<view class="w100x flex right"> {{ model.lotQuantity }}/{{ model.capacity }}</view>
					</u-form-item>

				</u--form>
			</view>
			<view class="mt10">
				<u--form labelPosition="left" :model="model" labelWidth="100">
					<u-form-item label="槽位号" borderBottom labelWidth="150">
						<!-- <view class="w100x flex right"> {{ model.slot }}</view> -->
						<u--input v-model="model.slot" @focus="handleFocus(false)"  @blur="handleFocus(true)"></u--input>
					</u-form-item>
					<u-form-item label="电芯条码" required labelWidth="100">
						<u--input :key="inputKey"  v-model="model.lotName" border="none" placeholder="请扫描或输入" :focus="isInBoxOrSlot"  @blur="handleBlur"></u--input>
						<view class="iconfont icon-saoma" @click="scan('lotName')"></view>
					</u-form-item>
				</u--form>
			</view>
			<view class="mt10">
				<view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
					<view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">槽位号</view>
					<view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">条码号</view>
					<view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
				</view>
				<view class="table_content">
					<view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
						<view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50 pl4 pr4 pt2 pb2">{{ ele.slot }}</view>
						<view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.serialno }}</view>
						<view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
							<view class="w80x" @click="deleteItem(ele, index)">
								<u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
							</view>
						</view>
					</view>
					<NoData v-if="!list || list.length === 0"></NoData>
				</view>
			</view>


		</view>
		<view class="btnContainer" @click="submit">提交</view>
	</view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData';
import useNls from "@/mixins/useNls";
import moment from 'moment';
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";


const SHOW_MODEL_KEY = 'CONSUMABLE_RECIEVE_EXIST'
export default {
	mixins: [useNls, ScrollMixin, PrintPackageMixin],
	components: {
		NoData,
	},
	data() {
		this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
		return {
			lodash: _,
			pageParams: {},
			pageTitle: '',
			globalMap: getApp().globalData.globalMap, // 获取全局数据
			nlsMap: {

			},

			columns: [],
			select: false,
			selectType: '',
			trayNoParmas: {},
			model: {},
			dicts: {
				machineNoList: [],
				productOrderNameList: [],
				processOperationNameList: [],
			},
			selectedJobType: '',
			list: [],
			inputKey:0,
			isInBoxOrSlot: true, // 记录是否在 载具框/槽位号框
		}
	},
	computed: {
	},
	watch: {
		'model.durableName': {
			handler(val) {
				this.changeboxNo(val)
			},
		},

		'model.lotName': {
			handler(val) {
				this.changelotName(val)
			},
		},
	},
	async onLoad(options) {
		let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
		this.pageParams = pageParams
		this.pageTitle = pageParams.pageTitle // 标题
		await this.initNls(pageParams, this.nlsMap)

		this.ScanMachineName()
		this.initModel()
	},
	methods: {
		moment,
		checkSelect(type) {
			this.select = true;
			this.selectType = type;
			switch (type) {
				case 'machineName':
					this.columns = this.dicts.machineNoList;
					break;
				case 'productOrderName':
					this.columns = this.dicts.productOrderNameList;
					break;
				case 'oprSequenceNo':
					this.columns = this.dicts.processOperationNameList;
					break;
				default:
					break;
			}
		},

		async selectFirm(e) {
			this.$set(this.model, this.selectType, e.value[0].value)
			this.select = false
			if (this.selectType == 'machineName') {
				this.productOrderNameList = []
				this.scanMachineNameInfo()
			}
		},

		initModel() {
			this.model = {
				slot: 1,
				lotName: '',
				machineName: '',
				oprSequenceNo: '',
				durableText: '',
				durableName: '',
				lotQuantity: '',
				capacity: '',
				productOrderName: '',
			}
		},
		ScanMachineName() {
			this.$service.ThreeCodeToOne.ScanMachineName().then(res => {
				this.dicts.machineNoList = res.datas.map(item => ({
					label: item.machineText,
					value: item.machineName,
				}))
			})
		},
		async changeboxNo(value) {

			if (!value) return
			
			// 去除前后空格和回车符
			    value = value.trim().replace(/\r?\n|\r/g, '')
			if (!this.model.oprSequenceNo) {
				this.$Toast('请选择工序!')
				this.model.durableName = ''
				return
			}

			if (!this.model.productOrderName) {
				this.$Toast('请选择工单!')
				this.model.durableName = ''
				return
			}
			try {
				let params = {
					durableName: value,
					wipOrderNo: this.model.productOrderName,
					oprSequenceNo: this.model.oprSequenceNo
				}
				let res = await this.$service.ThreeCodeToOne.scanDurableName(params)
				if (res.success) {
					this.model.capacity = res.datas[0].capacity

					this.model.durableText = res.datas[0].durableText
					this.model.lotQuantity = res.datas[0].lotQuantity
				}

				this.$Toast(res.msg)
			} catch (error) {
				this.model.durableName = ''
			}


		},

		async changelotName(value) {

			if (!value) return
			
			// 去除前后空格和回车符
			    value = value.trim().replace(/\r?\n|\r/g, '')
			// 1. 校验条码长度是否为 24 位，且以 '01DCB' 开头
			if (value.length !== 24 || !value.startsWith('01DCB')) {
				this.model.lotName = ''
				this.inputKey++
				this.$Toast('不符合一线编码规则')
				return 
			}
			// 2. 检查条码是否已被扫描过
			let scanned = this.list.some(item => item.serialno === value);
			if (scanned) {
				this.model.lotName = ''
				this.inputKey++
				this.$Toast(`该条码 ${value} 已被扫描过`)
				return
			}
			// 3. 判断是否超出托盘最大容量
			if (this.list.length >= this.model.capacity) {
				this.model.lotName = ''
				this.inputKey++
				this.$Toast('已超托盘扫描上限')
				return
			}
			try {
				let params = {
					serialno: value,
				}
				let res = await this.$service.ThreeCodeToOne.ScanLotName(params)
				// if (res.datas.length > 0) {
				// 	this.model.lotName = ''
				// 	this.inputKey++
					// this.$Toast(`该条码 ${value} 已存在！`)
				// } else {
					let exists = this.list.some(item => item.slot === this.model.slot);

					if (exists) {
						this.$Toast(`槽位号 ${this.model.slot} 已存在！`);
					} else {
						this.list.push({
							slot: this.model.slot,
							serialno: value
						});
						this.model.slot++;
					}
				// }

				this.model.lotName = ''
			} catch (error) {
				this.model.lotName = ''
			}


		},
		async scanMachineNameInfo() {
			let data = {
				machineName: this.model.machineName,
			}
			let res = await this.$service.ThreeCodeToOne.scanMachineNameInfo(data)


			if (res.success) {
				this.dicts.productOrderNameList = res.datas[0].productOrderNameList.map(item => ({
					label: item,
					value: item,
				}))
				this.dicts.processOperationNameList = res.datas[0].processOperationNameList.map(item => ({
					label: item.processOperationText,
					value: item.processOperationName,
				}))
			}


		},
		deleteItem(item, index) {
			uni.showModal({
				title: '提示',
				content: `是否确认删除？`,
				cancelText: '取消',
				confirmText: '确认',
				cancelColor: '#666',
				confirmColor: '#409eff',
				success: (res) => {
					if (res.confirm) {
						this.model.slot = item.slot
						this.list.splice(index, 1);
					}
					if (res.cancel) { }
				},
			})
		},
		handleFocus(status) {
		    this.isInBoxOrSlot = status;
		  },
		handleBlur() {
			setTimeout(() => {
			if (this.isInBoxOrSlot) {
				this.isInBoxOrSlot = false
			  this.$nextTick(() => {
			   this.isInBoxOrSlot = true
			   
			  });
			}
		     }, 100);
		  },
		async submit() {
			if (!this.model.machineName || !this.model.productOrderName || !this.model.oprSequenceNo) {
				this.$Toast("请填写完整信息");
				return;
			}
			if (!this.list || this.list.length === 0) {
				this.$Toast("请先扫电芯码");
				return;
			}
			let data = {
				machineNo: this.model.machineName,
				wipOrderNo: this.model.productOrderName,
				oprSequenceNo: this.model.oprSequenceNo,
				container: this.model.durableName,
				serialnoList: this.list,
			}
			try {
				let res = await this.$service.ThreeCodeToOne.throughOut(data);

				if (res.success) {
					this.$Toast("放行成功！");
					this.list=[];
						this.model.slot = 1;
						this.model.lotName = '';
						this.model.durableText = '';
						this.model.durableName = '';
						this.model.lotQuantity = '';
						this.model.capacity = '';
					
				}else{
					this.$Toast(res.msg);
				}

			} catch (error) {
				console.error("提交失败:", error);
				this.$Toast(error.msg);
			}
		},
		scan(key) {
			// #ifdef H5
			switch (key) {
				case 'durableName':
					this.model.durableName = '7859696401*ATD00518'
					break;
					case 'lotName':
						this.model.lotName = '01DCB1111111111111111113'
						break;
				default:
					break;
			}
			// #endif
			//#ifdef APP-PLUS
			uni.scanCode({
				success: (res) => {
					switch (key) {
						case 'durableName':
							this.model.durableName = res.result
							break;
						case 'lotName':
							this.model.lotName = res.result
							break;
						default:
							break;
					}
				},
			})
			// #endif
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

.list {
	margin-top: 10px;
}

.item {
	padding: 10px;
	background: #f2f2f2;
	margin-bottom: 5px;
}
</style>
