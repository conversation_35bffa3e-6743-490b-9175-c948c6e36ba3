<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="极卷卸卷" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备号描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineDescription }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.poleRollLotName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(m)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.poleRollLoadingQuantity }} </view>
        </u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.poleRollLoadingTime ? moment(model.poleRollLoadingTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view>
        </u-form-item>

        <u-form-item label="剩余数量(m)" borderBottom required labelWidth="130">
          <view class="w100x flex right">
            <u--input @input="inputChange($event, 'poleRollQuantity')" v-model="model.poleRollQuantity" border="none" type="number"></u--input>
          </view>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="cancel"></u-modal>
    </view>

    <view class="btnContainer" @click="submit">卸卷</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        poleRollQuantity: '剩余数量不能为空',
      },
      model: {},
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      select: false,
      show: false,
      content: ''
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    moment,
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        lotName: null, //	出货牌号	string	
        machineDescription: null, //	设备描述	string	
        machineName: null, //	设备名	string	
        machinePolarity: null, //	设备极性	string	
        poleRollLoadingLotName: null, //	上卷条码	string	
        poleRollLoadingQuantity: null, //上卷数量	number	
        poleRollLoadingTime: null, //上卷时间	string(date-time)	
        poleRollLotName: null, //极卷条码	string	
        poleRollQuantity: null, //	剩余数量	number	
        processOperationName: null, //工序	string	
        productSpecDesc: null, //产品描述	string	
        productSpecName: null, //	产品编码	string	
        productOrderName: null, //	工单号	string
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.poleRollQuantity > this.model.poleRollLoadingQuantity) {
        return this.$Toast('剩余数量不能大于上卷数量！')
      } else if (this.model.poleRollQuantity < 0) {
        return this.$Toast('剩余数量不能小于0！')
      }

      uni.showModal({
        title: '提示',
        content: `设备${this.model.machineName}，现卸极卷${this.model.poleRollLotName},剩余数量：${this.model.poleRollQuantity}，请确认！`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            let params = {
              ...this.model
            }
            this.$service.Polar.PoleRollUnLoading(params).then(res => {
              this.$Toast('卸卷成功!')
              this.initModel()
            })
          }
          if (res.cancel) { }
        },
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      let params = {
        machineName: value
      }
      this.$service.Polar.getMachineDataCheck(params).then(res => {
        if (res.success && res.message) {
          this.show = true
          this.content = res.message
        } else {
          this.model = res.datas[0]
        }
      }).catch(() => {
        this.model.machineName = null
        this.initModel()
      })
    },
    async confirm() {
      this.show = false
      let params = {
        machineName: this.model.machineName
      }
      try {
        let res = await this.$service.Polar.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.model.machineName = null
        this.initModel()
      }
    },
    cancel() {
      this.show = false
      this.model.machineName = null
    },
    inputChange(e, type) {
      e = e && (e.match(/^\d*(\.?\d{0,3})/g)[0])
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'YN-GZ-WG-ZP-401'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
