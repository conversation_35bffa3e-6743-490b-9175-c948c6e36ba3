<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="极卷烘烤开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="烘烤机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入烘烤机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="烘烤机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item v-if="!showSelect" label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.processOperationName }} </view>
        </u-form-item>

        <u-form-item v-if="showSelect" label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="clickProcessOperationName">
            <u--input readonly v-model="model.processOperationName" border="none" placeholder="请选择"></u--input>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="烘洞号" borderBottom required labelWidth="130">
          <u--input v-model="model.portName" border="none" placeholder="请扫描或输入烘洞号" @focus="focusEvent('portName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('portName')"></view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom required labelWidth="130">
          <u--input v-model="model.lotName" border="none" placeholder="请扫描或输入极卷条码" @focus="focusEvent('lotName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.description : '' }} </view>
        </u-form-item>

        <u-form-item label="已扫码极卷数" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ lotList.length }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changePortName = this.$debounce(this.changePortName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        machineName: '烘烤机编号不能为空',
        portName: '烘洞号编号不能为空',
      },
      model: {},
      columns: [],
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showSelect: false,
      ProcessOperationNameList: [],
      lotList: []
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.portName': {
      handler(res) {
        this.changePortName(res)
      }
    },
    'model.lotName': {
      handler(res) {
        this.changeLotName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 烘烤机
        machineSpecDesc: null, // 烘烤机描述
        processOperationName: null, // 工序
        portName: null, // 烘洞号
        lotName: null, // 极卷条码
        ovenTime: null, // 烘烤总时间
        description: null,
        productSpecName: null
      }
    },
    selectFirm(e) {
      this.model.processOperationName = e.value[0]
      this.select = false
    },

    clickProcessOperationName() {
      this.select = true
      this.columns = this.ProcessOperationNameList
    },

    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.lotList.length == 0) {
        return this.$Toast('请先扫描极卷条码!')
      }
      let params = {
        ...this.model,
        lotList: this.lotList
      }
      this.$service.bake.startOven(params).then(res => {
        if (res.success) {
          this.$Toast('烘烤开始成功!')
          this.lotList = []
          this.initModel()
        }
      })
    },

    /* 烘烤机 */
    async changeMachineName(value) {
      if (!value) return
      this.columns = []
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.bake.queryPdaMachineName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的烘烤机编码!')
          }
          this.model = res.datas[0]
          this.model.machineName = value
          this.ProcessOperationNameList = this.model.processOperationName.split(',')
          if (this.ProcessOperationNameList.length > 1) {
            this.showSelect = true
            this.model.processOperationName = this.ProcessOperationNameList[0]
          } else {
            this.showSelect = false
          }
          if (this.model.parameter) {
            this.model.ovenTime = JSON.parse(this.model.parameter).ovenTime
          }
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 扫烘洞编码 */
    async changePortName(value) {
      if (!value) return
      let params = {
        machineName: this.model.machineName,
        portName: value
      }
      let res = await this.$service.bake.queryPdaByPortName(params)
      if (res.success) {
        if (res.datas.length == 0) {
          this.model.portName = ''
          return this.$Toast('请扫描正确的烘洞编码!')
        }
        if (res.datas[0].loggedInTime) {
          this.model.portName = ''
          return this.$Toast(`烘洞编号${value}已开始烘烤!`)
        }
        this.model.portName = value
        this.model.polarity = res.datas[0].polarity
        if (res.datas[0].parameter) {
          this.model.ovenTime = JSON.parse(res.datas[0].parameter).ovenTime
        }
        this.$service.bake.queryPdaByLotList(params).then(res => {
          if (res.success) {
            this.lotList = res.datas.map(item => {
              return {
                eventTime: item.eventTime,
                lotName: item.lotName
              }
            })
          }
        })
      }
    },
    // 扫描极性
    async changeLotName(value) {
      if (!value) return
      let params = {
        machineName: this.model.machineName,
        lotName: value
      }
      try {
        let res = await this.$service.bake.queryPdaByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) return this.$Toast('极卷编码不存在！')

          if (res.datas[0].processOperationName != this.model.processOperationName) {
            this.model.lotName = ''
            this.$Toast(`已扫码极卷的工序${res.datas[0].processOperationName}与烘烤的工序${this.model.processOperationName}不一致！`)
            return
          }

          if (res.datas[0].workShop != this.model.workShop) {
            this.model.lotName = ''
            this.$Toast(`已扫码极卷的工段${res.datas[0].workShop}与扫码极卷的工段${this.model.workShop}不一致！`)
            return
          }

          if (!res.datas[0].cardName) {
            this.model.lotName = ''
            this.$Toast(`没找到合适的工艺卡！`)
            return
          }

          if (res.datas[0].parameter) {
            let ovenTime = JSON.parse(res.datas[0].parameter).ovenTime
            if (this.lotList.length > 0 && ovenTime != this.model.ovenTime) {
              this.model.lotName = ''
              this.$Toast(`已扫码极卷的烘烤总时间${ovenTime}与扫码极卷的烘烤总时间${this.model.ovenTime}不一致！`)
              return
            } else {
              this.model.ovenTime = ovenTime
            }
          }

          this.model.lotName = res.datas[0].lotName
          this.model.workShop = res.datas[0].workShop
          this.model.polarity = res.datas[0].polarity
          this.model.productSpecName = res.datas[0].productSpecName
          this.model.description = res.datas[0].description
          if (this.lotList.findIndex(item => item.lotName == value) > -1) {
            let index = this.lotList.findIndex(item => item.lotName == value)
            this.lotList.splice(index, 1)
            this.$Toast('极卷条码解绑成功!')
          } else {
            let obj = {
              eventTime: new Date().valueOf(),
              lotName: value
            }
            this.lotList.push(obj)
            this.$Toast('极卷条码绑定成功!')
          }
          this.model.lotName = ''
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },

    gotoQuery() {
      if (this.lotList.length == 0) return
      uni.navigateTo({
        url: `/pages/bake/bakeStart/modules/SweptVolume?portName=${this.model.portName}&machineName=${this.model.machineName}&lotList=${JSON.stringify(this.lotList)}`,
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.ZKKX.03.01'
          break;
        case 'portName':
          this.model.portName = '1'
          break;
        case 'lotName':
          this.model.lotName = '1000073'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'saveNo') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
