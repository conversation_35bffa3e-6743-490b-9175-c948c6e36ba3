<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="查询结果" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <view class="flex flex_wrap fs12">
            <view class="flex w100x h24 hcenter c_999">
              <view class="w60 nowrap">条码号:</view>
              <view class="nowrap c_00b17b"> {{ ele.batchId }} </view>
            </view>

            <view class="flex w100x h24 hcenter c_999">
              <view class="w60 nowrap">工单:</view>
              <view class="nowrap c_00b17b"> {{ ele.productOrderName }} </view>
            </view>

            <view class="flex w50x h24 hcenter c_999">
              <view class="w60">条码类型:</view>
              <view> {{ ele.lotTypeText }} </view>
            </view>

            <view class="flex w50x h24 hcenter c_999">
              <view class="w60">物料编码:</view>
              <view> {{ ele.productSpecName }} </view>
            </view>
            
            <view class="flex w100x lin24 c_999">
              <view class="w60 nowrap">物料名称:</view>
              <view class="c_00b17b"> {{ ele.productSpecText }} </view>
            </view>

            <view class="flex w50x h24 hcenter c_999">
              <view class="w60 nowrap">订单:</view>
              <view class="nowrap"> {{ ele.workOrderName }} </view>
            </view>

            <view class="flex w50x h24 hcenter c_999">
              <view class="w60">工段:</view>
              <view> {{ ele.workShopText }} </view>
            </view>
            
            <view class="flex w50x h24 hcenter c_999">
              <view class="w60">产线:</view>
              <view class="nowrap"> {{ ele.areaText }} </view>
            </view>
          </view>

          <view class="flex betweeen mt5">
            <u-button class="mr10" type="success" text="展开下级" :disabled="ele.hasChild !== 'Y'" @click="expandDetail(ele)"></u-button>
            <u-button type="success" text="查看明细" @click="detail(ele)"></u-button>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>
        <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      // model: Array.from({ length: 20 }, (v, i) => i),
      model: [],
      paramsOption: {},
      statementType: ''
    };
  },

  onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    // this.pageTitle = nlsMap.detailTitle // 标题
    this.nlsMap = nlsMap
    this.paramsOption = options && JSON.parse(options.params)
    this.statementType = options.statementType
    console.log('paramsOption', this.statementType,options, this.paramsOption);
    this.initSearchModel()
    this.getData()
  },
  methods: {
    expandDetail(ele) {
      let obj = {
        ...ele
      }
      uni.navigateTo({
        url: `/pages/ProductionExecution/PDARetrospectiveReport/searchChildren?params=${JSON.stringify(obj)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}&statementType=${this.statementType}`,
      })
    },
    detail(ele) {
      let obj = {
        ...ele,
      }
      uni.navigateTo({
        url: `/pages/ProductionExecution/PDARetrospectiveReport/detail?params=${JSON.stringify(obj)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
      })
    },
    initSearchModel() {
      let batchIdList = []
      if (this.paramsOption.batchIdList.length > 0) {
        batchIdList = [].concat(this.paramsOption.batchIdList)
      }
      this.searchModel = {
        pageNo: this.pageNumber,
        limit: this.pageSize,
        ... this.paramsOption,
        statementType: this.statementType,
        batchIdList: batchIdList
      }
    },
    getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))


      this.$service.PDARetrospectiveReport.just(params).then((res) => {
        if (res && res.success) {
          this.model = clearOldData ? res.datas[0].records : [...this.model, ...res.datas[0].records]
          if (this.searchModel.pageNo == res.datas[0].pages) {
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';
</style>