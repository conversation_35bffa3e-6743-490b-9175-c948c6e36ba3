<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切下料—删除不良" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备" borderBottom required labelWidth="100">
          <u--input readonly v-model="model.machineName" border="none"></u--input>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.productSpecName }} </view>
        </u-form-item>

        <u-form-item label="产品描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="弹夹" required borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.durableName }} </view>
        </u-form-item>

        <u-form-item label="弹夹容量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.capacity }} </view>
        </u-form-item>

        <u-form-item label="批次ID" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <u-form-item label="设备计数(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ computedQty }} </view>
        </u-form-item>

        <u-form-item label="人工确认(片)" borderBottom labelWidth="130">
          <u--input readonly v-model="model.uesrConfirm" border="none"></u--input>
        </u-form-item>

        <u-form-item required label="报废数量(片)" borderBottom labelWidth="120">
          <view class="w100x flex right"> {{ model.ngQuantity }} </view>
        </u-form-item>

        <view v-for="(item, index) in GetReasonCodeTypeList" :key="index" class="mb10 br10 bc_fff pa10 b_dcdee2_dashed">
          <u-form-item label="不合格代码" labelWidth="100">
            <view class="w100x flex right">
              <view>{{ item.reasonCode }}</view>
            </view>
          </u-form-item>
          <u-form-item label="不合格描述" labelWidth="100">
            <view class="w100x flex right">
              <view>{{ item.reasonCodeDesc }}</view>
            </view>
          </u-form-item>
          <u-form-item label="数量(片)" labelWidth="130">
            <u--input type="number" v-model="item.ngQuantity" border="none"></u--input>
          </u-form-item>
          <view class="btn" @click="submit(item)">删除</view>
        </view>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="productRequestName" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        uesrConfirm: '人工确认不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      GetReasonCodeTypeList: [],
      uesrConfirm: null,
      computedQty: null
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad(e) {
    this.initModel()
    this.model.machineName = e && e.machineName
    this.computedQty = e && e.computedQty
    this.model.processOperationName = e && e.processOperationName
    this.model.lotName = e && e.lotName
    this.uesrConfirm = e && e.uesrConfirm
    this.GetReasonCodeList()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    leftClick() {
      uni.$emit('updateData', { machineName: this.model.machineName, uesrConfirm: this.uesrConfirm })
    },
    initModel() {
      this.model = {
        machineName: null, // 设备
        machineSpecDesc: null, // 设备描述
        productSpecName: null, // 产品编码
        productSpecDesc: null, // 产品描述
        durableName: null, // 弹夹
        capacity: null, // 弹夹容量
        lotName: null, // 批次号
        uesrConfirm: null, // 人工确认
        ngQuantity: null, // 报废数量
        productQuantity: null, // 良品数量
        computedQty: null, // 计算数量
        processOperationName: '',
        lotName: ''
      }
    },

    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确的搅拌机编号')
      }
      this.focusObj.saveNo = false
      this.select = true
    },
    selectFirm(e) {
      if (e.value[0].reasonCodeType) {
        this.model.reasonCodeType = e.value[0].reasonCodeType
        this.GetReasonCode(this.model.reasonCodeType)
      } else {
        this.model.reasonCode = e.value[0].reasonCode
      }
      this.select = false
    },
    submit(item) {
      let params = {
        ...item,
        machineName: this.model.machineName,
        lotName: this.model.lotName,
        processOperationName: this.model.processOperationName
      }
      this.$service.DieCutting.DieCuttingDeleteNgLotRecord(params).then(res => {
        this.$Toast('删除成功！')
        this.changeMachineName(this.model.machineName)
        this.GetReasonCodeList()
      })
    },

    /* 搅拌机 */
    changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value
      }
      this.$service.DieCutting.DieCuttingByMachineName(params).then(res => {
        this.model = res.datas[0]
        this.model.computedQty = 100
        if (!this.model.uesrConfirm) this.model.uesrConfirm = this.uesrConfirm
      }).catch(() => {
        this.model.machineName = null
      })
    },
    GetReasonCodeList() {
      const params = {
        machineName: this.model.machineName,
        lotName: this.model.lotName,
        processOperationName: this.model.processOperationName
      }
      this.$service.DieCutting.DieCuttingNgLotRecordList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'GF20-01-FQ01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/uform.scss';
@import '../../../../styles/publicStyle.scss';
.btn {
  margin: 0 auto;
  height: 34px;
  line-height: 34px;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 15px;
  text-align: center;
  border-radius: 11px;
}
</style>
