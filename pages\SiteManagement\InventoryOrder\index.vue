<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>

    <view class="myContainer ma10">
      <scroll-view class="h100x" refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in model" :key="index">
          <view class="flex between h40 hcenter c_999">
            <view>盘点单号</view>
            <view>{{ ele.inventoryOrderNo }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>盘点仓库</view>
            <view>{{ $utils.optionShowConfig(ele.stockCode, ele.stockText) }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>盘点类型</view>
            <view>{{ ele.inventoryTypeText }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>状态</view>
            <!-- <view>{{ $utils.optionShowConfig(ele.state, ele.stateText) }}</view> -->
            <view :class="getClass(ele)" class="pa5 c_fff">{{ ele.stateText }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view>盘点周期</view>
            <view class="fs12">
              {{ ele.startDate ? moment(ele.startDate).format('YYYY-MM-DD') : '' }} -
              {{ ele.endDate ? moment(ele.endDate).format('YYYY-MM-DD') : '' }}
            </view>
          </view>

          <!-- <view class="flex between h40 hcenter c_999">
            <view>盘点开始时间</view>
            <view> {{ ele.endDate ? moment(ele.endDate).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
          </view> -->

          <view class="flex between h40 hcenter c_999">
            <view>创建人</view>
            <view>{{ ele.createUser }}</view>
          </view>
          <view class="flex between h40 hcenter c_999">
            <view>创建时间</view>
            <view> {{ ele.createTime ? moment(ele.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
          </view>
          <view>
            <u-button type="success" text="详情" @click="summit(ele)"></u-button>
          </view>
        </view>
        <NoData v-if="!model || model.length === 0"></NoData>

        <!-- <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" /> -->
      </scroll-view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import moment from 'moment'
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin, useNls],
  components: {
    NoData,
  },
  data() {
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {


        detailTitle: '详情',
      },
      model: [],
      // model: Array.from({ length: 20 }, (v, i) => i),
      dicts: {
        InventoryOrderStateList: [],
      },
    };
  },

  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.initSearchModel()

    await this.getEnumValue('InventoryOrderState', 'InventoryOrderStateList') // 明细状态
    this.getData()
  },
  methods: {
    moment,
    getClass(item, index) {
      let config = {
        NotStart: 'bc_409eff', // "未开始"
        Processing: 'bc_e4db43',// 进行中
        Finish: 'bc_00b17b', // 已完成
      }
      return config[item.state]
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    summit(ele) {
      uni.navigateTo({
        url: `/pages/SiteManagement/InventoryOrder/detail?params=${JSON.stringify(ele)}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))} `,
      })
    },
    initSearchModel() {
      this.searchModel = {
        page: this.pageNumber,
        size: this.pageSize,
        ... this.paramsOption
      }
    },
    returnRefresh() {
      this.getData(true, true)
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.page = this.pageNumber
      this.searchModel.size = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))

      this.$service.InventoryOrder.ListInventoryOrder().then((res) => {
        if (res && res.success) {
          // this.model = res.datas
          // this.status = 'nomore'
          this.model = res.datas
          this.refresherTriggered = false
        }
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.bc_409eff {
  background: #409eff;
}

.bc_e4db43 {
  background: #e4db43;
}

.bc_00b17b {
  background: #00b17b;
}
</style>