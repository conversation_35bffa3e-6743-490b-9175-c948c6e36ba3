<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="涂布工序-单面开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="涂布机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入涂布机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="涂布机描述" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processOperationName ? model.processOperationName + ' / ' + model.processOperationNameDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + ' / ' + model.productSpecDesc : '' }} </view>
        </u-form-item>

        <u-form-item label="配方号" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.consumableSpecName ? model.consumableSpecName + ' / ' + model.bomcomponentDesc : '' }}
          </view>
        </u-form-item>

        <!-- <u-form-item label="气动泵" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.portName }} </view>
        </u-form-item> -->

        <u-form-item label="单面出货牌号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.lotName }} </view>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">物料信息</view>

        <u-form-item label="储蓄罐" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.carrierName }} </view>
          <u-icon class="ml2" @click="gotoQuery(1)" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
		<u-form-item label="理论可产出数量" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.producibleQuantity }}&nbsp;米 </view>
        </u-form-item>
        <u-form-item label="上料信息" borderBottom labelWidth="100">
          <view class="w100x flex right"> 其他原材料 </view>
          <u-icon class="ml2" @click="gotoQuery(2)" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">工艺卡信息</view>

        <u-form-item label="工艺卡编号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.cardName }} </view>
        </u-form-item>

        <u-form-item label="工艺卡版本号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.cardRevision }} </view>
        </u-form-item>
      </u--form>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import PrintPackageMixin from "@/mixins/printPackage";
export default {
  mixins: [PrintPackageMixin],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '涂布机编号不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      focusObj: {
        materialPosition: false
      },
    }
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 涂布机
        machineSpecDesc: null, // 涂布机描述
        productOrderName: null, // 工单号
        productSpecName: null, // 产品编码
        consumableSpecName: null, // 配方号
        carrierName: '', // 储蓄编码
        createTime: null, // 完工时间
        plannedOutPut: null, // 数量
        // portName: null, // 气动泵
        lotName: null, // 单面出货牌号
        // portName: null, // 气动泵
        cardName: null, // 工艺卡编码
        cardRevision: null // 工艺卡版本
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model,
        operationType: 'SinglerCoating'
      }
      this.$service.Size.startSingleCoating(params).then(res => {
        if (res.success) {
          this.$Toast('操作成功！')
          this.initModel()
          setTimeout(() => {
            this.printPackage(res.datas[0], 'Label') // 打印
          }, 800);
        }
      })
    },

    /* 涂布机 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value,
        operationType: 'Coating'
      }
      try {
        let res = await this.$service.Size.getDeassignInfoByMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          if (!this.model.carrierName) {
            this.$Toast(`涂布机${this.model.machineName}没有绑定储蓄罐!`)
            this.initModel()
            return
          }
          if (!this.model.processOperationName) {
            this.$Toast(`涂布机${this.model.machineName}没有绑定工序!`)
            this.initModel()
            return
          }
          if (!this.model.machineSpecDesc) {
            this.$Toast(`涂布机不存在!`)
            this.initModel()
            return
          }
          // 查询工艺卡信息
          if (this.model.machineName && this.model.processOperationName && this.model.productSpecName) {
            this.getOperationCard()
          }
        } else {
          this.model.machineName = ''
          this.$Toast('未找到涂布机信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 查询工艺卡 */
    async getOperationCard() {
      let params = {
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
        productSpecName: this.model.productSpecName
      }
      try {
        let res = await this.$service.Size.GetOperationCard(params)
        if (res.success && res.datas.length > 0) {
          let { cardName, cardRevision } = res.datas[0]
          this.$set(this.model, 'cardName', cardName)
          this.$set(this.model, 'cardRevision', cardRevision)
        } else {
          this.$Toast(`工艺卡信息不存在!`)
          this.initModel()
        }
      } catch (error) {
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'GF20-01-TBD01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
    gotoQuery(type) {
      if (type == 1) {
        if (!this.model.machineName || !this.model.carrierName) return this.$Toast(`涂布机或储蓄机不存在!`)
        uni.navigateTo({
          url: `/pages/Coating/coatingStart/modules/MaterialInformation?carrierName=${this.model.carrierName}&lotName=${this.model.lotName}`,
        })
      } else {
        if (!this.model.machineName) return this.$Toast(`涂布机不存在!`)
        uni.navigateTo({
          url: `/pages/Coating/coatingStart/modules/loadingInformation?machineName=${this.model.machineName}&processOperationName=${this.model.processOperationName}`,
        })
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
