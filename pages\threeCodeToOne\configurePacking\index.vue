<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-input type="text" v-model="model.serchNo" placeholder="输入搜索关键词" style="margin-bottom: 10px;" />
        <u-form-item label="物料编码" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('consumableName')">
            <view v-if="model.consumableName">{{ $utils.filterObjLabel(dicts.packNcList, model.consumableName) }}
            </view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5"
              :style="{ transform: select && selectType === 'consumableName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
        <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm"
          @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
        <!-- <u-form-item label="物料名称" labelWidth="120">
          <view class="w100x flex right"> {{ model.consumableNameDesc }}</view>
        </u-form-item> -->
        <!-- 动态渲染表单 -->
        <u-form-item v-for="(item, index) in configList" :key="index" :label="item.configName" borderBottom
          labelWidth="250">
          <!-- 1️⃣ u-switch (开关) -->
          <u-switch v-if="item.configType === 'SWITCH'" v-model="model[item.configKey]" :activeValue="1"
            :inactiveValue="0" activeColor="#2979ff">
          </u-switch>


          <!-- 2️⃣ u-select (下拉选择) -->
          <!-- <u-select v-else-if="item.configType === 'SELECT'" v-model="model[item.configKey]"
            :list="getSelectOptions(item.configKey)"></u-select> -->

          <!-- 3️⃣ u-input (输入框) -->
          <u-input v-else-if="item.configType === 'TEXT'" v-model="model[item.configKey]"></u-input>

          <!-- 4️⃣ 其他情况 -->
          <view v-else> 未知组件类型: {{ item.configType }} </view>
        </u-form-item>
      </u--form>

    </view>
    <view class="btnContainer" @click="saveConfig">保存</view>
    <view class="btnContainer" @click="reservConfig">{{ btnText }}</view>
    <view class="btnContainer" @click="deleteCache">清除缓存</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import moment from 'moment'
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin, PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.debounceChangeSerchNo = this.$debounce(this.changeSerchNo, 300);
    this.changeconsumableName = this.$debounce(this.changeconsumableName, 1000)
    return {
      btnText: '初始化',
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      columns: [],
      select: false,
      selectType: '',
      trayNoParmas: {},
      model: {},
      list1: Array.from({ length: 20 }, (v, i) => i),
      selectList: [],
      dicts: {
        packNcList: [],
        packNcListAll: [],
      },
      configList: [],

    }
  },
  computed: {
  },
  watch: {
    'model.serchNo': {
      handler(val) {
        this.debounceChangeSerchNo(val)
      },
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.initModel()
    this.init_getPackNc()
  },
  methods: {
    async changeconsumableName(value) {
      this.loadConfigData(value)
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'consumableName':
          this.columns = this.dicts.packNcList
          break;
        default:
          break;
      }
    },
    getSelectOptions(enumname) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        return this.selectList = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })

    },
    init_getPackNc() {
      this.$service.ThreeCodeToOne.getPackNc().then(res => {
        this.dicts.packNcList = res.datas.map(item => ({
          label: item.value + "/" + item.text,
          value: item.value,
          text: item.text,
        }))
        this.dicts.packNcListAll = JSON.parse(JSON.stringify(this.dicts.packNcList));
      })

    },
    changeSerchNo(val) {
      if (val === "") {
        this.dicts.packNcList = this.dicts.packNcListAll;
      } else {
        this.dicts.packNcList = this.dicts.packNcListAll.filter(item =>
          item.label.includes(val)
        );
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.$set(this.model, "consumableNameDesc", e.value[0].text)
      this.select = false
      if (this.selectType == 'consumableName') {
        this.loadConfigData(this.model.consumableName)
      }
    },
    async loadConfigData(consumableName) {
      if (!consumableName) {
        return
      }
      try {

        let data = {
          consumableName: this.model.consumableName,
        }
        let res = await this.$service.ThreeCodeToOne.getPackingConfig(data)

        if (res.data.length === 0) {
          this.btnText = '初始化'
        } else {
          this.btnText = '恢复默认设置'
        }
        // 解析数据
        this.configList = res.data;

        // 初始化 model 数据
        res.data.forEach(item => {
          let value = item.configValue;

          if (item.configType === 'SWITCH') {
            value = value === "1" || value === "true" || value === true ? 1 : 0;
          }
          this.model.consumableNameDesc = item.consumableNameDesc
          this.$set(this.model, item.configKey, value);
        });
        console.log("get", this.model)
      } catch (error) {
        console.error("加载配置失败", error);
      }
    },
    async deleteCache() {
      if (!this.model.consumableName) {
        return
      }
      let data = {
        consumableName: this.model.consumableName,
      }
      let res = await this.$service.ThreeCodeToOne.deleteCache(data)

      this.$Toast("清除成功！");
    },
    async saveConfig() {
      console.log(this.configList)
      if (this.configList.length === 0) {
        return
      }
      try {

        let dataList = this.configList.map(item => ({
          consumableName: this.model.consumableName, // 物料编码
          configKey: item.configKey,                 // 配置项
          configValue: this.model[item.configKey]    // 配置值
        }));
        let params = {
          data: dataList
        };

        let res = await this.$service.ThreeCodeToOne.updatePackingConfig(params)
        if (res.success) {
          this.loadConfigData(this.model.consumableName)
          this.$Toast("保存成功！");
        }

      } catch (error) {
        console.error("保存失败", error);
        this.$Toast("保存失败！");
      }
    },
    async reservConfig() {
      if (!this.model.consumableName) {
        return
      }
      try {

        let params = {
          consumableName: this.model.consumableName,
        };

        let res = await this.$service.ThreeCodeToOne.reservConfig(params)
        if (res.success) {
          this.loadConfigData(this.model.consumableName)
          this.$Toast("重置成功");
        }

      } catch (error) {
        console.error("重置失败", error);
        this.$Toast("重置失败！");
      }
    },
    initModel() {
      this.model = {
        consumableName: '',
        consumableNameDesc: '',
        serchNo: '',
      }
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let parmas = {
        trayNo: this.model.trayNo,
        trayReturnType: this.model.trayReturnType,
        returnConsumableItemList: this.list,
      }
      this.$service.TrayReturnController.submit(parmas).then(res => {
        this.$Toast('操作成功')
        this.model.trayNo = ''
        this.model.consumableName = ''
        this.list = []
        if (res.datas.length > 0) {
          setTimeout(() => {
            this.myprintPackage(res.datas) // 打印
          }, 800);
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'consumableName':
          this.model.consumableName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
