<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="搅拌工序-投料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="搅拌机" prop="machineName" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请扫描或输入搅拌机" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="搅拌机描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDescription" border="none"></u--input>
        </u-form-item>

        <u-form-item label="极性" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machinePolarity" border="none"></u--input>
        </u-form-item>

        <u-form-item label="出货牌号" borderBottom labelWidth="100">
          <u--input readonly v-model="model.lotName" border="none"></u--input>
        </u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">投料信息</view>

        <u-form-item label="计划浆料产出(kg)" borderBottom labelWidth="140"> <u--input readonly v-model="model.plannedOutPut" border="none"></u--input></u-form-item>

        <u-form-item label="工艺标准产出(kg)" borderBottom labelWidth="140"> <u--input readonly v-model="model.standardOutPut" border="none"></u--input></u-form-item>

        <u-form-item label="投料比" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.feedRatio }}
          </view>
        </u-form-item>

        <u-form-item label="投料搅拌进度" borderBottom labelWidth="120">
          <u--input readonly v-model="model.currentWorkStep" border="none"></u--input>
        </u-form-item>

        <u-form-item label="已投总重量(kg)" borderBottom labelWidth="140"> <u--input readonly v-model="model.feededWeight" border="none"></u--input></u-form-item>

        <view class="lin40 fs16 pl20 c_00b17b">待投料信息</view>

        <u-form-item label="物料编码" borderBottom labelWidth="120">
          <view class="w100x flex right c_00b17b">
            {{ model.consumableSpecName }}
          </view>
          <u-icon class="ml2" @click="gotoQuery(1)" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-form-item label="投料标准(kg)" borderBottom labelWidth="120"> <u--input readonly v-model="model.feedStandard" border="none"></u--input></u-form-item>

        <u-form-item label="投料公差(kg)" borderBottom labelWidth="120"> <u--input readonly v-model="model.feedTolerance" border="none"></u--input></u-form-item>

        <u-form-item label="还需投入数量(kg)" borderBottom labelWidth="150">
          <view class="w100x flex right c_00b17b">
            {{ model.needFeedWeight }}
          </view>
          <u-icon class="ml2" @click="gotoQuery(2)" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-form-item label="投料" borderBottom required labelWidth="100">
          <u--input v-model="model.feedingConsumableName" border="none" placeholder="请扫描或输入物料标签条码" @focus="focusEvent('feedingConsumableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('feedingConsumableName')"></view>
        </u-form-item>

        <u-form-item label="投入重量(kg)" required labelWidth="130">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="model.feedingConsumableWeight" border="bottom" type="number"></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>

    <view class="btnContainer2">
      <view @click="Feeding(1)">继续投料</view>
      <view @click="Feeding(2)">投料完成</view>
    </view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID, LOCALHOST_PRINT } from '@/utils/common/evtName.js'
export default {
  mixins: [RedScan],
  name: 'stirFeeding',
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeFeedingConsumableName = this.$debounce(this.changeFeedingConsumableName, 1000)
    return {
      rulesTip: {
        machineName: '搅拌机编码不能为空',
        productQuantity: '物料标签条码不能为空',

        lotWeight: '产出电芯计量数量不能为空',
      },
      model: {},
      machineNameFlag: false,
      checkMachineOutput: false, // 是否需要校验IOT设备上报产出数量
      ratio: null,  // 单位转换
      ratioFlag: false,
      show: false,
      content: '',
      type: ''
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.feedingConsumableName': {
      handler(val) {
        this.changeFeedingConsumableName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        machineName: null, // 设备编号
        machineDescription: null, // 搅拌机描述:
        machinePolarity: null, // 极性
        lotName: null, // 出货牌号
        plannedOutPut: null, // 计划浆料产出:
        standardOutPut: null, // 工艺标准产出
        feedRatio: null, // 投料比
        currentWorkStep: null, // 投料搅拌进度,
        feededWeight: null,// 已投总重量
        consumableSpecName: null, // 产品编码
        feedStandard: null, // 投料标准
        feedTolerance: null, // 投料公差
        needFeedWeight: null, //还需投入数量
        feedingConsumableName: null, // 投料 
        feedingConsumableWeight: null, // 投入重量 
      }
    },
    onSkip(url) {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      uni.navigateTo({
        url: url
      })
    },
    /* 设备编号 */
    async changeMachineName(value) {
      if (!value) return

      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.materialunFeeding.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
        }
      } catch (error) {
        // this.model.machineName = null
        this.initModel()
      }
    },
    async changeFeedingConsumableName(value) {
      if (!value) return
      if (!this.model.machineName) {
        this.model.feedingConsumableName = ''
        return this.$Toast('未扫描设备号！')
      }
      let params = {
        consumableName: value,
        needFeedConsumableSpec: this.model.consumableSpecName
      }
      try {
        let res = await this.$service.materialunFeeding.validateConsumable(params)
        if (res.success) {

        }

      } catch (error) {
        this.model.feedingConsumableName = null
      }
    },
    dataHandle(orginData, ratioData) {
      if (this.model[orginData] || this.model[orginData] == '0') {
        this.model[ratioData] = parseInt(this.$utils.calculate_mul(this.model[orginData], this.ratio))
      } else {
        this.model[ratioData] = null
      }
    },

    intChange(e, type) {
      if (e) {
        let str = e && e + ""
        let result = str && (str.match(/^\d*/g)[0])
        this.$nextTick(() => {
          this.$set(this.model, type, result)
        })
      }
    },

    inputChange(e, type) {
      e = e && (e.match(/^\d*(\.?\d{0,2})/g)[0])
      if (this.ratioFlag) {
        if (type === 'productQuantity') {
          this.dataHandle('productQuantity', 'lotWeight')
        }
        if (type === 'lossProductQuantity') {
          this.dataHandle('lossProductQuantity', 'lossLotWeight')
        }
      }
      this.$nextTick(() => {
        this.$set(this.model, type, e)
      })
    },


    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA01'
          break;
        case 'feedingConsumableName':
          this.model.feedingConsumableName = 'TEST001'
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'feedingConsumableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
    gotoQuery(type) {
      if (type == 1) {
        uni.navigateTo({
          url: `/pages/MixingProcess/modules/usableMaterial?consumableSpecName=${this.model.consumableSpecName}`,
        })
      } else {
        uni.navigateTo({
          url: `/pages/MixingProcess/modules/usedMaterial?currentWorkStep=${this.model.currentWorkStep}&lotName=${this.model.lotName}&machineName=${this.model.machineName}&processOperationName=${this.model.processOperationName}&consumableSpecName=${this.model.consumableSpecName}`,
        })
      }
    },
    // 投料
    async Feeding(type) {
      this.type = type
      const params = {
        consumableName: this.model.feedingConsumableName
      }
      let res = await this.$service.materialunFeeding.validateFIFO(params)
      if (!res.datas[0]) {
        this.show = true
        this.content = '标签条码不是最先接收条码是否继续投料?'
      } else {
        this.confirm()
      }
    },
    confirm() {
      // 继续投料
      if (this.type == 1) {
        this.readData()
      } else { // 投料完成
        this.submit()
      }
    },
    // 投料完成
    submit() {
      const params = {
        ...this.model
      }
      this.$service.materialunFeeding.finishFeedMsgProcessor(params).then(res => {
        if (res.success) {
          this.$Toast('投料完成！')
          this.show = false
          // this.initModel()
          this.changeMachineName(this.model.machineName)
        }
      })
    },
    // 继续投料
    readData() {
      const params = {
        ...this.model
      }
      this.$service.materialunFeeding.mixingFeedMsgProcessor(params).then(res => {
        if (res.success) {
          this.changeMachineName(this.model.machineName)
          this.model.feedingConsumableName = ''
          this.model.feedingConsumableWeight = ''
          this.$Toast('投料成功！')
          this.show = false
        }
      })
    }

  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>