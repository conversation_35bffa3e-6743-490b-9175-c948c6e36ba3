<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="电芯烘烤结束" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="烘烤机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入烘烤机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="烘烤机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item v-if="!showSelect" label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.processOperationName }} </view>
        </u-form-item>

        <u-form-item v-if="showSelect" label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="clickProcessOperationName">
            <u--input readonly v-model="model.processOperationName" border="none" placeholder="请选择"></u--input>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="烘洞号" borderBottom required labelWidth="130">
          <u--input :focus="portNameFocus" @blur="portNameFocus = false" v-model="model.portName" border="none" placeholder="请扫描或输入烘洞号" @focus="focusEvent('portName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('portName')"></view>
        </u-form-item>

        <!-- <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.description : '' }} </view>
        </u-form-item> -->

        <u-form-item label="已扫码载具数" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ lotList.length }} </view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-form-item label="烘烤开始时间" borderBottom labelWidth="130"
          ><view class="w100x flex right"> {{ model.createTime ? moment(model.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view></u-form-item
        >

        <u-form-item label="已烘烤时长" borderBottom labelWidth="140">
          <view class="w100x flex right">
            <view
              ><span>{{ hours }}</span
              ><span class="lin1 ml2">h</span>
            </view>
            <view class="ml30"
              ><span>{{ minutes }}</span
              ><span class="lin1 ml2">min</span>
            </view>
          </view>
        </u-form-item>
        <!-- <div class="fs16 fb ml22 mt10" style="color: #02a7f0">
          <span>产品参数录入</span>
        </div>
        <view v-if="lotList.length > 0">
          <view v-for="(item, index) in lotList" :key="index" class="b_dcdee2_dashed mt10">
            <u-form-item label="极卷条码:" borderBottom labelWidth="160"> <u--input v-model="item.lotName" border="none"></u--input></u-form-item>
            <u-form-item required label="极片水分测试温度:" borderBottom labelWidth="160"> <u--input v-model="item.temperature" border="none"></u--input><span class="lin1 ml2">℃</span></u-form-item>
            <u-form-item required label="极片水分含量:" borderBottom labelWidth="160"> <u--input v-model="item.content" border="none"></u--input><span class="lin1 ml2">ppm</span></u-form-item>
          </view>
        </view>
        <NoData v-if="!lotList || lotList.length === 0"></NoData> -->
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="labelText" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="checkTimeOut">结束</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import moment from 'moment'
export default {
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changePortName = this.$debounce(this.changePortName, 1000)
    return {
      rulesTip: {
        machineName: '烘烤机编号不能为空',
        portName: '烘洞号编号不能为空',
      },
      model: {},
      columns: [],
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      lotList: [],
      hours: 0,
      minutes: 0,
      showSelect: false,
      ProcessOperationNameList: [],
      time: null,
      portNameFocus: false,
      standardTime: '1'
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    'model.portName': {
      handler(res) {
        this.changePortName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
    this.time = null
    this.hours = 0
    this.minutes = 0
  },
  methods: {
    moment,
    focusEvent(type) {
      // this.model[type] = ''
    },
    getColor() {
      if (this.hours >= this.model.ovenTime) {
        if (this.minutes > 0) {
          return 'color: red;'
        } else {
          return 'color: #303133'
        }
      } else {
        return 'color: red'
      }
    },
    getRemainingTime(val) {
      let etime = moment(new Date()).valueOf();
      let stime = val
      let usedTime = etime - stime;  //两个时间戳相差的毫秒数
      this.hours = parseInt((usedTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      this.minutes = parseInt((usedTime % (1000 * 60 * 60)) / (1000 * 60));
    },
    initModel() {
      this.model = {
        machineName: null, // 烘烤机
        description: null, // 烘烤机描述
        processOperationName: null, // 工序
        portName: null, // 烘洞号
        createTime: null
        // lotName: null, // 极卷条码
        // loggedInTime: null, // 烘烤开始时间
        // ovenTime: null, // 烘烤总时间
        // description: null,
        // productSpecName: null,
        // lotNote: null // 当前烘烤时长
      }
    },
    checkTimeOut() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.lotList.length == 0) {
        return this.$Toast('请先扫描极卷条码!')
      }
      console.log('this.standardTime', this.standardTime);
      if (this.standardTime == '0') {
        this.submit()
      } else {
        let createTime = moment(this.model.createTime).format('YYYY-MM-DD HH:mm:ss')
        let nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        let diffInMinutes = moment(nowTime).diff(moment(createTime), 'minutes')
        let standardTimeMin = this.standardTime * 60
        console.log('diffInMinutes standardTimeMin',diffInMinutes ,standardTimeMin);
        if (diffInMinutes < standardTimeMin) {
          uni.showModal({
            title: '提示',
            content: `烘烤时长不满足工艺卡烘烤总时长设定，请确认是否要结束烘烤？`,
            cancelText: '取消',
            confirmText: '确认',/* 只可以4个字 */
            cancelColor: '#666',
            confirmColor: '#409eff',
            success: (res) => {
              if (res.confirm) {
                this.submit()
              }
              if (res.cancel) { }
            },
          })
        } else {
          this.submit()
        }
      }
    },
    submit() {
      // this.model.lotNote = (this.hours * 60) + this.minutes // 当前烘烤时长min
      let params = {
        ...this.model,
        durableList: this.lotList
      }
      this.$service.bakeCell.endOven(params).then(res => {
        if (res.success) {
          this.$Toast('烘烤结束成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 烘烤机 */
    async changeMachineName(value) {
      if (!value) return
      this.columns = []
      let params = {
        machineName: value,
        menuName: 'Oven'
      }
      try {
        let res = await this.$service.bakeCell.getMachineInfo(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的烘烤机编码!')
          }
          this.model = res.datas[0]
          this.model.machineName = value
          this.model.description = res.datas[0].description
          this.ProcessOperationNameList = this.model.processOperationName.split(',')
          if (this.ProcessOperationNameList.length > 1) {
            this.showSelect = true
            this.model.processOperationName = this.ProcessOperationNameList[0]
          } else {
            this.showSelect = false
          }
          if (this.model.parameter) {
            this.model.ovenTime = JSON.parse(this.model.parameter).ovenTime
          }
          this.portNameFocus = true
        }
      } catch (error) {
        this.model.machineName = null
      }
    },
    /* 扫烘洞编码 */
    async changePortName(value) {
      if (!value) return
      let params = {
        machineName: this.model.machineName,
        portName: value
      }
      let res = await this.$service.bakeCell.checkPort(params)
      if (res.success) {
        if (res.datas.length == 0) {
          this.model.portName = ''
          return this.$Toast('请扫描正确的烘洞编码!')
        }
        if (res.datas[0].checkResult) {
          this.$Toast(`烘洞${this.model.portName}未开始烘烤`)
          this.model.portName = ''
          this.lotList = []
          return
        }
        this.model.portName = value
        this.model.createTime = res.datas[0].createTime
        this.hours = Math.floor(res.datas[0].finishTime / 60)
        this.minutes = res.datas[0].finishTime % 60
        this.$service.bakeCell.getAssignCarrierInfo({
          machineName: this.model.machineName,
          portName: this.model.portName,
        }).then(res => {
          if (res.success) {
            this.standardTime = res.datas[0] && res.datas[0].standardTime
            this.lotList = res.datas.map(item => {
              return {
                // eventTime: item.eventTime,
                // lotName: item.lotName,
                // temperature: '',
                // content: ''
                ...item
              }
            })
          }
        })
        // this.model.lotName = res.datas[0].lotName
        // this.model.portName = value
        // this.model.polarity = res.datas[0].polarity
        // this.model.loggedInTime = res.datas[0].loggedInTime
        // this.model.productSpecName = res.datas[0].productSpecName
        // this.model.description = res.datas[0].description
        // if (this.model.loggedInTime) {
        //   this.getRemainingTime(this.model.loggedInTime)
        //   this.time = null
        //   this.time = setInterval(() => {
        //     this.getRemainingTime(this.model.loggedInTime)
        //   }, 60000);
        // }
        // if (res.datas[0].parameter) {
        //   this.model.ovenTime = JSON.parse(res.datas[0].parameter).ovenTime
        // }
        // this.$service.bake.queryPdaByLotList(params).then(res => {
        //   if (res.success) {
        //     this.lotList = res.datas.map(item => {
        //       return {
        //         eventTime: item.eventTime,
        //         lotName: item.lotName,
        //         temperature: '',
        //         content: ''
        //       }
        //     })
        //   }
        // })
      }
    },

    clickProcessOperationName() {
      this.select = true
      this.columns = this.ProcessOperationNameList
    },

    // 选择
    async selectFirm(e) {
      this.model.processOperationName = e.value[0]
      this.select = false
    },

    gotoQuery() {
      if (this.lotList.length == 0) return
      uni.navigateTo({
        url: `/pages/bake/bakeStartCell/modules/SweptVolume?portName=${this.model.portName}&machineName=${this.model.machineName}&lotList=${JSON.stringify(this.lotList)}`,
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.ZKKX.03.01'
          break;
        case 'portName':
          this.model.portName = 'PORT01'
          break;
        case 'lotName':
          this.model.lotName = '1000073'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'portName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
