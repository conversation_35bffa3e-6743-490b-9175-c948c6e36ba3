<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="涂布工序-双面开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="涂布机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="涂布机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.processOperationName }} </view>
        </u-form-item>

        <u-form-item label="上卷条码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.poleRollName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(m)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.poleRollQuantity }} </view>
        </u-form-item>
		
		<u-form-item label="理论可产出数量" borderBottom labelWidth="130">
		  <view class="w100x flex right"> {{ model.producibleQuantity }}&nbsp;米 </view>
		</u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="100">
          <view class="w100x flex right">{{ model.poleRollMountTime ? moment(model.poleRollMountTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.productSpecName }} </view>
        </u-form-item>

        <!-- <u-form-item label="分切条数" borderBottom required labelWidth="130">
          <view class="w100x flex right">
            <u--input v-if="model.slittingQuantity == 1" v-model="model.slittingQuantity" border="none" readonly type="number"></u--input>
            <u--input v-else v-model="model.slittingQuantity" border="none" type="number"></u--input>
          </view>
        </u-form-item> -->

        <view class="lin40 fs16 pl20 c_00b17b">工艺卡信息</view>

        <u-form-item label="工艺卡编号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.cardName }} </view>
        </u-form-item>

        <u-form-item label="工艺卡版本号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.cardRevision }} </view>
        </u-form-item>
      </u--form>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import moment from 'moment'
import PrintPackageMixin from "@/mixins/printPackage";
export default {
  mixins: [PrintPackageMixin],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        // slittingQuantity: '分切条数不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    moment,
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        cardName: null, //	工艺卡名	
        cardRevision: null, //工艺卡版本	
        description: null, //	设备描述	
        factoryName: null, //工厂	
        machineName: null, //	设备名	
        poleRollMountTime: null, //上卷时间
        poleRollName: null, //上卷条码	
        poleRollQuantity: null, //	上卷数量
        processOperationName: null, //	工序	
        productOrderName: null, //工单号	
        productSpecName: null, //	产品编码	
        slittingQuantity: null, //分切条数
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model,
        operationType: 'Coating'
      }
      this.$service.Polar.singlerRollStartMsgProcessor(params).then(res => {
        this.$Toast('操作成功！')
        this.initModel()
        setTimeout(() => {
          this.printPackage(res.datas[0], 'Label') // 打印
        }, 800);
      })
    },

    /* 设备信息 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false

      this.columns = []
      let params = {
        machineName: value,
        operationType: 'Coating'
      }
      try {
        let res = await this.$service.Polar.getMachineDataForStart(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
          this.model.machineName = value
          if (this.model.operationType && this.model.operationType != 'Coating') {
            this.$Toast('涂布机不存在!')
            this.initModel()
            return
          }
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.model.machineName = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA02'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
