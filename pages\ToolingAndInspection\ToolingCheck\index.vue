<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="工装校准" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="130">
        <u-form-item label="工装" borderBottom required labelWidth="130">
          <u--input v-model="model.durableName" border="none" focus placeholder="请扫描或输入" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>
        <u-form-item label="描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>
        <u-form-item label="上次校准时间" labelWidth="130">
          <view class="w100x flex right"> {{ model.lastCalibrationTime }} </view>
        </u-form-item>
        <u-form-item label="工装校准履历" borderBottom labelWidth="120">
          <view class="w100x flex right"></view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">校准完成</view>
  </view>
</template>

<script>
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        durableName: '工装不能为空',
      },
      model: {},
    }
  },
  watch: {
    'model.durableName': {
      handler(val) {
        this.changeDurableName(val)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        durableName: null, // 冲壳机
        description: null, // 冲壳机描述
        lastCleanTime: null
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        durableName: this.model.durableName
      }
      this.$service.ToolingAndInspection.finishCalibration(params).then(res => {
        if (res.success) {
          this.$Toast('校准完成!')
          this.initModel()
        }
      })
    },

    /* 工装 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        durableName: value,
      }
      try {
        let res = await this.$service.ToolingAndInspection.getDurableDataFirst(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.durableName = ''
            return this.$Toast('请扫描正确的工装编码!')
          }
          this.model = res.datas[0]
          if (this.model.durableSpecName) {
            this.$service.ToolingAndInspection.DurableSpecApi({
              durableSpecName: this.model.durableSpecName
            }).then(data => {
              if (data.datas.length > 0) {
                this.model.description = data.datas[0].description
              }
            })
          }
        }
      } catch (error) {
        this.model.durableName = null
        this.model.description = null
      }
    },
    gotoQuery() {
      if(!this.model.durableName) {
        this.$Toast('请先填写工装号！')
        return
      }
      uni.navigateTo({
        url: `/pages/ToolingAndInspection/ToolingCheck/modules/ToolingCheckList?durableName=${this.model.durableName}`,
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'durableName':
          this.model.durableName = 'Tray002'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
