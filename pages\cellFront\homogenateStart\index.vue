<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="匀浆开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备编号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" placeholder="请填写" focus @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="100">
          <u--input readonly v-model="model.machineDesc" border="none"></u--input>
        </u-form-item>

        <u-form-item label="浆料种类" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.spliceTypeValue }}
          </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="产线" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.areaName, model.areaDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="工单" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect">
            <view>{{ selectProduct.productRequestName }}</view>
            <u-icon name="arrow-down" color="black" size="18" ></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="时间" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ selectProduct.createTime }}
          </view>
        </u-form-item>

        <u-form-item label="产品" labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(selectProduct.productSpecName, selectProduct.productSpecDesc) }}
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" @confirm="selectFirm" @cancel="select = false" @close="select = false" keyName="productRequestName" :closeOnClickOverlay="true"></u-picker>
    </view>
    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import RedScan from "@/mixins/RedScan";
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  name: 'homogenateStart',
  mixins: [RedScan],
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000) // 消抖
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        productRequestName: '工单不能为空',
      },
      model: {
        machineName: null, // 设备编号
        machineDesc: null, // 设备描述
        spliceTypeValue: null,// 种类
        processOperationName: null, // 工序编码
        processOperationDesc: null, // 工序描述
        areaName: null, // 产线编码
        areaDesc: null, // 产线描述
        productRequestName: null, //工单

      },
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
    };
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  computed: {
    selectProduct() {
      let obj = {}
      if (this.model.productRequestName) {
        obj = this.columns.find(item => item.productRequestName === this.model.productRequestName)
      }
      return obj
    }
  },
  methods: {
    checkSelect() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      this.select = true
    },
    /* 设备编号 */
    async changeMachineName(value) {
      console.log('value', value);
      if (!value) return
      this.machineNameFlag = false
      this.model.machineDesc = null
      this.model.spliceTypeValue = null
      this.model.processOperationName = null
      this.model.processOperationDesc = null
      this.model.areaName = null
      this.model.areaDesc = null
      this.model.productRequestName = null


      this.columns = []
      let params = {
        operateNo: '10', // 后台区分固定字段
        machineName: value,
      }
      try {
        let res = await this.$service.homogenate.trackIn(params)
        this.machineNameFlag = true
        this.model = Object.assign(this.model, res.data)
        this.columns = res.data.trackInProductRequestList
      } catch (error) {
        this.model.machineName = null
      }

    },
    selectFirm(e) {
      this.$set(this.model, 'productRequestName', e.value[0].productRequestName)
      this.select = false
    },
    submit() {
      if (!this.machineNameFlag) {
        return this.$Toast('请输入或扫描正确设备编号')
      }
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let dto = {
        actionId: 'TrackInForStock', // 消息名称
        menuId: 'TrackInForStock', // 消息名称
        requestId: `${new Date().getTime()}`, // 时间戳
        reqMap: {
          userId: this.$getLocal(USER_ID), // 登陆人
          DS_TIBCO_REPLY: [],
          // 暂定写死
          DS_TIBCO_MAIN: [
            {
              DEST_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_SUBJECT: 'L8SFAB_HT_JCMGR',
              REPLY_VALUE: 'GET_RV_XMLDATA',
              MAX_TIME: '5',
            },
          ],
          langCd: '',
          DS_TIBCO_MSG: [
            {
              COMMAND_ID: 'TrackInForStock', // 消息名称
              FILE_ID: 'TrackInForStock', // 消息名称
              MESSAGE_NAME: 'TrackInForStock', // 消息名称
              TRANSACTION_ID: `${new Date().getTime()}`, // 时间戳
              EVENT_USER: this.$getLocal(USER_ID), // 登陆人
              EVENT_COMMENT: 'TrackInForStock', // 功能名称
              REPLYSUBJECT_NAME: 'TrackInForStock', // 界面名称
              TERMINAL: 'PDA', // PDA请求，WEB请求
              // FACTORYNAME:'',
              PROCESSOPERATIONNAME: this.model.processOperationName,
              MACHINENAME: this.model.machineName,
              PRODUCTREQUSET: this.selectProduct.productRequestName
            },
          ],
          DS_TIBCO_REPEAT: [],
        },
      }
      this.$service.common.commonExec(dto).then(res => {
        if (res.success) {
          this.$Toast('操作成功')
          this.machineNameFlag = false
          this.model.machineName = null
          this.model.machineDesc = null
          this.model.spliceTypeValue = null
          this.model.processOperationName = null
          this.model.processOperationDesc = null
          this.model.areaName = null
          this.model.areaDesc = null
          this.model.productRequestName = null
        }
      })
    },

    scan(key) {
      // #ifdef H5
      this.$set(this.model, 'machineName', 'C1Z001001')
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })

      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>