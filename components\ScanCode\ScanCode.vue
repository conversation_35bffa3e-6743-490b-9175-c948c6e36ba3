<template>
  <view class="content"></view>
</template>

<script>
var main, receiver, filter
var _codeQueryTag = false
export default {
  data() {
    return {
      scanCode: '',
    }
  },
  created: function (option) {
    // #ifndef H5
    this.initScan()
    this.startScan()
    // #endif
  },
  onHide: function () {
    // #ifndef H5
    this.stopScan()
    // #endif
  },
  destroyed: function () {
    // #ifndef H5
    this.stopScan()
    // #endif
  },
  methods: {
    initScan() {
      let _this = this
      main = plus.android.runtimeMainActivity()
      var IntentFilter = plus.android.importClass(
        'android.content.IntentFilter'
      )
      console.log(IntentFilter)
      filter = new IntentFilter()
      //下面的addAction内改为自己的广播动作
      filter.addAction('com.yt.action.scan')
      receiver = plus.android.implements(
        'io.dcloud.feature.internal.reflect.BroadcastReceiver',
        {
          onReceive: function (context, intent) {
            plus.android.importClass(intent)
            //下面的getStringExtra内改为自己的广播标签
            let code = intent.getStringExtra('text')
            console.log(code)
            _this.queryCode(code)
          },
        }
      )
    },
    startScan() {
      main.registerReceiver(receiver, filter)
    },
    stopScan() {
      main.unregisterReceiver(receiver)
    },
    queryCode: function (code) {
      if (_codeQueryTag) return false
      _codeQueryTag = true
      setTimeout(function () {
        _codeQueryTag = false
      }, 150)
      var id = code
      console.log('id:', id)
      uni.$emit('scan', {
        code: id,
      })
    },
  },
}
</script>