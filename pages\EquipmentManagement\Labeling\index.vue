<template>
  <view class="myContainerPage bc_f3f3f7">
    <!-- <u-navbar title="贴标" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar> -->
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <u-tabs
      ref="uTabs"
      :list="tabList"
      :current="current"
      lineWidth="80"
      lineColor="#01bfbf"
      :scrollable="false"
      :activeStyle="{ color: '#333', fontWeight: 'bold' }"
      :inactiveStyle="{ color: '#ccc', transform: 'scale(1)' }"
      :itemStyle="{ height: '80rpx', width: `calc(100% / ${tabList.length})` }"
      @change="tabsChange"
    >
    </u-tabs>
    <view class="mt10 myContainer">
      <labeling2 v-if="current === 0" :nlsMap="nlsMap"></labeling2>
      <labeling v-if="current == 1" :nlsMap="nlsMap"></labeling>
      <unbind v-if="current == 2" :nlsMap="nlsMap"></unbind>
    </view>
  </view>
</template>

<script>
import useNls from "@/mixins/useNls";
import labeling2 from './component/labeling2.vue'
import labeling from './component/labeling.vue'
import unbind from './component/unbind.vue'
export default {
  name: 'rollerStart',
  mixins: [useNls],
  components: {
    labeling2,
    labeling,
    unbind,
  },
  data() {
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },

      tabList: [
        {
          name: '贴标2',
        },
        {
          name: '贴标',
        },
        {
          name: '解标',
        },
      ],
      current: 1, // tab
      // swiperCurrent: 0, // 滑块swipe
    };
  },


  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    // this.initModel()
  },

  methods: {
    tabsChange(e) {
      this.current = e.index
    },


  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
</style>