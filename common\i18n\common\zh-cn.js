export default {
	common: {
		button: {
			confirm: '确认',
			cancel: '取消',
			back: '返回',
		},
		form: {
			pleaseSelect: '请选择',
			pleaseEnter: '请输入',
			PleaseScanOrEnter: '请扫描或输入',
		},
		tip: {
			cannotEmpty: '不能为空'
		},
	},
	menu: {
		MaterialManage:'物料管理',
		InStorageSearch:'扫码入库',
		OutStorageSearch:'扫码出库',
		EquipmentManagement: "设备管理",
		SetProductionModel: "设备换型设置",
		EquipmentCleaning: "设备清洗",
		EquipmentCheck: "设备校准",
		printSearch: "补打查询",
		Tuning: "调机",
		MaterialLoading: "材料上料",
		LoadingMaterial: "材料上料",
		UnloadingMaterial: "材料卸料",
		MaterialReceipt: "物料接收",
		Polar: "极卷上卷",
		PolarRollUp: "极卷_上卷",
		PolarCombined: "合卷_上卷",
		PolarUnload: "极卷_卸卷",
		Production: "搅拌工序",
		stirStart: "搅拌_开始",
		stirFeeding: "搅拌_投料",
		stirStir: "搅拌_搅拌",
		stirProduce: "搅拌_产出",
		stirExceptionHandling: "搅拌_异常处理",
		Size: "涂布工序",
		sizeDesignation: "浆料_指定",
		sizeUnbinding: "浆料_解绑",
		coatingStart: "单面开始",
		coatingSingleDown: "单面完工",
		coatingDoubleStart: "双面开始",
		coatingDoubleDown: "双面完工",
		roll_In: "辊压工序",
		roll_InStart: "辊压_开始",
		roll_InDown: "辊压_完工",
		roll_InStartSecond: "二次辊压_开始",
		roll_InDownSecond: "二次辊压_完工",
		Slitting: "分切工序",
		slittingStart: "分切开始",
		slittingDown: "分切完工",
		bake: "烘烤工序",
		bakeStart: "极卷_开始",
		bakeEnd: "极卷_结束",
		bakeStartCell: "电芯_开始",
		bakeEndCell: "电芯_结束",
		DieCutting: "模切工序",
		DieCutting_RollUp: "模切_上卷",
		DieCutting_Begin: "模切_开始",
		DieCutting_Blanking: "模切_下料",
		DieCutting_Unroll: "模切_卸卷",
		stand: "静置工序",
		standStart: "静置_开始",
		standEnd: "静置_结束",
		semiManufacturesBindingAndUnbinding: "在制品绑定与解绑载具",
		BindingAndUnbinding: "绑定_解绑",
		punchingShell: "冲壳与半成品载具上卸料",
		shellOnoadMaterial: "冲壳_上料",
		unloadMaterial: "冲壳_卸料",
		LoadingSemiFinished: "半成品_上料",
		UnloadingSemiFinished: "半成品_卸料",
		QualityControl: "质量管理",
		MaterialRecord: "材料记录",
		MaterialDetermination: "材料判定",
		SemiRecord: "在制品记录",
		SemiDetermination: "在制品判定",
		FirstInspectionTask: "首检任务",
		FirstInspectionDone: "首检待办",
		FirstInspectionSample: "首检取样",
		FirstInspectionParams: "首检参数",
		FirstInspectionDecision: "首检判定",
		InspectionTask: "巡检任务",
		InspectionDone: "巡检待办",
		InspectionSample: "巡检取样",
		InspectionParams: "巡检参数",
		InspectionDecision: "巡检判定",
		ToolingAndInspection: "工装",
		ToolingClean: "工装清洗",
		ToolingCheck: "工装校准",
		ToolingInstall: "工装安装",
		ToolingUnload: "工装卸载",
		ProcessAdjustment: "工序调整",
		WorkingProcessAdjustment: "在制_调整",
		SingleVolumeStart_END: "单卷开始_下卷",
		SingleVolumeStart: "单卷_开始",
		coatingVolume: "完工_下卷"
	}
}