<template>
  <view class="bc_f3f3f7 listPage">
    <u-navbar title="浆料物料信息查询" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="topContainer bc_999 br12 ma10">
      <view class="flex h40 hcenter c_999">
        <view>储存罐:</view>
        <view class="ml6">{{ carrierName }}</view>
      </view>
      <view class="fs16 c_00b17b mt10">浆料物料信息</view>
    </view>
    <view class="listContainer ma10">
      <view class="mb10 br10 bc_fff pa10">
        <view class="flex between h40 hcenter c_999">
          <view>浆料出货牌号</view>
          <view>{{ searchModel.consumableName }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>配方号</view>
          <view>{{ searchModel.consumableSpecName ? searchModel.consumableSpecName + ' / ' + searchModel.bomcomponentDesc : '' }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>产出数量(kg)</view>
          <view>{{ searchModel.createQuantity }}</view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>当前数量(kg)</view>
          <view class="flex hcenter">
            <view>{{ searchModel.quantity }}</view>
          </view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>浆料完工时间</view>
          <view class="flex hcenter">
            <view>{{ moment(searchModel.createTime).format('YYYY-MM-DD HH:mm:ss') }}</view>
          </view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>指定操作人员</view>
          <view class="flex hcenter">
            <view>{{ searchModel.eventUser }}</view>
          </view>
        </view>
        <view class="flex between h40 hcenter c_999">
          <view>浆料指定时间</view>
          <view class="flex hcenter">
            <view>{{ moment(searchModel.appointTime).format('YYYY-MM-DD HH:mm:ss') }}</view>
          </view>
        </view>
      </view>
      <view @click="goTop">
        <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
      </view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      carrierName: '',
      lotName: ''
    };
  },

  onLoad(e) {
    this.carrierName = e && e.carrierName
    this.lotName = e && e.lotName
    this.getData()
  },
  methods: {
    moment,
    getData() {
      this.searchModel = {}
      let params = {
        carrierName: this.carrierName,
        lotName: this.lotName
      }
      this.$service.Size.GetFeedingmaterial(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.searchModel = res.datas[0]
          }
        }
      })
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../../styles/publicStyle.scss';
</style>