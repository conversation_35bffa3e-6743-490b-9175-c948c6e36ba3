<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="myContainer ma5">
      <view class="ml5 flex between flex_shrink0" style="color: #409eff">
        <view> 检验类型: {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }}</view>
      </view>
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <!-- <u-form-item label="检验类型" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ InspectionTypes }}/ {{ $utils.filterObjLabel(dicts.InspectionTypesList, InspectionTypes) }} </view>
        </u-form-item> -->
        <!-- {{ model }}========={{ dicts }} -->
        <template v-if="InspectionTypes !== 'ProdcutWarehouseInspection'">
          <u-form-item label="设备号" borderBottom required labelWidth="100">
            <u--input :readonly="!lodash.isEmpty(options_Params)" v-model="model.machineName" border="none" placeholder="请扫描或输入设备号"></u--input>
            <view v-if="lodash.isEmpty(options_Params)" class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>

          <u-form-item label="设备号描述" borderBottom labelWidth="100">
            <u--input readonly v-model="model.machineDesc" border="none"></u--input>
          </u-form-item>

          <!-- <u-form-item label="工序" borderBottom labelWidth="100">
            <view class="w100x flex right">
              {{ $utils.optionShowConfig(model.processOperationName, model.processOperationDesc) }}
            </view>
          </u-form-item> -->

          <u-form-item label="工序" required borderBottom labelWidth="100">
            <view class="w100x flex right" @click="checkSelect('processOperationName')">
              <view v-if="model.processOperationName">{{ $utils.filterObjLabel(dicts.processOperationNameList, model.processOperationName) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" v-if="lodash.isEmpty(options_Params)" :style="{ transform: select && selectType === 'processOperationName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>
        </template>

        <template v-if="InspectionTypes == 'ProdcutWarehouseInspection'">
          <u-form-item label="FQC检验类型" required borderBottom labelWidth="120">
            <view class="w100x flex right" @click="checkSelect('fqcInspectType')">
              <view v-if="model.fqcInspectType">{{ $utils.filterObjLabel(dicts.fqcInspectTypeList, model.fqcInspectType) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" v-if="lodash.isEmpty(options_Params)" :style="{ transform: select && selectType === 'fqcInspectType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </u-form-item>

          <u-form-item label="条码号" borderBottom labelWidth="100">
            <template v-if="model.fqcInspectType">
              <u--input :readonly="!lodash.isEmpty(options_Params)" v-model="model.warehousingNo" border="none" placeholder="请扫描"></u--input>
              <view v-if="lodash.isEmpty(options_Params)" class="iconfont icon-saoma" @click="scan('warehousingNo')"></view>
            </template>
          </u-form-item>
        </template>

        <u-form-item label="工单" borderBottom required labelWidth="100">
          <template v-if="!lodash.isEmpty(options_Params)">
            <view class="w100x flex right">
              {{ model.productOrderName }}
            </view>
          </template>
          <template v-else>
            <view class="w100x flex right" @click="checkSelect('productOrderName')">
              <view v-if="model.productOrderName">{{ $utils.filterObjLabel(dicts.productOrderNameList, model.productOrderName) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" v-if="lodash.isEmpty(options_Params)" :style="{ transform: select && selectType === 'productOrderName' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </template>
        </u-form-item>

        <u-form-item label="任务编码" required borderBottom labelWidth="120">
          <template v-if="!lodash.isEmpty(options_Params)">
            <view class="w100x flex right">
              {{ model.taskNo }}
            </view>
          </template>
          <template v-else>
            <view class="w100x flex right" @click="checkSelect('taskNo')">
              <view v-if="model.taskNo">{{ $utils.filterObjLabel(dicts.taskNoList, model.taskNo) }}</view>
              <view class="c_c0c4cc" v-else>请选择</view>
              <view class="ml5" v-if="lodash.isEmpty(options_Params)" :style="{ transform: select && selectType === 'taskNo' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
                <u-icon name="arrow-down"></u-icon>
              </view>
            </view>
          </template>
        </u-form-item>

        <u-form-item label="任务创建方式" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.createTypeDictText }} </view>
        </u-form-item>

        <u-form-item label="任务状态" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.inspectStateDictText }} </view>
        </u-form-item>

        <u-form-item label="样品条码" borderBottom labelWidth="100">
          <!-- 有任务编号才显示 -->
          <template v-if="model.taskNo">
            <u--input v-model="model.lotName" border="none" placeholder="请扫描样品条码"></u--input>
            <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
          </template>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.productSpecName, model.productSpecDesc) }}
          </view>
        </u-form-item>

        <u-form-item label="已取样品信息" borderBottom labelWidth="120">
          <view class="w100x flex right">{{ list.length }}</view>
          <u-icon class="ml2" @click="gotoQuery" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>

        <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">取样完成</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeWarehousingNo = this.$debounce(this.changeWarehousingNo, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      lodash: _,
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      rulesTip: {
        // machineName: '设备编号不能为空',
        // taskNo: '任务编码不能为空',
        // lotName: '样品条码不能为空',
      },
      model: {},
      ngLotItemList: [],
      columns: [],
      select: false,
      selectType: '',
      dicts: {
        InspectionTypesList: [], // 检验类型
        processOperationNameList: [], // 工序列表
        fqcInspectTypeList: [],  // Fqc 检验类型
        productOrderNameList: [], // Fqc 工单下拉
        taskNoList: [], // 任务编码
      },
      InspectionTypes: '',
      options_Params: {},
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.InspectionSampleList;
      },
      set(value) {
        this.$store.commit('setInspectionSampleList', value);
      }
    },
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
    // FQC 检验
    'model.warehousingNo': {
      handler(val) {
        this.changeWarehousingNo(val)
      }
    },
    'model.lotName': {
      handler(val) {
        this.changeLotName(val)
      }
    },
  },
  async onLoad(options) {
    this.initModel()
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.InspectionSamplingTitle // 标题
    this.nlsMap = nlsMap
    this.InspectionTypes = options.InspectionTypes
    this.options_Params = options.params && JSON.parse(options.params)
    console.log('options_Params', this.options_Params);
    // 取样跳转过来的
    if (!_.isEmpty(this.options_Params)) {
      let { processOperationName, fqcInspectType, warehousingNo, machineName, taskNo, createTypeDictText, inspectStateDictText, workOrderName } = this.options_Params

      this.model.processOperationName = processOperationName
      this.model.machineName = machineName
      this.model.fqcInspectType = fqcInspectType
      this.model.warehousingNo = warehousingNo
      // 任务列表带过来的
      this.model.taskNo = taskNo
      this.model.productOrderName = workOrderName
      this.model.createTypeDictText = createTypeDictText
      this.model.inspectStateDictText = inspectStateDictText
    }
    this.getEnumValue('InspectionTypes', 'InspectionTypesList') // 单据类型
    this.getEnumValue('FqcInspectType', 'fqcInspectTypeList') // FQC检验类型
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('getInspectionTaskList')
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      let params = {
        inspectType: this.InspectionTypes,
        machineName: value,
      }
      try {
        let res = await this.$service.QualityManagement.getMachineForInspection(params)
        this.model.machineDesc = res.datas[0].machineDesc
        // this.model.processOperationName = res.datas[0].processOperationName
        // this.model.processOperationDesc = res.datas[0].processOperationDesc
        this.dicts.productOrderNameList = res.datas[0].productOrderNameList.map((item) => ({
          label: item,
          value: item,
        }))
        this.dicts.processOperationNameList = res.datas[0].processOperationList.map((item) => ({
          label: item.processOperationText,
          value: item.processOperationName,
        }))
        if (!_.isEmpty(this.options_Params)) {
          return
        } else {
          // init
          this.model.taskNo = ''
          this.dicts.taskNoList = []
          this.model.productSpecName = ''
          this.model.productSpecDesc = ''
          this.model.inspectStateDictText = ''
          this.model.createTypeDictText = ''
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    //  FQC 检验
    async changeWarehousingNo(value) {
      if (!value) return
      try {
        let params = {
          inspectType: this.InspectionTypes,
          fqcInspectType: this.model.fqcInspectType,
          warehousingNo: value,
          workOrderName: this.model.productOrderName,
        }
        let res = await this.$service.QualityManagement.getMachineForInspection(params)
        this.dicts.productOrderNameList = res.datas[0].productOrderNameList.map((item) => ({
          label: item,
          value: item,
        }))
      } catch (error) {
        this.initModel()
      }
    },


    /* 获取任务列表 */
    async getInspectionTaskList() {
      this.model.createTypeDictText = ''
      this.model.inspectStateDictText = ''
      this.model.taskNo = ''
      this.dicts.taskNoList = []
      this.model.productSpecName = ''
      this.model.productSpecDesc = ''
      this.list = []
      let params = {
        inspectType: this.InspectionTypes,
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
        // FQC
        warehousingNo: this.model.warehousingNo,
        fqcInspectType: this.model.fqcInspectType,
        workOrderName: this.model.productOrderName,
      }
      try {
        let res = await this.$service.QualityManagement.getInspectionTaskList(params)
        if (res.datas.length > 0) {
          let taskNoList = res.datas.map(item => ({
            ...item,
            value: item.taskNo,
            label: item.taskNo
          }))
          this.dicts.taskNoList = taskNoList.filter(item => item.inspectStateDictText == '待取样' || item.inspectStateDictText == '检验中')
        }
      } catch (error) {
        console.log('error', error);
      }
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    checkSelect(type) {
      if (!_.isEmpty(this.options_Params)) {
        return
      }
      this.select = true
      this.selectType = type
      switch (type) {
        case 'taskNo':
          this.columns = this.dicts.taskNoList
          break;
        case 'fqcInspectType':
          this.columns = this.dicts.fqcInspectTypeList
          break;
        case 'productOrderName':
          this.columns = this.dicts.productOrderNameList
          break;
        case 'processOperationName':
          this.columns = this.dicts.processOperationNameList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'taskNo') {
        this.model.createTypeDictText = e.value[0].createTypeDictText
        this.model.inspectStateDictText = e.value[0].inspectStateDictText
        this.model.productSpecName = ''
        this.model.productSpecDesc = ''
        this.list = []
      }

      if (this.selectType == 'processOperationName') {
        if (this.model.processOperationName && this.model.productOrderName) {
          //   需要条件工序工单都有值
          this.getInspectionTaskList()
        }
      }
      if (this.selectType == 'productOrderName') {
        if (this.InspectionTypes == 'ProdcutWarehouseInspection') {
          //  FQC 没有工序
          this.getInspectionTaskList()
        } else {
          if (this.model.processOperationName && this.model.productOrderName) {
            //   需要条件工序工单都有值
            this.getInspectionTaskList()
          }
        }
      }

    },
    initModel() {
      this.model = {
        warehousingNo: '', // fqc 检验类型
        machineName: '', // 设备编码
        machineDesc: '', // 设备描述
        processOperationName: '', // 工序
        processOperationDesc: '', // 工序描述
        taskNo: '', // 任务编码 
        createTypeDictText: '', // 任务创建方式
        inspectStateDictText: '', // 任务状态
        lotName: '', // 条码号
        productOrderName: '', //  工单号
        productSpecName: '', //  产品编码
        productSpecDesc: '', //  产品描述
      }
      this.dicts.productOrderNameList = []
      this.dicts.processOperationNameList = []
      this.dicts.taskNoList = []
      this.list = []
    },
    gotoQuery() {
      if (!this.model.taskNo) {
        this.$Toast('请先选择任务编码！')
        return
      }
      uni.navigateTo({
        url: `/pages/QualityManagement/InspectionSampling/sampleList?InspectionTypes=${this.InspectionTypes}&taskNo=${this.model.taskNo}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`
      })
    },
    submit() {
      if (this.list.length == 0) {
        this.$Toast('样品条码数量为0,请先扫码取样!')
        return
      }
      let params = {
        inspectType: this.InspectionTypes,
        lotNameList: this.list.map(ele => ele.lotName),
        machineName: this.model.machineName,
        processOperationName: this.model.processOperationName,
        taskNo: this.model.taskNo,
        workOrderName: this.model.productOrderName,
        productSpecName: this.model.productSpecName
      }
      this.$service.QualityManagement.finshSample(params).then(res => {
        this.$Toast('操作成功!')
        if (_.isEmpty(this.options_Params)) {
          setTimeout(() => {
            this.initModel()
          }, 500)
        } else {
          setTimeout(() => {
            this.leftClick()
          }, 1500);
        }
      })
    },


    // 扫描样品条码
    async changeLotName(value) {
      if (!value) return
      let falg = this.list.some(item => item.lotName == value)
      if (falg) {
        this.$Toast('该条码已存在！')
        this.model.lotName = ''
        return
      }
      try {
        let params = {
          fqcInspectType: this.model.fqcInspectType,
          inspectType: this.InspectionTypes,
          lotName: this.model.lotName,
          taskNo: this.model.taskNo,
          machineName: this.model.machineName,
          processOperationName: this.model.processOperationName
        }
        let res = await this.$service.QualityManagement.getLotData(params)
        if (res.datas.length > 0) {
          this.model.productSpecName = res.datas[0].productSpecName
          this.model.productSpecDesc = res.datas[0].productSpecDesc

          this.list.push(res.datas[0])
          this.$Toast('扫描成功')
          setTimeout(() => {
            this.model.lotName = ''
          }, 500);
        } else {
          this.model.lotName = ''
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'NEX-LK1-1F-013' // ALINAK01  ALIMSA01
          break;
        case 'warehousingNo':
          this.model.warehousingNo = 'SCRKM0000012'
          break;
        case 'lotName':
          this.model.lotName = 'Pr20240805000001-2811000001' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
