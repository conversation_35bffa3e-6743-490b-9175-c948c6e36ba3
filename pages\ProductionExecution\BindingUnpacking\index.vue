<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="操作类型" required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('containerBindOrUnBind')">
            <view v-if="model.containerBindOrUnBind">{{ $utils.filterObjLabel(dicts.containerBindOrUnBindList, model.containerBindOrUnBind) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'containerBindOrUnBind' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
export default {
  mixins: [useNls],
  components: {
    NoData,
  },
  data() {
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

        // lineTypeIndex
        page1Title: '单电芯绑盘',
        // machineTypeIndex
        page2Title: '单电芯解盘',

        page3Title: '整托解盘',

        detailTitle: '详情',
      },
      rulesTip: {
        containerBindOrUnBind: '请选择操作类型',
      },
      model: {},
      dicts: {
        containerBindOrUnBindList: [
          // { value: '1', label: '单电芯绑盘', },
          // { value: '2', label: '单电芯解盘', },
          // { value: '3', label: '整托解盘', },
        ],
      },
      columns: [],
      select: false,
      selectType: '',
    }
  },
  computed: {},
  watch: {},
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.getEnumValue('ContainerBindOrUnBind', 'containerBindOrUnBindList') // 单据类型
    this.initModel()
  },
  methods: {
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'containerBindOrUnBind':
          this.columns = this.dicts.containerBindOrUnBindList
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },

    initModel() {
      this.model = {
        containerBindOrUnBind: '1', // 设备号
      }
    },
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },

    // 确定
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.containerBindOrUnBind == '1') {
        uni.navigateTo({
          url: `/pages/ProductionExecution/BindingUnpacking/page1?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        })
      }

      if (this.model.containerBindOrUnBind == '2') {
        uni.navigateTo({
          url: `/pages/ProductionExecution/BindingUnpacking/page2?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        })
      }

      if (this.model.containerBindOrUnBind == '3') {
        uni.navigateTo({
          url: `/pages/ProductionExecution/BindingUnpacking/page3?nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
