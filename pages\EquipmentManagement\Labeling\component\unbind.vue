<template>
  <view class="myContainerPage pl5 pr5 pb5" :class="{ 'error-shake': showErrorEffect }">
    <view class="myContainer">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="扫码区域" borderBottom required labelWidth="100">
          <input v-model="model.lotName" border="none" inputmode="none" :focus="focus_lotName"  @focus="hideKeyboard" style="width: 100%;" placeholder="请扫描"></input>
          <!-- 清除按钮 -->
          <view v-if="model.lotName" class="close-icon-wrapper">
            <u-icon name="close" class="close-icon" color="#999" size="20" @click="handleClear" />
          </view>
          <view class="iconfont icon-saoma" @click="scan('lotName')"></view>
        </u-form-item>
        <u-form-item label="PACK码" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.sourceLotName }}
          </view>
        </u-form-item>
        <u-form-item label="客户码" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.targetLotName }}
          </view>
        </u-form-item>

        <u-form-item label="物料编码" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productSpecName }}
          </view>
        </u-form-item>

        <u-form-item label="物料名称" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productSpecDesc }}
          </view>
        </u-form-item>

        <u-form-item label="生产工单" borderBottom labelWidth="120">
          <view class="w100x flex right">
            {{ model.productOrderName }}
          </view>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">解绑关系</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
import _ from "lodash";
export default {
  name: 'unbind',
  mixins: [],
  props: {
    nlsMap: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      showErrorEffect: '',
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      model: {},
      columns: [],
      select: false,
      selectType: '',
      focus_lotName: true,
      checkCode: 0,
    };
  },
  watch: {
    'model.lotName': {
      handler(val) {
        this.changeLotName(val)
      }
    },
  },
  created() {
    this.initModel()
  },
  mounted() {
  },
  methods: {
    
    hideKeyboard() {
      if (process.env.UNI_PLATFORM === 'h5') {
        // 如果是 web 平台，不执行
        return;
      }
      uni.hideKeyboard(); // 隐藏软键盘
    },
    handleClear() {
    this.model.lotName = '';
    this.focus_lotName = false;
    this.$nextTick(() => {
      this.focus_lotName = true;
    });
  },
    moment,
    initModel() {
      this.model = {
        lotName: '',
        sourceLotName: '',// 当前条码
        targetLotName: '',// 
        productSpecName: '',// 
        productSpecDesc: '',// 
        productOrderName: '',// 生产工单
      }
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'params1':
          // this.columns = this.dicts.params1List
          break;
        default:
          break;
      }
    },
    selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },

    async changeLotName(value) {
      if (!value) return

      // 忽略大小写判断前4位是否为 "pack"
      console.log(value.substring(0, 4).toLowerCase())
      if (value.substring(0, 4).toLowerCase() === 'pack') {
        let params = {
          lotName: value,
        }
        let res1 = await this.$service.ThreeCodeToOne.BindOrUnBind_GetSntrace(params)
        if (res1.success) {
          if (res1.datas.length > 0) {
            this.model.sourceLotName = value
            this.model.targetLotName = res1.datas[0].currentSn
          }
        }
      } else {
        let params = {
          currentSn: value,
        }
        let res1 = await this.$service.ThreeCodeToOne.BindOrUnBind_GetSntrace_ByCurrentSn(params)
        if (res1.success) {
          if (res1.datas.length > 0) {
            this.model.sourceLotName = res1.datas[0].sourceSn
            this.model.targetLotName = value
          }
        }
      }

      try {
        let params = {
          lotName: this.model.sourceLotName,
        }
        let res = await this.$service.Labeling.GetLotInfo(params)
        if (res.datas.length > 0) {
          this.model.productSpecName = res.datas[0].productSpecName
          this.model.productSpecDesc = res.datas[0].productSpecDesc
          this.model.productOrderName = res.datas[0].productOrderName
        } else {
          this.$Toast('条码不存在')
          this.initModel()
        }
      } catch (error) {
        this.initModel()
      }
    },
    async BarcodeUnBind() {
      let formattedTimestamp = moment().format('YYYYMMDDHHmmssSSS');
      let randomNum = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10)).join('')
      let transId = `${formattedTimestamp}.${randomNum}`
      let eventTime = moment().format('YYYY-MM-DD HH:mm:ss')
      if (!this.model.sourceLotName) {
        return this.$Toast('PACK码不能为空!')
      }
      if (!this.model.targetLotName) {
        return this.$Toast('客户码不能为空!')
      }
      let params = {
        cellList: [
          {
            "serialNo": this.model.sourceLotName,
            "parentSerialNo": this.model.targetLotName,
          }
        ],
        eventTime: eventTime,
        eventType: 'U',
        eventUser: this.$getLocal(USER_ID),
        factoryNo: "10011",
        lineName: "BZ1",
        transId: transId,
        workShopSection: "BZ1PACKBZ"
      }
      let res = await this.$service.ThreeCodeToOne.BarcodeUnBind(params)
      if (res.resultMsg === "成功") {
        this.$Toast("解绑成功")
        this.initModel()
        this.focus_lotName = false;
        this.$nextTick(() => {
          this.focus_lotName = true;
        });
      } else {
        this.showErrorEffect = true
        uni.showModal({
          title: '错误提示',
          content: res.resultMsg || '操作失败，请检查数据！',
          showCancel: true,
          cancelText: '取消',
          confirmText: '我知道了',
          success: (res) => {
            if (res.cancel) {
              this.showErrorEffect = false // 取消关闭效果
            }
            this.showErrorEffect = false
            // confirm 点击可以保留效果或也关闭
          }
        })
      }
    },
    submit() {
      uni.showModal({
        title: '提示',
        content: '是否进行条码解绑',
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.BarcodeUnBind()
          }
          if (res.cancel) {
          }
        },
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'lotName':
          this.model.lotName = 'C20240730164218527'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          const raw = res.result || '';
          const cleaned = raw.replace(/\s+/g, '').trim(); // 清洗：去掉空格、换行、制表符
          this.$set(this.formModel, key, cleaned)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.myContainerPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;

  .myContainer {
    flex: 1;
    overflow-y: scroll;
  }
}

@keyframes alarmFlash {
  0% {
    box-shadow: 0 0 0px red;
  }

  50% {
    box-shadow: 0 0 20px red;
  }

  100% {
    box-shadow: 0 0 0px red;
  }
}

@keyframes shake {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  50% {
    transform: translateX(5px);
  }

  75% {
    transform: translateX(-5px);
  }

  100% {
    transform: translateX(0);
  }
}

.error-shake {
  animation: shake 0.4s ease-in-out, alarmFlash 1s infinite;
  border: 2px solid red;
  border-radius: 10px;
}
</style>