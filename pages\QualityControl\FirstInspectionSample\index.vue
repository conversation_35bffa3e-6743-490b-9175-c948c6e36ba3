<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="首检取样" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true" @leftClick="leftClick"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.sbh" border="none" placeholder="请扫描设备号二维码" :focus="focus_sbh" @focus="focusEvent('sbh')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('sbh')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.sbms }} </view>
        </u-form-item>

        <u-form-item label="工序" borderBottom labelWidth="100">
          <view class="w100x flex right" @click="selectReasonCodeType('gx')">
            <view>{{ model.gx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="首检任务编码" required borderBottom labelWidth="120">
          <view class="w100x flex right" @click="selectReasonCodeType('sjrwbm')">
            <view>{{ model.sjrwbm }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="任务创建方式" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.rwcjfs }} </view>
        </u-form-item>

        <u-form-item label="任务状态" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.rwzt }} </view>
        </u-form-item>

        <u-form-item label="样品条码" borderBottom required labelWidth="100">
          <u--input v-model="model.yptm" border="none" :focus="focus_yptm" placeholder="请扫描样品条码" @focus="focusEvent('yptm')" @confirm="confirmAction"></u--input>
          <view class="iconfont icon-saoma" @click="scan('yptm')"></view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.gdh }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.cpbm }} </view>
        </u-form-item>

        <u-form-item label="已取样品信息" borderBottom labelWidth="120">
          <view class="w100x flex right"></view>
          <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm" @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">取样完成</view>
  </view>
</template>

<script>
import _ from "lodash";
export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      focus_sbh: false,
      focus_yptm: false,
      model: {
        sbh: '',
        sbms: '',
        gx: '',
        sjrwbm: '',
        rwcjfs: '',
        rwzt: '',
        yptm: '',
        gdh: '',
        cpbm: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      radiolist1: [
        {
          name: '报废'
        },
        {
          name: '复投'
        }
      ],
      selectType: '',
      simpleTrackProduct: [],
      paramSbh: '',
      // dealList: ['D060', 'D070', 'D080', 'E090']
      // dealList: ['D060', 'D070']
      dealList: [],
      options: {}
    }
  },
  computed: {
    list: {
      get() {
        return this.$store.state.FinshSampleList;
      },
      set(value) {
        this.$store.commit('changeFinshSampleList', value);
      }
    },
  },
  watch: {
    'model.sbh': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    'model.yptm': {
      handler(res) {
        if (!this.model.machineName) {
          this.$Toast('请先扫描设备！')
          this.$nextTick(() => {
            this.model.yptm = ''
          })
          return
        }
        if (!this.model.gx) {
          this.$Toast('请选择工序！')
          this.$nextTick(() => {
            this.model.yptm = ''
          })
          return
        }
        if (!this.model.sjrwbm) {
          this.$Toast('请选择首检任务编码')
          this.$nextTick(() => {
            this.model.yptm = ''
          })
          return
        }
        if (this.dealList.indexOf(this.model.gx) == -1) {
          // 存在特殊处理
          this.changeLotName(res)
        }
      }
    },
    'model.sjrwbm': {
      handler(res) {
        this.getFinshSampleList(res)
      }
    },
    'model.gx': {
      handler(val) {
        // 选完工序获取任务编码
        this.getInspectionTaskList(val)
      }
    },
  },
  onLoad(options) {
    this.options = options
    console.log('options=', this.options);
    let { processOperationName, machineName, taskNo, createTypeDictText, inspectStateDictText } = options
    if (machineName) {
      // 有设备参数记录
      this.paramSbh = machineName
      this.focus_yptm = true
    } else {
      this.focus_sbh = true
    }
    this.model.sbh = machineName
    this.model.gx = processOperationName
    this.model.sjrwbm = taskNo
    this.model.rwcjfs = createTypeDictText
    this.model.rwzt = inspectStateDictText
    // this.initModel()
    // this.GetReasonCodeType()
    this.getEnumValue()
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('getInspectionTaskList')
    },
    getEnumValue() {
      const params = {
        enumname: 'entertype',
      }
      this.$service.QualityControl.getEnumValue(params).then(res => {
        this.dealList = res.datas.map((item) => {
          return item.value
        })
        console.log('this.getEnumValue', this.dealList);
      })
    },
    confirmAction() {
      console.log('this.model.gx', this.model.gx, this.dealList.indexOf(this.model.gx));
      if (this.dealList.indexOf(this.model.gx) == -1) {
        // 不存在特殊处理
        return
      }
      this.changeLotName(this.model.yptm)
    },
    GetReasonCodeType() {
      const params = {
        inspectType: 'FirstInspection',
        processOperationName: this.model.gxType
      }
      this.$service.QualityControl.getReasonCodeList(params).then(res => {
        this.GetReasonCodeTypeList = res.datas.map((item) => {
          return {
            label: item.reasonCodeDesc,
            value: item.reasonCode
          }
        })
        console.log('this.GetReasonCodeTypeList', this.GetReasonCodeTypeList);
      })
    },
    selectReasonCodeType(type) {
      if (type == 'gx') {
        this.columns[0] = [].concat(this.GetReasonCodeTypeList)
      }
      if (type == 'sjrwbm') {
        this.columns[0] = [].concat(this.simpleTrackProduct)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      if (this.selectType == 'gx') {
        this.model.gx = e.value[0].label
        // this.model.gxType = e.value[0].value
      }
      if (this.selectType == 'sjrwbm') {
        this.model.sjrwbm = e.value[0].label
        this.model.rwcjfs = e.value[0].createTypeDictText
        this.model.rwzt = e.value[0].inspectStateDictText
      }
      this.select = false
    },
    gotoQuery() {
      if (!this.model.sjrwbm) {
        this.$Toast('请先选择首检任务编码！')
        return
      }
      uni.navigateTo({
        url: `/pages/QualityControl/FirstInspectionSample/modules/FirstInspectionSampleList?taskNo=${this.model.sjrwbm}`,
      })
    },
    focusEvent(type) {
      if (type == 'yptm') {

      }
      console.log(123123);
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        sbh: '',
        sbms: '',
        gx: '',
        sjrwbm: '',
        rwcjfs: '',
        rwzt: '',
        yptm: '',
        gdh: '',
        cpbm: ''
      }
    },
    submit(value, type) {
      if (!this.model.sbh) {
        this.$Toast('请填写设备号！')
        return
      }
      if (!this.model.gx) {
        this.$Toast('请先选择工序！')
        return
      }
      if (!this.model.sjrwbm) {
        this.$Toast('请先选择首检任务编码！')
        return
      }
      if (!this.model.yptm) {
        this.$Toast('请填写样品条码！')
        return
      }
      this.focus_yptm = false
      // 取样完成
      let params = {
        inspectType: 'FirstInspection',
        machineName: this.model.sbh,
        processOperationName: this.model.gx,
        taskNo: this.model.sjrwbm,
        lotName: this.model.yptm,
        workOrderName: this.model.gdh,
        productSpecName: this.model.productSpecName
      }
      this.$service.QualityControl.finshSample(params).then((res) => {
        console.log('finshSample', res);
        this.$Toast('取样完成！')
        if (_.isEmpty(this.options)) {
          setTimeout(() => {
            // 刷新已取样品信息
            this.getFinshSampleList(this.model.sjrwbm)
            this.focus_yptm = true
            // 清空样品条码
            this.model = {
              ...this.model,
              yptm: '',
              gdh: '',
              cpbm: '',
              productSpecName: ''
            }
          }, 500)
        } else {
          setTimeout(() => {
            this.leftClick()
          }, 1500);
        }
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 设备号 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        inspectType: 'FirstInspection',
        machineName: this.model.sbh
      }
      try {
        let res = await this.$service.QualityControl.getMachineDataForFirstInspection(params)
        console.log('getMachineDataForFirstInspection', res);
        if (res.datas.length > 0) {
          let data = res.datas[0]
          let changeFlag = false
          if (this.paramSbh != this.model.sbh) {
            // 和参数相同的设备号，不更改任务编码和工序
            changeFlag = true
          }
          this.model = {
            ...res.datas[0],
            sbh: this.model.sbh,
            sbms: data.description,
            gx: changeFlag ? '' : this.model.gx,
            sjrwbm: changeFlag ? '' : this.model.sjrwbm,
            rwcjfs: changeFlag ? '' : this.model.rwcjfs,
            rwzt: changeFlag ? '' : this.model.rwzt,
            yptm: '',
            gdh: '',
            cpbm: ''
          }
          this.GetReasonCodeTypeList = data.machineGroupName.split(',').map(item => {
            return {
              label: item,
              value: item
            }
          })
        } else {
          this.model.sbh = ''
          this.$Toast('设备号不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        inspectType: 'FirstInspection',
        lotName: this.model.yptm,
        taskNo: this.model.sjrwbm,
        machineName: this.model.machineName,
        processOperationName: this.model.gx
      }
      try {
        let res = await this.$service.QualityControl.getFirstLotData(params)
        console.log('getFirstLotData', res);
        if (res.datas.length > 0) {
          let data = res.datas[0]
          this.model = {
            ...this.model,
            yptm: data.lotName,
            gdh: data.productOrderName,
            cpbm: data.productSpecName + '/' + data.productSpecDesc,
            productSpecName: data.productSpecName
          }
        } else {
          this.model.yptm = ''
          this.$Toast('样品不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.model.yptm = ''
        // this.initModel()
      }
    },
    /* 任务列表 */
    async getInspectionTaskList(value) {
      console.log('getInspectionTaskList2', value);
      if (!value) return
      let params = {
        inspectType: 'FirstInspection',
        machineName: this.model.sbh,
        processOperationName: this.model.gx
      }
      try {
        let res = await this.$service.QualityControl.getInspectionTaskList(params)
        console.log('getInspectionTaskList', res);
        if (res.datas.length > 0) {
          this.simpleTrackProduct = res.datas.map(item => {
            return {
              ...item,
              label: item.taskNo,
              value: item.taskNo
            }
          })
          this.simpleTrackProduct = this.simpleTrackProduct.filter(item => {
            // return item.inspectStateDictText == '已完成'
            return item.inspectStateDictText == '待取样' || item.inspectStateDictText == '检验中' || item.inspectStateDictText == '待判定'
          })
        } else {
          this.simpleTrackProduct = []
        }
      } catch (error) {
        console.log('error', error);
        // this.initModel()
      }
    },
    /* 已取样列表 */
    async getFinshSampleList(value) {
      console.log('getFinshSampleList1', value);
      if (!value) return
      let params = {
        inspectType: 'FirstInspection',
        taskNo: this.model.sjrwbm
      }
      try {
        let res = await this.$service.QualityControl.getFinshSampleList(params)
        console.log('getFinshSampleList', res);
        if (res.datas.length > 0) {
          this.list = res.datas
        } else {
          this.list = []
        }
      } catch (error) {
        console.log('error', error);
        this.model.yptm = ''
        // this.initModel()
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
