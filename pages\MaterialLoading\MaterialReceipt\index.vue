<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="物料接收确认" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10">
      <view class="myContainer ml10 mr10 mb4">
        <u--form labelPosition="left" :model="form" labelWidth="110">
          <u-form-item label="配送人员编号:" required labelWidth="110">
            <u--input v-model="form.releaseUser" border="none" focus placeholder="请扫描或输入配送人员" @focus="focusEvent('releaseUser')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('releaseUser')"></view>
          </u-form-item>
          <u-form-item label="接收人员编号:" required labelWidth="110">
            <u--input v-model="form.completeUser" border="none" focus placeholder="请扫描或输入接收人员" @focus="focusEvent('completeUser')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('completeUser')"></view>
          </u-form-item>
          <u-form-item label="ERP发料单号:" required labelWidth="110">
            <u--input v-model="form.wmsProductOrderPickName" border="none" focus placeholder="请扫描或输入ERP发料单号" @focus="focusEvent('wmsProductOrderPickName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('wmsProductOrderPickName')"></view>
          </u-form-item>
          <u-form-item label="MES领料单号:" labelWidth="130">
            <view class="w100x flex right">
              {{ form.productOrderPickName }}
            </view>
          </u-form-item>
          <u-form-item label="工单编号:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.productOrderName }}
            </view>
          </u-form-item>
          <u-form-item label="产品名称:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.productSpecName }}
            </view>
          </u-form-item>
          <u-form-item label="需求日期:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.releaseTime ? moment(form.releaseTime).format('YYYY-MM-DD') : '' }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">物料明细</view>
      <scroll-view class="h300" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view v-for="(item, index) in simpleTrackProduct" :key="index" class="bt_e1e1e1 pa10">
            <view class="flex between h30 hcenter c_000">
              <view>物料编码:</view>
              <view>{{ item.consumableSpecName }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>物料名称:</view>
              <view>{{ item.consumableSpecDesc }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>使用工序:</view>
              <view>{{ item.processOperationName }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>申请数量(PCS):</view>
              <view>{{ item.createdQuantity }}</view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>发料数量(PCS):</view>
              <view class="flex hcenter">
                <view>{{ item.releaseQuantity }}</view>
              </view>
            </view>
            <view class="flex between h30 hcenter c_000">
              <view>批次号:</view>
              <view class="flex hcenter">
                <view>{{ item.consumableName }}</view>
              </view>
            </view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
    <view class="btnContainer2 bottomBtn">
      <view @click="submit('Finished')">接收</view>
      <view class="bc_e9283e" @click="submit('Refused')">拒收</view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import moment from 'moment'
export default {
  name: 'MaterialReceipt',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.wmsProductOrderPickName': {
      handler(val) {
        this.changeWmsProductOrder(val)
      }
    },
  },
  data() {
    this.changeWmsProductOrder = this.$debounce(this.changeWmsProductOrder, 1000)
    return {
      rulesTip: {
        releaseUser: '配送人员不能为空',
        completeUser: '接收人员不能为空',
        wmsProductOrderPickName: 'ERP发料单号不能为空',
      },
      form: {},
      simpleTrackProduct: [],
    };
  },

  onLoad(e) {
    this.initModel()
  },
  methods: {
    moment,
    initModel() {
      this.form = {
        wmsProductOrderPickName: null, // 领料单号
        productOrderPickName: null, // MES领料单号
        productOrderName: null, // 工单编号
        productSpecName: null, // 产品名称
        releaseTime: null, // 需求日期
        releaseUser: null, // 配送人
        completeUser: null, // 拒/接收人
        completeTime: null // 拒/接收时间
      }
    },
    focusEvent(type) {
      this.form[type] = ''
    },
    /* 根据发料单号查询领料单信息 */
    async changeWmsProductOrder(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        wmsProductOrderPickName: value
      }
      try {
        let res = await this.$service.MaterialLoading.getProductOrderPickList(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          let { productOrderPickName, productOrderName, productSpecName, releaseTime } = res.datas[0]
          this.form.productOrderPickName = productOrderPickName
          this.form.productOrderName = productOrderName
          this.form.productSpecName = productSpecName
          this.form.releaseTime = releaseTime
          // this.getData(productOrderPickName)
          this.getData(value)
        } else {
          this.$Toast('数据不存在！')
        }
      } catch (error) {
        this.form.machineName = null
      }
    },
    getData(productOrderPickName) {
      let params = {
        // productOrderPickName,
        wmsProductOrderPickName: productOrderPickName
      }
      this.$service.MaterialLoading.getProductOrderMaterialList(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            this.simpleTrackProduct = res.datas
          } else {
            this.simpleTrackProduct = []
          }
        }
      })
    },
    submit(status) {
      for (let key in this.rulesTip) {
        if (!this.form[key]) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let { wmsProductOrderPickName, productOrderPickName, releaseUser, completeUser } = this.form
      let params = {
        wmsProductOrderPickName, // 发料单
        productOrderPickName, // 领料单
        releaseUser, // 配送人
        completeUser, // 接收人
        completeTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        productOrderPickState: status
      }
      this.$service.MaterialLoading.ProductOrderPickUpdate(params).then((res) => {
        if (res && res.success) {
          this.$Toast('操作成功！')
          this.initModel()
          this.simpleTrackProduct = []
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.form.machineName = 'YN-GZ-WG-ZP-401'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.form, key, res.result)
          },
        })
      }
      // #endif
    },
  },
};
</script>


<style lang="scss" scoped>
@import '../../../styles/publicStyle.scss';
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  .topContainer {
    flex-shrink: 0;
  }
  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
  /deep/ .u-form-item__body {
    padding: 13rpx;
  }
  .bottomBtn {
    position: fixed;
    bottom: -28rpx;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .table_header {
    flex-shrink: 0;
  }
  .table_content {
    flex: 1;
    overflow-y: scroll;
  }
  .btn {
    width: 120rpx;
    height: 50rpx;
    border-radius: 10%;
    background-color: #0285be;
    border: 2rpx solid #b1c2db;
    text-align: center;
    line-height: 50rpx;
    font-size: 24rpx;
    color: #fff;
  }
}
</style>