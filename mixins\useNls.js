/**
 *
 * @description: 国际化 单页面
 * @author: ouyangqing
 * 参考材料上料 LoadingMaterial
 * 使用
 *  import useNls from "@/mixins/useNls";
 *  mixins: [useNls],
 *  data() {
    return {
      pageTitle: '', // 标题国际化
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
        lbmachineName: '设备号',
        lbmachineDescription: '设备描述',
        lbmaterialLocationName: '安装点',
        lbprocessOperationName: '工序',
        lbproductOrderName: '工单号',
        lbproductSpecName: '产品编码',
        lbbomID: 'BOM',
        lbconsumableName: '标签条码',
        lbconsumableSpec: '材料',
        lbquantity: '数量',
        lbloadingQuantity: '上料数量',
        lbfeeding: '上料',
        lbviewDetail: '查看已上料明细',
      },
    }
  },
 * 
* onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageTitle = pageParams.pageTitle
    this.initNls(pageParams, this.nlsMap)
  },
*/
export default {
  data() {
    return {
    }
  },
  methods: {
    initNls(pageParams, nlsMap) {
      let injectMap = pageParams.nlsMap;
      let isSync = false;
      Object.keys(nlsMap).forEach((key) => {
        if (injectMap.hasOwnProperty(key)) {
          nlsMap[key] = injectMap[key];
        } else {
          isSync = true;
        }
      })
      if (isSync) {
        this.syncNls(pageParams, nlsMap);
      }
    },


    syncNls(pageParams, nlsMap, localType = null,) {
      if (localType == null) localType = 'Global'
      let nlsList = []

      let result = pageParams.menuId.replace(/[^0-9]/g, '');
      let res_menuId = result.slice(0, 9)

      Object.keys(nlsMap).forEach((key) => {
        let typeName = key.startsWith('lb') ? 'label' : key.startsWith('ms') ? 'message' : key.startsWith('bt') ? 'button' : 'label'

        let nlsItem = {
          appName: 'GFM',
          menuId: res_menuId,
          porosMenuId: null,
          labelKey: key,
          labelText: nlsMap[key],
          localType: localType,
          typeName: typeName,
          nlsValues: []
        }
        nlsList.push(nlsItem);
      });

      let data = nlsList
      this.$service.nls.syncAll(data).then(res => {
        res.datas.forEach((item) => {
          this.nlsMap[item.labelKey] = item.labelText
        })
      })
    }
  },
}
