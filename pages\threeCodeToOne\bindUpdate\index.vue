<template>
	<view class="bc_f3f3f7 myContainerPage">
		<u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
			leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
		<!-- {{ nlsMap }} -->
		<view class="myContainer ma10">
			<u--form labelPosition="left" :model="model" labelWidth="100">
				<u-form-item label="物料箱标签" borderBottom required labelWidth="100">
					<u--input v-model="model.carrierName" border="none" placeholder="请扫描或输入" @focus="handleFocus(false)"
						@blur="handleFocus(true)"></u--input>
					<view class="iconfont icon-saoma" @click="scan('carrierName')"></view>
				</u-form-item>
				<u-form-item label="物料箱标签" borderBottom labelWidth="100">
					<view class="w100x flex right">
						{{ model.carrierName2 }}
					</view>
				</u-form-item>
				<u-form-item label="成品编码" borderBottom labelWidth="100">
					<view class="w100x flex right">
						{{ model.productSpecName }}
					</view>
				</u-form-item>

				<u-form-item label="装箱规格(行*列*层)" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ model.packRule }}</view>
				</u-form-item>

				<u-form-item label="装箱数量" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ model.countInf }}</view>
				</u-form-item>
			</u--form>
			<!-- </view> -->
			<view class="btnContainer" style="width: auto;" @click="productUnBindAll">整箱释放</view>
			<!-- <view class="mt10"> -->
			<u--form labelPosition="left" :model="model" labelWidth="100">
				<u-form-item label="扫描区" borderBottom required labelWidth="100">
					<u--input v-model="model.currentSn" border="none" placeholder="请扫描或输入" :focus="isInFocus"
						@blur="handleBlur"></u--input>
					<view class="iconfont icon-saoma" @click="scan('currentSn')"></view>
				</u-form-item>
				<view class="mt10">
					<u-radio-group v-model="model.czfs" placement="column" style="width: 100%;">
						<view style="display: flex;flex-direction: row-reverse">
							<u-radio v-for="item in radiolist1" :key="item.name" :customStyle="{ marginRight: '10rpx' }"
								:label="item.name" :name="item.name">
							</u-radio>
						</view>
					</u-radio-group>
				</view>
				<view class="mt10">
				</view>
				<u-form-item>
				</u-form-item>
			</u--form>

			<u--form v-show="model.czfs === '产品替换'" labelPosition="left" :model="modelNew" labelWidth="100">

				<view class="lin40 fs16 pl20 c_00b17b mt20">新绑条码</view>
				<u-form-item label="客户码" borderBottom labelWidth="100">
					<view class="w100x flex right"> {{ modelNew.custCode }} </view>
				</u-form-item>
				<u-form-item label="物料编码" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelNew.productSpecName }} </view>
				</u-form-item>

				<u-form-item label="物料名称" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelNew.description }}</view>
				</u-form-item>

				<u-form-item label="生产工单" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelNew.productOrderName }}</view>
				</u-form-item>
				<u-form-item label="包装工单" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelNew.packOrderName }}</view>
				</u-form-item>
			</u--form>
			<u--form v-show="model.czfs === '产品替换'" labelPosition="left" :model="modelOld" labelWidth="100">

				<view class="lin40 fs16 pl20 c_00b17b mt20">被绑条码</view>
				<u-form-item label="客户码" borderBottom labelWidth="100">
					<view class="w100x flex right"> {{ modelOld.custCode }} </view>
				</u-form-item>
				<u-form-item label="物料编码" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelOld.productSpecName }} </view>
				</u-form-item>
				<u-form-item label="物料名称" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelOld.description }}</view>
				</u-form-item>
				<u-form-item label="生产工单" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelOld.productOrderName }}</view>
				</u-form-item>
				<u-form-item label="包装工单" borderBottom labelWidth="120">
					<view class="w100x flex right"> {{ modelOld.packOrderName }}</view>
				</u-form-item>

			</u--form>
			<u-button class="info-btn" @click="goToDetail">
				<i class="icon-info">i</i>
			</u-button>

		</view>

	</view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
	mixins: [useNls, ScrollMixin, PrintPackageMixin],
	components: {
		NoData,
	},
	data() {
		this.changecurrentSn = this.$debounce(this.changecurrentSn, 1000)
		this.changeboxNo = this.$debounce(this.changeboxNo, 1000)
		return {
			pageParams: {},
			isInFocus: true,
			pageTitle: '',
			globalMap: getApp().globalData.globalMap, // 获取全局数据
			nlsMap: {
			},
			model: {},
			modelNew: {},
			modelOld: {},
			list: [], // 
			radiolist1: [
				{
					name: '产品替换'
				},
				{
					name: '单产品解绑'
				}
			],
		}
	},
	computed: {},
	watch: {
		'model.carrierName': {
			handler(val) {
				this.changeboxNo(val)
			}
		},
		'model.currentSn': {
			handler(val) {
				this.changecurrentSn(val)
			}
		},
		'model.czfs': {
			handler(val) {
				this.initModel2()
			}
		},
		'modelNew.custCode': {
			handler(val) {
				console.log('modelNew changed:', val);
				this.checkAndCallApi();
			},
			deep: true
		},
		'modelOld.custCode': {
			handler(val) {
				this.checkAndCallApi();
			},
			deep: true
		}
	},
	async onLoad(options) {
		let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
		this.pageParams = pageParams
		this.pageTitle = pageParams.pageTitle // 标题
		await this.initNls(pageParams, this.nlsMap)
		this.initModel()
		this.initModel2()
	},
	methods: {
		initModel() {
			this.model = {
				carrierName: '', //箱号
				carrierName2: '',
				countInf: '',// 装箱数量
				productSpecName: '',//成品编码
				packRule: '',//装箱规格
				currentSn: '',
				czfs: '',
			}
		},
		initModel2() {
			this.modelNew = {
				custCode: '',//客户码
				productSpecName: '',
				description: '',
				productOrderName: '',
				packOrderName: '',
			}
			this.modelOld = {
				custCode: '',//客户码
				productSpecName: '',
				description: '',
				productOrderName: '',
				packOrderName: '',
			}
		},
		async changecurrentSn(value) {
			if (!value) return
			if (this.model.czfs === '' || this.model.czfs === null || this.model.czfs === undefined) {
				this.model.currentSn = ''
				return this.$Toast('请选择操作');
			}

			if (this.model.czfs === '单产品解绑') {
				uni.showModal({
					title: '提示',
					content: '是否进行产品解绑',
					cancelText: '取消',
					confirmText: '确认',
					cancelColor: '#666',
					confirmColor: '#409eff',
					success: (res) => {
						if (res.confirm) {
							this.productUnBind(value)
						}
						if (res.cancel) {
						}
					},
				})

			} else {
				let params = {
					currentSn: value,
				}
				try {
					let res = await this.$service.ThreeCodeToOne.getPackInfByCusCode(params)
					if (res.success) {
						if (res.datas[0].boxCode != '' && res.datas[0].boxCode != null && res.datas[0].boxCode != undefined) {
							this.modelOld = res.datas[0]
						} else {
							this.modelNew = res.datas[0]
						}
					}
					this.model.currentSn = ''
				} catch (error) {
					this.model.currentSn = ''
				}
			}

		},
		async changeboxNo(value) {


			if (!value) return
			this.model.countInf = ''
			this.model.productSpecName = ''
			this.model.packRule = ''
			// this.model.carrierName2 = ''
			try {
				let params = {
					carrierName: value,
				}
				let res = await this.$service.ThreeCodeToOne.getPackBoxInf(params)
				if (res.success) {
					this.model.countInf = res.datas[0].countInf
					this.model.productSpecName = res.datas[0].productSpecName
					this.model.packRule = res.datas[0].packRule
					this.model.layerCount = res.datas[0].layerCount
					this.model.Quantity = res.datas[0].Quantity
					this.model.carrierName2 = this.model.carrierName || this.model.carrierName2
					this.model.carrierName = ''
				}
			} catch (error) {
				console.log(error)
				this.model.carrierName = ''
			}
		},
		goToDetail() {
			uni.navigateTo({
				url: `/pages/threeCodeToOne/bindUpdate/detail?carrierName=${this.model.carrierName2}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}&layerCount=${this.model.layerCount}&Quantity=${this.model.Quantity} `,
			})
		},
		checkAndCallApi() {
			// 确保 modelNew 和 modelOld 都不为空
			if (this.modelNew.custCode && this.modelOld.custCode) {
				uni.showModal({
					title: '提示',
					content: '是否进行替换',
					cancelText: '取消',
					confirmText: '确认',
					cancelColor: '#666',
					confirmColor: '#409eff',
					success: (res) => {
						if (res.confirm) {
							this.callApi();
						}
						if (res.cancel) {
							this.initModel2()
						}
					},
				})

			}
		},
		isModelFilled(model) {
			// 判断对象的所有字段是否都有值
			return Object.values(model).every(value => value !== '' && value !== null && value !== undefined);
		},
		async callApi() {
			try {
				let params = {
					currentSn: this.modelNew.custCode,
					sourceSn: this.modelOld.custCode,
					carrierName: this.model.carrierName2,
					eventUser: this.$getLocal(USER_ID),
				};
				let res = await this.$service.ThreeCodeToOne.productReplace(params);
				if (res.success) {
					this.$Toast('产品替换成功');
					this.initModel2()
					this.changeboxNo(this.model.carrierName2)
				} else {
					uni.showModal({
						title: '提示',
						content: res.msg,
						cancelText: '取消',
						confirmText: '确认',
						cancelColor: '#666',
						confirmColor: '#409eff',
						success: (res) => {
							if (res.confirm) {
								this.initModel2()
							}
							if (res.cancel) { }
						},
					})
				}
			} catch (error) {
				console.error('接口调用异常', error);
				uni.showModal({
					title: '提示',
					content: res.msg,
					cancelText: '取消',
					confirmText: '确认',
					cancelColor: '#666',
					confirmColor: '#409eff',
					success: (res) => {
						if (res.confirm) {
							this.initModel2()
						}
						if (res.cancel) { }
					},
				})
			}
		},
		async productUnBind(value) {
			try {
				let params = {
					barCode: value,
					eventUser: this.$getLocal(USER_ID),
					carrierName: this.model.carrierName2,
				};
				let res = await this.$service.ThreeCodeToOne.productUnBind(params);
				if (res.success) {
					this.$Toast('产品解绑成功');
					this.changeboxNo(this.model.carrierName2)
				} else {
					uni.showModal({
						title: '提示',
						content: error.msg,
						cancelText: '取消',
						confirmText: '确认',
						cancelColor: '#666',
						confirmColor: '#409eff',
						success: (res) => {
							if (res.confirm) {

							}
							if (res.cancel) { }
						},
					})
				}
				this.model.currentSn = ''
			} catch (error) {
				this.model.currentSn = ''
				console.error('接口调用异常', error);
				uni.showModal({
					title: '提示',
					content: error.msg,
					cancelText: '取消',
					confirmText: '确认',
					cancelColor: '#666',
					confirmColor: '#409eff',
					success: (res) => {
						if (res.confirm) {

						}
						if (res.cancel) { }
					},
				})
			}
		},
		async productUnBindAll() {
			uni.showModal({
				title: '提示',
				content: '是否进行整箱释放？',
				cancelText: '取消',
				confirmText: '确认',
				cancelColor: '#666',
				confirmColor: '#409eff',
				success: async (res) => {
					if (res.confirm) {
						try {
							let params = {
								carrierName: this.model.carrierName2,
								eventUser: this.$getLocal(USER_ID),
							};
							let res = await this.$service.ThreeCodeToOne.productUnBindAll(params);
							if (res.success) {
								this.$Toast('整箱释放成功');
								this.changeboxNo(this.model.carrierName2)
							} else {
								uni.showModal({
									title: '提示',
									content: error.msg,
									cancelText: '取消',
									confirmText: '确认',
									cancelColor: '#666',
									confirmColor: '#409eff',
									success: (res) => {
										if (res.confirm) {

										}
										if (res.cancel) { }
									},
								})
							}
						} catch (error) {
							console.error('接口调用异常', error);
							// uni.showModal({
							// 	title: '提示',
							// 	content: error.msg,
							// 	cancelText: '取消',
							// 	confirmText: '确认',
							// 	cancelColor: '#666',
							// 	confirmColor: '#409eff',
							// 	success: (res) => {
							// 		if (res.confirm) {

							// 		}
							// 		if (res.cancel) { }
							// 	},
							// })
						}
					}
					if (res.cancel) { }
				},
			})

		},
		handleFocus(status) {
			this.isInFocus = status;
		},
		handleBlur() {
			setTimeout(() => {
				if (this.isInFocus) {
					this.isInFocus = false
					this.$nextTick(() => {
						this.isInFocus = true

					});
				}
			}, 100);
		},
		scan(key) {
			// #ifdef H5
			switch (key) {
				case 'carrierName':
					this.model.carrierName = 'LA202312010002' // 
					break;
				default:
					break;
			}
			// #endif
			//#ifdef APP-PLUS
			uni.scanCode({
				success: (res) => {
					this.$set(this.model, key, res.result)
				},
			})
			// #endif
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

/* 右下角圆形按钮样式 */
.info-btn {
	position: fixed;
	bottom: 20px;
	right: 20px;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: #00aaff;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
	font-size: 20px;
	cursor: pointer;
}

/* 鼠标悬浮效果 */
.info-btn:hover {
	background-color: #0088cc;
}
</style>
