<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="模切开始" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="设备号" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入设备号" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="设备描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.machineSpecDesc }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productSpecName ? model.productSpecName + '/' + model.productSpecDesc : '' }}</view>
        </u-form-item>

        <u-form-item label="极卷条码" borderBottom labelWidth="170">
          <view class="w100x flex right"> {{ model.consumableName }} </view>
        </u-form-item>

        <u-form-item label="工单号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.productOrderName }} </view>
        </u-form-item>

        <u-form-item label="上卷数量(米)" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.quantity }} </view>
        </u-form-item>

        <u-form-item label="理论可产出数量" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.producibleQuantity }}&nbsp;片 </view>
        </u-form-item>

        <u-form-item label="上卷作业员" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.eventUser }} </view>
        </u-form-item>

        <u-form-item label="上卷时间" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.eventTime ? moment(model.eventTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </view>
        </u-form-item>

        <u-form-item label="弹夹" borderBottom required labelWidth="130">
          <u--input v-model="model.durableName" border="none" placeholder="请扫描或输入弹夹" @focus="focusEvent('durableName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
        </u-form-item>

        <u-form-item label="弹夹容量(片)" borderBottom labelWidth="130">
          <view class="w100x flex right">{{ model.capacity }}</view>
        </u-form-item>
      </u--form>
    </view>

    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import moment from 'moment'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    return {
      rulesTip: {
        machineName: '设备编号不能为空',
        durableName: '弹夹不能为空',
      },
      model: {},
      columns: [],
      machineNameFlag: false, // 正确设备编号标识
      select: false,
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },

    'model.durableName': {
      handler(res) {
        this.changeDurableName(res)
      }
    }
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    moment,
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/Polar/modules/Upgradeable`,
      })
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 设备号
        machineSpecDesc: null, // 设备描述
        productSpecName: '', // 产品编码
        productSpecDesc: '', // 产品名称
        consumableName: null, // 极卷条码
        productOrderName: null, // 工单号
        quantity: null, // 上卷数量
        eventUser: null, // 上卷作业员
        eventTime: null, // 上卷时间
        durableName: null, // 弹夹
        capacity: null, // 弹夹容量
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        ...this.model
      }
      this.$service.DieCutting.DieCuttingStartMsgProcessor(params).then(res => {
        this.$Toast('模切开始成功！')
        this.initModel()
      })
    },

    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      this.machineNameFlag = false
      this.columns = []
      let params = {
        machineName: value
      }
      try {
        let res = await this.$service.DieCutting.queryPdaMachineName(params)
        if (res.datas.length > 0) {
          this.machineNameFlag = true
          this.model = res.datas[0]
        } else {
          this.model.machineName = ''
          this.$Toast('未找到设备信息!')
        }
      } catch (error) {
        this.initModel()
      }
    },
    /* 弹夹 */
    async changeDurableName(value) {
      if (!value) return
      let params = {
        durableName: value,
      }
      try {
        let res = await this.$service.DieCutting.CheckAndGetDurableByDurableName(params)
        if (res.datas.length > 0) {
          let { capacity } = res.datas[0]
          this.model.capacity = capacity
        } else {
          this.$Toast('暂无信息！')
        }
      } catch (error) {
        this.model.durableName = null
        this.model.capacity = capacity
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALIMSA01'
          break;
        case 'durableName':
          this.model.durableName = 'TEST007'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      if (key === 'durableName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.model, key, res.result)
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
