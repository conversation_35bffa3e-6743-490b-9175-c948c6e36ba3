<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="在制品工序调整" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
      leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="载具条码" borderBottom required labelWidth="120">
          <u--input v-model="model.zzptm" border="none" focus placeholder="请扫描载具条码"
            @focus="focusEvent('zzptm')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('zzptm')"></view>
        </u-form-item>

        <!-- <u-form-item label="工序" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.gx }} </view>
        </u-form-item> -->

        <u-form-item label="工单编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gdbm }} </view>
        </u-form-item>

        <u-form-item label="产品编码" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.cpbm }} </view>
        </u-form-item>

        <u-form-item label="产品名称" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.cpmc }} </view>
        </u-form-item>

        <u-form-item label="工艺路线" borderBottom labelWidth="100">
          <view class="w100x flex right"> {{ model.gylx }} </view>
        </u-form-item>

        <u-form-item label="调整工序" required borderBottom labelWidth="130">
          <view class="w100x flex right" @click="selectReasonCodeType('tzgx')">
            <view>{{ model.tzgx }}</view>
            <u-icon name="arrow-down" color="black" size="18"></u-icon>
          </view>
        </u-form-item>

        <u-form-item label="调整数量" borderBottom labelWidth="100">
          <view class="w100x flex right">
            <view class="flex w50x hcenter">
              <u--input v-model="SemiRecordNum" border="none" readonly></u--input>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4c c_5d66c9 flex hcenter mt5">
        <view class="mr5">待提交工序调整</view>
        <u-icon class="ml2" @click="gotoQuery()" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
      </view>
      <view class="pl10">{{ tipContent }}</view>
      <u-picker v-if="select" :show="select" :columns="columns" keyName="label" @confirm="selectFirm"
        @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
      <u-modal :showCancelButton="true" :show="show" title="提示" :content="content" @confirm="confirm"
        @cancel="show = false"></u-modal>
    </view>
    <view class="btnContainer" @click="submit">提交</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'

export default {
  data() {
    this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
    this.changeLotName = this.$debounce(this.changeLotName, 1000)
    return {
      rulesTip: {
        durableName: '设备编号不能为空',
        poleRollQuantity: '极卷数量不能为空',
      },
      model: {
        zzptm: '',
        gx: '',
        gdbm: '',
        cpbm: '',
        cpmc: '',
        gylx: '',
        tzgx: '',
        tzgxType: '',
        nodePosition: ''
      },
      columns: [
        []
      ],
      durableNameFlag: false, // 正确设备编号标识
      select: false,
      show: false,
      content: '',
      focusObj: {
        saveNo: false,
        materialPosition: false
      },
      showLotName: true,
      GetReasonCodeTypeList: [],
      ProcessOperationNameList: [
        {
          label: 'aa',
          value: '1'
        },
        {
          label: 'bb',
          value: '2'
        }
      ],
      radiolist1: [
        {
          name: '退货'
        },
        {
          name: '放行'
        }
      ],
      tipContent: ''
    }
  },
  watch: {
    'model.zzptm': {
      handler(val) {
        this.changeDurableName(val)
      }
    },
    // 'model.lotName': {
    //   handler(res) {
    //     this.changeLotName(res)
    //   }
    // }
  },
  onLoad() {
    this.list = []
    this.listNew = []
    // this.initModel()
    // this.GetReasonCodeType()
  },
  computed: {
    list: {
      get() {
        return this.$store.state.WorkingProcessAdjustmentList;
      },
      set(value) {
        this.$store.commit('changeWorkingProcessAdjustmentList', value);
      }
    },
    SemiRecordNum: {
      get() {
        // let arr = []
        // this.list.forEach((item) => {
        //   if(arr.indexOf(item.zzptm) == -1) {
        //     arr.push(item.zzptm)
        //   }
        // })
        return this.list.length;
      },
      set(value) {
        // this.$store.commit('setCount', value);
      }
    }
  },
  methods: {
    gotoQuery() {
      uni.navigateTo({
        url: `/pages/ProcessAdjustment/WorkingProcessAdjustment/modules/WorkingProcessAdjustmentList2`
      })
    },
    GetReasonCodeType() {
      const params = {
        processFlowName: this.model.processFlowName,
        processFlowRevision: this.model.processFlowRevision,
      }
      this.$service.ProcessAdjustment.getProcessOpData(params).then(res => {
        this.ProcessOperationNameList = res.datas.map((item) => {
          return {
            label: item.processOperationName + '/' + item.description,
            value: item.processOperationName,
            nodePosition: item.nodePosition
          }
        })
      })
    },
    selectReasonCodeType(type) {
      if (type == 'tzgx') {
        this.columns[0] = [].concat(this.ProcessOperationNameList)
      }
      this.selectType = type
      console.log('this.columns', this.columns);
      this.select = true
    },
    selectFirm(e) {
      this.model.tzgx = e.value[0].label
      this.model.tzgxType = e.value[0].value
      this.model.nodePosition = e.value[0].nodePosition
      // this.focusObj.saveNo = true
      this.select = false
      if (this.model.zzptm) {
        this.list.push(
          ...this.listNew.map(item => ({
            ...item,
            tzgx: this.model.tzgx,
            nodePosition: this.model.nodePosition,
            tzgxType: this.model.tzgxType

          }))
        )
        this.tipContent = `载具${this.model.zzptm}中所有条码提交明细成功`
        // 选完之后清空条码及信息
        this.initModel()
      }
    },
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        zzptm: '',
        gx: '',
        gdbm: '',
        cpbm: '',
        cpmc: '',
        gylx: '',
        tzgx: this.model.tzgx ? this.model.tzgx : '',
        tzgxType: this.model.tzgxType ? this.model.tzgxType : '',
        nodePosition: this.model.nodePosition ? this.model.nodePosition : '',
      }
    },
    submit() {
      if (this.SemiRecordNum == 0) {
        this.$Toast('暂无待提交工序调整！')
        return
      }
      let arr = []
      this.list.forEach(item => {
        arr.push(item.lotName)
      })
      // return
      let params = {
        lotName: arr.join(','),
        processFlowName: this.list[0].processFlowName,
        processFlow: this.list[0].processFlowName,
        processFlowRevision: this.list[0].processFlowRevision,
        processOperation: this.list[0].tzgxType,
        // nodePosition: this.model.tzgxType,
        nodePosition: this.list[0].nodePosition,
        userId: this.$getLocal(USER_ID)
      }
      this.$service.ProcessAdjustment.changeOperationByLotName(params).then(res => {
        this.$Toast('工序调整提交成功！')
        // 提交成功初始化
        this.model = {
          zzptm: '',
          gx: '',
          gdbm: '',
          cpbm: '',
          cpmc: '',
          gylx: '',
          tzgx: '',
          tzgxType: '',
          nodePosition: ''
        }
        // 提交成功清空明细
        this.list = []
        this.listNewt = []
        this.tipContent = ''
      })
    },

    confirm() {
      // 继续上卷
      this.model.poleRollLoadingType = 'poleRollJoinLoading'
      let params = {
        ...this.model,
      }
      this.$service.Polar.PoleRollLoading(params).then(res => {
        if (res.success) {
          this.$Toast('合卷上卷成功!')
          this.lotList = []
          this.hours = 0
          this.minutes = 0
          this.initModel()
        }
      })
    },

    /* 载具 */
    async changeDurableName(value) {
      if (!value) return
      this.columns = []
      let params = {
        lotName: value
      }
      try {
        let res = await this.$service.ProcessAdjustment.getLotDatas(params)
        if (res.datas.length > 0) {
          let data = res.datas[0]
          this.model = {
            ...res.datas[0],
            zzptm: value,
            gx: data.processOperationName + '/' + data.opDescription,
            gdbm: data.productOrderName,
            cpbm: data.productSpecName,
            cpmc: data.psDescription,
            gylx: data.processFlowName + '/' + data.pfDescription,
            tzgx: this.model.tzgx ? this.model.tzgx : '',
            tzgxType: this.model.tzgxType ? this.model.tzgxType : '',
            nodePosition: this.model.nodePosition ? this.model.nodePosition : '',
          }
          this.listNew = res.datas
          if (this.model.tzgx) {
            let flag = false
            let msg = '添加失败，调整工序与明细不一致！'
            this.list.forEach(ele => {
              if (ele.tzgx != this.model.tzgx) {
                flag = true
              }
              if (ele.zzptm == this.model.zzptm) {
                msg = '添加失败，该载具的条码已存在明细！'
                flag = true
              }
              if (ele.processFlowName != this.model.processFlowName || ele.processFlowRevision != this.model.processFlowRevision) {
                msg = '添加失败，该条码工艺路线与明细不一致！'
                flag = true
              }
            });
            if (flag) {
              this.$Toast(msg)
              this.tipContent = `载具${this.model.zzptm}提交明细失败`
              this.initModel()
              return
            }
            // 有调整同工序直接添加
            this.list.push(
              ...this.listNew.map(item => ({
                ...item,
                tzgx: this.model.tzgx,
                nodePosition: this.model.nodePosition,
                tzgxType: this.model.tzgxType
              }))
            )
            this.tipContent = `载具${this.model.zzptm}中所有条码提交明细成功`
            this.initModel()
            return
          }
          // 获取调整工序
          // 获取不良现象
          this.GetReasonCodeType()
        } else {
          this.model.clbqtm = ''
          this.$Toast('在制品条码不存在！')
        }
      } catch (error) {
        console.log('error', error);
        this.initModel()
      }
    },
    /* 条码 */
    async changeLotName(value) {
      if (!value) return
      let params = {
        lotName: value,
      }
      try {
        let res = await this.$service.carrierIsBind.GetProductAndDurableByLotName(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.lotName = ''
            this.$Toast('条码不存在！')
            return
          }
          let productData = res.datas[0]
          if (!this.model.lotGrade) {
            // 载具没产品时候，直接走绑定逻辑
            this.submit(value, '绑定')
          } else {
            // 如果载具已绑定，则校验
            if (productData.lotGrade !== this.model.lotGrade) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的状态${productData.lotGrade}与第1个在制品条码的状态${this.model.lotGrade}不一致！`)
              return
            }
            if (productData.processOperationName !== this.model.processOperationName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的工序${productData.processOperationName}与第1个在制品条码工序${this.model.processOperationName}不一致！`)
              return
            }
            if (productData.productSpecName !== this.model.productSpecName) {
              this.model.lotName = ''
              this.$Toast(`已扫码在制品条码的产品编码${productData.productSpecName}与第1个在制品条码产品编码${this.model.productSpecName}不一致！`)
              return
            }
            // 前面校验通过，且弹夹为空，则进行绑定
            if (!productData.carrierName) {
              if (this.model.lotQuantity == this.model.capacity) {
                this.model.lotName = ''
                this.$Toast(`载具${this.model.durableName}已装满`)
                return
              }
              this.submit(value, '绑定')
              return
            } else {
              if (productData.carrierName !== this.model.durableName) {
                this.model.lotName = ''
                this.$Toast(`在制品条码${value}已绑定载具${productData.carrierName}！`)
                return
              }
              this.submit(value, '解绑')
            }
          }
        }
      } catch (error) {
        this.model.lotName = ''
      }
    },
    scan(key) {
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
