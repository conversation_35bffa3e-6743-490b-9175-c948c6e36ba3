<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar title="冲壳卸料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="冲壳机" borderBottom required labelWidth="100">
          <u--input v-model="model.machineName" border="none" focus placeholder="请扫描或输入冲壳机" @focus="focusEvent('machineName')"></u--input>
          <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
        </u-form-item>

        <u-form-item label="冲壳机描述" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.description }} </view>
        </u-form-item>

        <u-form-item label="铝塑膜批次号" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.consumableName }} </view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="130">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.materialLocationName) }}
          </view>
        </u-form-item>

        <u-form-item label="上料数量" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.createQuantity }} </view>
        </u-form-item>

        <u-form-item label="上料时间" borderBottom labelWidth="130">
          <view class="w100x flex right"> {{ model.mountTime }} </view>
        </u-form-item>

        <u-form-item required label="剩余数量(m)" labelWidth="140"> <u--input v-model="model.realQuantity" border="none" type="number"></u--input> </u-form-item>
      </u--form>
    </view>

    <view class="btnContainer" @click="submit">卸料</view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
export default {
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '冲壳机编号不能为空',
      },
      model: {},
    }
  },
  watch: {
    'model.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  onLoad() {
    this.initModel()
  },
  methods: {
    focusEvent(type) {
      // this.model[type] = ''
    },
    initModel() {
      this.model = {
        machineName: null, // 冲壳机
        description: null, // 冲壳机描述
        consumableName: null, // 铝塑膜批次号
        consumableSpecName: null, // 物料编号
        materialLocationName: null, // 物料描述
        createQuantity: null, // 上料数量
        mountTime: null, // 上料时间
        realQuantity: 0 // 剩余数量
      }
    },
    submit() {
      for (let key in this.rulesTip) {
        if (!this.model[key] && this.model[key] != 0) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if(this.model.realQuantity > this.model.createQuantity) {
        return this.$Toast('剩余数量不能大于上料数量！')
      } else if(this.model.realQuantity < 0) {
        return this.$Toast('剩余数量不能小于0！')
      }
      let params = {
        consumableName: this.model.consumableName,
        machineName: this.model.machineName,
        remainingQuantity: this.model.realQuantity,
      }
      this.$service.punchingShell.unmount(params).then(res => {
        if (res.success) {
          this.$Toast('卸料成功!')
          this.initModel()
        }
      })
    },

    /* 冲壳机 */
    async changeMachineName(value) {
      if (!value) return
      this.model.description = null
      this.model.consumableName = null
      this.model.consumableSpecName = null
      this.model.materialLocationName = null
      this.model.createQuantity = null
      this.model.mountTime = null
      this.model.remainingQuantity = null
      let params = {
        machineName: value,
      }
      try {
        let res = await this.$service.punchingShell.validateUnmount(params)
        if (res.success) {
          if (res.datas.length == 0) {
            this.model.machineName = ''
            return this.$Toast('请扫描正确的冲壳机编码!')
          }
          this.model = res.datas[0]
        }
      } catch (error) {
        this.model.machineName = null
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'G.EQ.CKJB.01.01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
