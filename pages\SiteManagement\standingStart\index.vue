<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="物料条码" borderBottom required labelWidth="100">
          <u--input v-model="model.consumableName" border="none" focus placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
        </u-form-item>

        <u-form-item label="物料" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.consumableSpecName, model.consumableSpecText) }}
          </view>
        </u-form-item>

        <u-form-item label="批次号" borderBottom labelWidth="100">
          <u--input readonly v-model="model.dateCode" border="none"></u--input>
        </u-form-item>
        <u-form-item label="标准静置时长(h)" labelWidth="140">
          <u--input readonly v-model="model.quietTime" border="none"></u--input>
        </u-form-item>
      </u--form>
    </view>
    <view class="btnContainer" @click="submit">开始</view>
  </view>
</template>

<script>
import _ from "lodash";
import useNls from "@/mixins/useNls";
export default {
  mixins: [useNls],
  data() {
    this.changeConsumableName = this.$debounce(this.changeConsumableName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },
      rulesTip: {
        consumableName: '物料条码不能为空',
      },
      model: {},
    }
  },
  computed: {
  },
  watch: {
    'model.consumableName': {
      handler(val) {
        this.changeConsumableName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)

    this.initModel()
  },
  methods: {
    initModel() {
      this.model = {
        consumableName: '', // 物料条码
        consumableSpecName: null, // 物料名称
        consumableSpecText: null, // 物料名称
        dateCode: null, // 批次号
        quietTime: null, // 标准静置时常

      }
    },
    async submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let params = {
        consumableName: this.model.consumableName,
      }
      let res = await this.$service.standingStart.startStewing(params)
      if (res.success) {
        this.$Toast('操作成功!')
        this.initModel()
      }
    },
    async changeConsumableName(value) {
      this.model.consumableSpecName = ''
      this.model.consumableSpecText = ''
      this.model.dateCode = ''
      this.model.quietTime = ''
      if (!value) return
      try {
        let params = {
          consumableName: value,
        }
        let res = await this.$service.standingStart.GetStartStewingInfo(params)
        this.model.consumableSpecName = res.datas[0].consumableSpecName
        this.model.consumableSpecText = res.datas[0].consumableSpecText
        this.model.dateCode = res.datas[0].dateCode
        this.model.quietTime = res.datas[0].quietTime
      } catch (error) {
        this.initModel()
      }
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = '' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
