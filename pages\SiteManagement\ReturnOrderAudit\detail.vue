<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="false" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true" @leftClick="leftClick"> </u-navbar>

    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="仓库" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockCode, model.stockText) }}
          </view>
        </u-form-item>

        <u-form-item label="库位" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ $utils.optionShowConfig(model.stockLocationCode, model.stockLocationText) }}
          </view>
        </u-form-item>

        <u-form-item label="退库方式" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.trayReturnWayText }}
          </view>
        </u-form-item>

        <u-form-item label="退库类型" labelWidth="100">
          <view class="w100x flex right">
            {{ model.trayReturnTypeText }}
          </view>
        </u-form-item>
      </u--form>

      <view class="mt10">
        <view class="mb10" style="color: #409eff"> 退料明细 </view>
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h30 hcenter c_999" v-if="model.trayReturnWay !== 'ConsumableReturn'">
              <view> 托盘标签</view>
              <view>{{ ele.trayNo }}</view>
            </view>

            <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">标签条码</view>
              <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">数量</view>
            </view>
            <view class="table_content">
              <view v-for="(item, itemindex) in ele.returnItemConsumableList" :key="itemindex" class="flex bl_e1e1e1 h40">
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ itemindex + 1 }}</view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.consumableName }}</view>
                <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60"> {{ item.returnQuantity }}</view>
              </view>
              <view class="pt10" v-if="!ele.returnItemConsumableList || ele.returnItemConsumableList.length === 0">
                <u-empty mode="data"></u-empty>
              </view>
            </view>

            <!-- <view v-for="(item, itemindex) in ele.returnItemConsumableList" :key="itemindex">
            <view class="flex between h40 hcenter c_999">
              <view> 最小包装条码/数量</view>
              <view>{{ item.consumableName }}/{{ item.returnQuantity }}</view>
            </view>
          </view> -->
            <!-- <view class="flex between h40 hcenter c_999">
            <view> 托盘标签</view>
            <view>{{ ele.consumableName }}</view>
          </view>

          <view class="flex between h40 hcenter c_999">
            <view> 数量</view>
            <view>{{ ele.returnQuantity }}</view>
          </view> -->
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view>
      </view>
    </view>

    <view class="flex pa5">
      <u-button type="error" text="驳回" @click="rejectHandle"></u-button>
      <u-button type="success" text="通过" @click="finishHandle"></u-button>
    </view>
  </view>
</template>

<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
import _ from "lodash";
export default {
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  data() {
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {
      },

      model: {},
      list: [],
      orginList: [],

    }
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.detailTitle // 标题

    this.params_returnOrderNo = options.returnOrderNo
    this.nlsMap = nlsMap

    this.initModel()
    this.getDetail()
  },
  methods: {
    leftClick() {
      this.$utils.backAndUpdata('returnRefresh')
    },
    initModel() {
      this.model = {
        stockCode: '',// 
        stockText: '', // 
        stockLocationCode: '', // 库位
        stockLocationText: '', // 库位
        trayReturnWay: '', // 退库方式
        trayReturnWayText: '', // 退库方式
        trayReturnTypeText: '', // 退库类型
      }
    },
    async getDetail() {
      let parmas = {
        returnOrderNo: this.params_returnOrderNo
      }
      let res = await this.$service.ReturnOrderController.listReturnOrder(parmas)
      this.model.stockCode = res.datas[0].stockCode
      this.model.stockText = res.datas[0].stockText
      this.model.stockLocationCode = res.datas[0].stockLocationCode
      this.model.stockLocationText = res.datas[0].stockLocationText
      this.model.trayReturnTypeText = res.datas[0].trayReturnTypeText
      this.model.trayReturnWay = res.datas[0].trayReturnWay
      this.model.trayReturnWayText = res.datas[0].trayReturnWayText
      let list = res.datas[0].returnOrderItemDetailList
      this.orginList = res.datas[0].returnOrderItemDetailList
      let grouped = list.reduce((acc, obj) => {
        const groupKey = obj.trayNo;
        if (!acc[groupKey]) {
          acc[groupKey] = { trayNo: groupKey, returnItemConsumableList: [] };
        }
        acc[groupKey].returnItemConsumableList.push({
          returnQuantity: obj.returnQuantity,
          consumableName: obj.consumableName
        });
        return acc;
      }, {});
      let result = Object.values(grouped);
      this.list = result
    },

    finishHandle() {
      let params = {
        returnOrderNo: this.params_returnOrderNo,
        returnItemConsumableList: this.orginList,
        stockCode: this.model.stockCode,
        stockLocationCode: this.model.stockLocationCode,
        trayReturnType: this.model.trayReturnType,
      }
      this.$service.ReturnOrderController.finish(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.leftClick()
        }, 1500);
      })
    },
    rejectHandle() {
      let params = {
        returnOrderNo: this.params_returnOrderNo,
        returnItemConsumableList: this.orginList,
        stockCode: this.model.stockCode,
        stockLocationCode: this.model.stockLocationCode,
        trayReturnType: this.model.trayReturnType,
      }
      this.$service.ReturnOrderController.reject(params).then(res => {
        this.$Toast('操作成功!')
        setTimeout(() => {
          this.leftClick()
        }, 1500);
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
