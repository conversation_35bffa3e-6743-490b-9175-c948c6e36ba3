!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).GeegaPush=t()}(this,(function(){"use strict";function e(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function t(t){return function(){var n=this,r=arguments;return new Promise((function(o,i){var a=t.apply(n,r);function s(t){e(a,o,i,s,c,"next",t)}function c(t){e(a,o,i,s,c,"throw",t)}s(void 0)}))}}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},a={exports:{}};!function(e){var t=function(e){var t,n=Object.prototype,r=n.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var o=t&&t.prototype instanceof v?t:v,i=Object.create(o.prototype),a=new _(r||[]);return i._invoke=function(e,t,n){var r=l;return function(o,i){if(r===p)throw new Error("Generator is already running");if(r===h){if("throw"===o)throw i;return U()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=O(a,n);if(s){if(s===g)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===l)throw r=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=p;var c=f(e,t,n);if("normal"===c.type){if(r=n.done?h:d,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=h,n.method="throw",n.arg=c.arg)}}}(e,n,a),i}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l="suspendedStart",d="suspendedYield",p="executing",h="completed",g={};function v(){}function E(){}function m(){}var y={};c(y,i,(function(){return this}));var w=Object.getPrototypeOf,T=w&&w(w(I([])));T&&T!==n&&r.call(T,i)&&(y=T);var b=m.prototype=v.prototype=Object.create(y);function x(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(o,i,a,s){var c=f(e[o],e,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(l).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,s)}))}s(c.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function O(e,n){var r=e.iterator[n.method];if(r===t){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=t,O(e,n),"throw"===n.method))return g;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var o=f(r,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function I(e){if(e){var n=e[i];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}return{next:U}}function U(){return{value:t,done:!0}}return E.prototype=m,c(b,"constructor",m),c(m,"constructor",E),E.displayName=c(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===E||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},x(k.prototype),c(k.prototype,a,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new k(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},x(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=I,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return s.type="throw",s.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=t}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}(a);var s=a.exports,c={exports:{}},u=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},f=u,l=Object.prototype.toString;function d(e){return"[object Array]"===l.call(e)}function p(e){return void 0===e}function h(e){return null!==e&&"object"==typeof e}function g(e){if("[object Object]"!==l.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function v(e){return"[object Function]"===l.call(e)}function E(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),d(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var m={isArray:d,isArrayBuffer:function(e){return"[object ArrayBuffer]"===l.call(e)},isBuffer:function(e){return null!==e&&!p(e)&&null!==e.constructor&&!p(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:h,isPlainObject:g,isUndefined:p,isDate:function(e){return"[object Date]"===l.call(e)},isFile:function(e){return"[object File]"===l.call(e)},isBlob:function(e){return"[object Blob]"===l.call(e)},isFunction:v,isStream:function(e){return h(e)&&v(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:E,merge:function e(){var t={};function n(n,r){g(t[r])&&g(n)?t[r]=e(t[r],n):g(n)?t[r]=e({},n):d(n)?t[r]=n.slice():t[r]=n}for(var r=0,o=arguments.length;r<o;r++)E(arguments[r],n);return t},extend:function(e,t,n){return E(t,(function(t,r){e[r]=n&&"function"==typeof t?f(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}},y=m;function w(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var T=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(y.isURLSearchParams(t))r=t.toString();else{var o=[];y.forEach(t,(function(e,t){null!=e&&(y.isArray(e)?t+="[]":e=[e],y.forEach(e,(function(e){y.isDate(e)?e=e.toISOString():y.isObject(e)&&(e=JSON.stringify(e)),o.push(w(t)+"="+w(e))})))})),r=o.join("&")}if(r){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e},b=m;function x(){this.handlers=[]}x.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},x.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},x.prototype.forEach=function(e){b.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var k=x,O=m,S=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},e},C=S,_=function(e,t,n,r,o){var i=new Error(e);return C(i,t,n,r,o)},I=_,U=m,R=U.isStandardBrowserEnv()?{write:function(e,t,n,r,o,i){var a=[];a.push(e+"="+encodeURIComponent(t)),U.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),U.isString(r)&&a.push("path="+r),U.isString(o)&&a.push("domain="+o),!0===i&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},N=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)},M=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e},A=m,K=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],V=m,L=V.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=V.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0};function j(e){this.message=e}j.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},j.prototype.__CANCEL__=!0;var P=j,F=m,B=function(e,t,n){var r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(I("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)},D=R,q=T,H=function(e,t){return e&&!N(t)?M(e,t):t},J=function(e){var t,n,r,o={};return e?(A.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=A.trim(e.substr(0,r)).toLowerCase(),n=A.trim(e.substr(r+1)),t){if(o[t]&&K.indexOf(t)>=0)return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([n]):o[t]?o[t]+", "+n:n}})),o):o},G=L,X=_,Y=oe,z=P,$=function(e){return new Promise((function(t,n){var r,o=e.data,i=e.headers,a=e.responseType;function s(){e.cancelToken&&e.cancelToken.unsubscribe(r),e.signal&&e.signal.removeEventListener("abort",r)}F.isFormData(o)&&delete i["Content-Type"];var c=new XMLHttpRequest;if(e.auth){var u=e.auth.username||"",f=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";i.Authorization="Basic "+btoa(u+":"+f)}var l=H(e.baseURL,e.url);function d(){if(c){var r="getAllResponseHeaders"in c?J(c.getAllResponseHeaders()):null,o={data:a&&"text"!==a&&"json"!==a?c.response:c.responseText,status:c.status,statusText:c.statusText,headers:r,config:e,request:c};B((function(e){t(e),s()}),(function(e){n(e),s()}),o),c=null}}if(c.open(e.method.toUpperCase(),q(l,e.params,e.paramsSerializer),!0),c.timeout=e.timeout,"onloadend"in c?c.onloadend=d:c.onreadystatechange=function(){c&&4===c.readyState&&(0!==c.status||c.responseURL&&0===c.responseURL.indexOf("file:"))&&setTimeout(d)},c.onabort=function(){c&&(n(X("Request aborted",e,"ECONNABORTED",c)),c=null)},c.onerror=function(){n(X("Network Error",e,null,c)),c=null},c.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",r=e.transitional||Y.transitional;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(X(t,e,r.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",c)),c=null},F.isStandardBrowserEnv()){var p=(e.withCredentials||G(l))&&e.xsrfCookieName?D.read(e.xsrfCookieName):void 0;p&&(i[e.xsrfHeaderName]=p)}"setRequestHeader"in c&&F.forEach(i,(function(e,t){void 0===o&&"content-type"===t.toLowerCase()?delete i[t]:c.setRequestHeader(t,e)})),F.isUndefined(e.withCredentials)||(c.withCredentials=!!e.withCredentials),a&&"json"!==a&&(c.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&c.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&c.upload&&c.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(r=function(e){c&&(n(!e||e&&e.type?new z("canceled"):e),c.abort(),c=null)},e.cancelToken&&e.cancelToken.subscribe(r),e.signal&&(e.signal.aborted?r():e.signal.addEventListener("abort",r))),o||(o=null),c.send(o)}))},W=m,Q=function(e,t){O.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},Z=S,ee={"Content-Type":"application/x-www-form-urlencoded"};function te(e,t){!W.isUndefined(e)&&W.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var ne,re={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(ne=$),ne),transformRequest:[function(e,t){return Q(t,"Accept"),Q(t,"Content-Type"),W.isFormData(e)||W.isArrayBuffer(e)||W.isBuffer(e)||W.isStream(e)||W.isFile(e)||W.isBlob(e)?e:W.isArrayBufferView(e)?e.buffer:W.isURLSearchParams(e)?(te(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):W.isObject(e)||t&&"application/json"===t["Content-Type"]?(te(t,"application/json"),function(e,t,n){if(W.isString(e))try{return(t||JSON.parse)(e),W.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||re.transitional,n=t&&t.silentJSONParsing,r=t&&t.forcedJSONParsing,o=!n&&"json"===this.responseType;if(o||r&&W.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(o){if("SyntaxError"===e.name)throw Z(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};W.forEach(["delete","get","head"],(function(e){re.headers[e]={}})),W.forEach(["post","put","patch"],(function(e){re.headers[e]=W.merge(ee)}));var oe=re,ie=m,ae=oe,se=function(e){return!(!e||!e.__CANCEL__)},ce=m,ue=function(e,t,n){var r=this||ae;return ie.forEach(n,(function(n){e=n.call(r,e,t)})),e},fe=se,le=oe,de=P;function pe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new de("canceled")}var he=m,ge=function(e,t){t=t||{};var n={};function r(e,t){return he.isPlainObject(e)&&he.isPlainObject(t)?he.merge(e,t):he.isPlainObject(t)?he.merge({},t):he.isArray(t)?t.slice():t}function o(n){return he.isUndefined(t[n])?he.isUndefined(e[n])?void 0:r(void 0,e[n]):r(e[n],t[n])}function i(e){if(!he.isUndefined(t[e]))return r(void 0,t[e])}function a(n){return he.isUndefined(t[n])?he.isUndefined(e[n])?void 0:r(void 0,e[n]):r(void 0,t[n])}function s(n){return n in t?r(e[n],t[n]):n in e?r(void 0,e[n]):void 0}var c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s};return he.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=c[e]||o,r=t(e);he.isUndefined(r)&&t!==s||(n[e]=r)})),n},ve="0.23.0",Ee=ve,me={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){me[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var ye={};me.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Ee+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,o,i){if(!1===e)throw new Error(r(o," has been removed"+(t?" in "+t:"")));return t&&!ye[o]&&(ye[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,i)}};var we={assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var r=Object.keys(e),o=r.length;o-- >0;){var i=r[o],a=t[i];if(a){var s=e[i],c=void 0===s||a(s,i,e);if(!0!==c)throw new TypeError("option "+i+" must be "+c)}else if(!0!==n)throw Error("Unknown option "+i)}},validators:me},Te=m,be=T,xe=k,ke=function(e){return pe(e),e.headers=e.headers||{},e.data=ue.call(e,e.data,e.headers,e.transformRequest),e.headers=ce.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),ce.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||le.adapter)(e).then((function(t){return pe(e),t.data=ue.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return fe(t)||(pe(e),t&&t.response&&(t.response.data=ue.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},Oe=ge,Se=we,Ce=Se.validators;function _e(e){this.defaults=e,this.interceptors={request:new xe,response:new xe}}_e.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=Oe(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&Se.assertOptions(t,{silentJSONParsing:Ce.transitional(Ce.boolean),forcedJSONParsing:Ce.transitional(Ce.boolean),clarifyTimeoutError:Ce.transitional(Ce.boolean)},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var o,i=[];if(this.interceptors.response.forEach((function(e){i.push(e.fulfilled,e.rejected)})),!r){var a=[ke,void 0];for(Array.prototype.unshift.apply(a,n),a=a.concat(i),o=Promise.resolve(e);a.length;)o=o.then(a.shift(),a.shift());return o}for(var s=e;n.length;){var c=n.shift(),u=n.shift();try{s=c(s)}catch(e){u(e);break}}try{o=ke(s)}catch(e){return Promise.reject(e)}for(;i.length;)o=o.then(i.shift(),i.shift());return o},_e.prototype.getUri=function(e){return e=Oe(this.defaults,e),be(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Te.forEach(["delete","get","head","options"],(function(e){_e.prototype[e]=function(t,n){return this.request(Oe(n||{},{method:e,url:t,data:(n||{}).data}))}})),Te.forEach(["post","put","patch"],(function(e){_e.prototype[e]=function(t,n,r){return this.request(Oe(r||{},{method:e,url:t,data:n}))}}));var Ie=_e,Ue=P;function Re(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;this.promise.then((function(e){if(n._listeners){var t,r=n._listeners.length;for(t=0;t<r;t++)n._listeners[t](e);n._listeners=null}})),this.promise.then=function(e){var t,r=new Promise((function(e){n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e){n.reason||(n.reason=new Ue(e),t(n.reason))}))}Re.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},Re.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},Re.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},Re.source=function(){var e;return{token:new Re((function(t){e=t})),cancel:e}};var Ne=Re,Me=m,Ae=u,Ke=Ie,Ve=ge;var Le=function e(t){var n=new Ke(t),r=Ae(Ke.prototype.request,n);return Me.extend(r,Ke.prototype,n),Me.extend(r,n),r.create=function(n){return e(Ve(t,n))},r}(oe);Le.Axios=Ke,Le.Cancel=P,Le.CancelToken=Ne,Le.isCancel=se,Le.VERSION=ve,Le.all=function(e){return Promise.all(e)},Le.spread=function(e){return function(t){return e.apply(null,t)}},Le.isAxiosError=function(e){return"object"==typeof e&&!0===e.isAxiosError},c.exports=Le,c.exports.default=Le;var je=c.exports,Pe="http://guc3-api.cloud-dev.geega.com",Fe="https://guc3-api-test.geega.com",Be="https://guc3-api-demo.geega.com",De="https://guc3-api.geega.com",qe={exports:{}};function He(e){this.message=e}He.prototype=new Error,He.prototype.name="InvalidCharacterError";var Je="undefined"!=typeof window&&window.atob&&window.atob.bind(window)||function(e){var t=String(e).replace(/=+$/,"");if(t.length%4==1)throw new He("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,r,o=0,i=0,a="";r=t.charAt(i++);~r&&(n=o%4?64*n+r:r,o++%4)?a+=String.fromCharCode(255&n>>(-2*o&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r);return a};function Ge(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw"Illegal base64url string!"}try{return function(e){return decodeURIComponent(Je(e).replace(/(.)/g,(function(e,t){var n=t.charCodeAt(0).toString(16).toUpperCase();return n.length<2&&(n="0"+n),"%"+n})))}(t)}catch(e){return Je(t)}}function Xe(e){this.message=e}function Ye(e,t){if("string"!=typeof e)throw new Xe("Invalid token specified");var n=!0===(t=t||{}).header?0:1;try{return JSON.parse(Ge(e.split(".")[n]))}catch(e){throw new Xe("Invalid token specified: "+e.message)}}Xe.prototype=new Error,Xe.prototype.name="InvalidTokenError";const ze=Ye;ze.default=Ye,ze.InvalidTokenError=Xe,qe.exports=ze;var $e=qe.exports,We={exports:{}};
/*! js-cookie v3.0.1 | MIT */
!function(e,t){e.exports=function(){function e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}function t(n,r){function o(t,o,i){if("undefined"!=typeof document){"number"==typeof(i=e({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in i)i[s]&&(a+="; "+s,!0!==i[s]&&(a+="="+i[s].split(";")[0]));return document.cookie=t+"="+n.write(o,t)+a}}function i(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],r={},o=0;o<t.length;o++){var i=t[o].split("="),a=i.slice(1).join("=");try{var s=decodeURIComponent(i[0]);if(r[s]=n.read(a,s),e===s)break}catch(e){}}return e?r[e]:r}}return Object.create({set:o,get:i,remove:function(t,n){o(t,"",e({},n,{expires:-1}))},withAttributes:function(n){return t(this.converter,e({},this.attributes,n))},withConverter:function(n){return t(e({},this.converter,n),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(n)}})}return t({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})}()}(We);var Qe=We.exports,Ze=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:window.location.hostname,o=n/60/60/24;Qe.set(e,t,{expires:o,domain:r})},et=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.hostname;return Qe.get(e,{domain:t})},tt=function(e,t){var n=document.cookie.match(/[^ =;]+(?==)/g);if(n)for(var r=n.length;r--;){var o=t||"/";Qe.remove(n[r],{path:o,domain:e})}},nt=function(e,t,n){if(n)for(var r=n.length;r--;){var o=t||"/";Qe.remove(n[r],{path:o,domain:e})}},rt="object"==typeof i&&i&&i.Object===Object&&i,ot="object"==typeof self&&self&&self.Object===Object&&self,it=(rt||ot||Function("return this")()).Symbol;it&&it.toStringTag;function at(e,t){for(var n=e.toString().length;n<t;)e="0"+e,n++;return e}it&&it.toStringTag;var st=function(e){var t=window.location.href,n=t.split("?")[0],r=[],o=-1!==t.indexOf("?")?t.split("?")[1]:"";if(""!==o){for(var i=(r=o.split("&")).length-1;i>=0;i-=1)r[i].split("=")[0]===e&&r.splice(i,1);r.length&&(n=n+"?"+r.join("&"))}return n};var ct=function(e){if(e)throw window.location.href=e,!0;console.error("[geega-js-guc-sdk] error: can not found URL")},ut={exports:{}};!function(e,t){!function(n){function r(e,t){var n;t=t||{},this._id=r._generateUUID(),this._promise=t.promise||Promise,this._frameId=t.frameId||"CrossStorageClient-"+this._id,this._origin=r._getOrigin(e),this._requests={},this._connected=!1,this._closed=!1,this._count=0,this._timeout=t.timeout||5e3,this._listener=null,this._installListener(),t.frameId&&(n=document.getElementById(t.frameId)),n&&this._poll(),n=n||this._createFrame(e),this._hub=n.contentWindow}r.frameStyle={display:"none",position:"absolute",top:"-999px",left:"-999px"},r._getOrigin=function(e){var t;return(t=document.createElement("a")).href=e,t.host||(t=window.location),((t.protocol&&":"!==t.protocol?t.protocol:window.location.protocol)+"//"+t.host).replace(/:80$|:443$/,"")},r._generateUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))},r.prototype.onConnect=function(){var e=this;return this._connected?this._promise.resolve():this._closed?this._promise.reject(new Error("CrossStorageClient has closed")):(this._requests.connect||(this._requests.connect=[]),new this._promise((function(t,n){var r=setTimeout((function(){n(new Error("CrossStorageClient could not connect"))}),e._timeout);e._requests.connect.push((function(e){if(clearTimeout(r),e)return n(e);t()}))})))},r.prototype.set=function(e,t){return this._request("set",{key:e,value:t})},r.prototype.get=function(e){var t=Array.prototype.slice.call(arguments);return this._request("get",{keys:t})},r.prototype.del=function(){var e=Array.prototype.slice.call(arguments);return this._request("del",{keys:e})},r.prototype.clear=function(){return this._request("clear")},r.prototype.getKeys=function(){return this._request("getKeys")},r.prototype.close=function(){var e=document.getElementById(this._frameId);e&&e.parentNode.removeChild(e),window.removeEventListener?window.removeEventListener("message",this._listener,!1):window.detachEvent("onmessage",this._listener),this._connected=!1,this._closed=!0},r.prototype._installListener=function(){var e=this;this._listener=function(t){var n,r,o;if(!e._closed&&t.data&&"string"==typeof t.data&&("null"===t.origin?"file://":t.origin)===e._origin)if("cross-storage:unavailable"!==t.data){if(-1!==t.data.indexOf("cross-storage:")&&!e._connected){if(e._connected=!0,!e._requests.connect)return;for(n=0;n<e._requests.connect.length;n++)e._requests.connect[n](r);delete e._requests.connect}if("cross-storage:ready"!==t.data){try{o=JSON.parse(t.data)}catch(e){return}o.id&&e._requests[o.id]&&e._requests[o.id](o.error,o.result)}}else{if(e._closed||e.close(),!e._requests.connect)return;for(r=new Error("Closing client. Could not access localStorage in hub."),n=0;n<e._requests.connect.length;n++)e._requests.connect[n](r)}},window.addEventListener?window.addEventListener("message",this._listener,!1):window.attachEvent("onmessage",this._listener)},r.prototype._poll=function(){var e,t,n;n="file://"===(e=this)._origin?"*":e._origin,t=setInterval((function(){if(e._connected)return clearInterval(t);e._hub&&e._hub.postMessage("cross-storage:poll",n)}),1e3)},r.prototype._createFrame=function(e){var t,n;for(n in(t=window.document.createElement("iframe")).id=this._frameId,r.frameStyle)r.frameStyle.hasOwnProperty(n)&&(t.style[n]=r.frameStyle[n]);return window.document.body.appendChild(t),t.src=e,t},r.prototype._request=function(e,t){var n,r;return this._closed?this._promise.reject(new Error("CrossStorageClient has closed")):((r=this)._count++,n={id:this._id+":"+r._count,method:"cross-storage:"+e,params:t},new this._promise((function(e,t){var o,i,a;o=setTimeout((function(){r._requests[n.id]&&(delete r._requests[n.id],t(new Error("Timeout: could not perform "+n.method)))}),r._timeout),r._requests[n.id]=function(i,a){if(clearTimeout(o),delete r._requests[n.id],i)return t(new Error(i));e(a)},Array.prototype.toJSON&&(i=Array.prototype.toJSON,Array.prototype.toJSON=null),a="file://"===r._origin?"*":r._origin,r._hub.postMessage(JSON.stringify(n),a),i&&(Array.prototype.toJSON=i)})))},e.exports?e.exports=r:t.CrossStorageClient=r}()}(ut,ut.exports);var ft={exports:{}};!function(e,t){var n;n={init:function(e){var t=!0;try{window.localStorage||(t=!1)}catch(e){t=!1}if(!t)try{return window.parent.postMessage("cross-storage:unavailable","*")}catch(e){return}n._permissions=e||[],n._installListener(),window.parent.postMessage("cross-storage:ready","*")},_installListener:function(){var e=n._listener;window.addEventListener?window.addEventListener("message",e,!1):window.attachEvent("onmessage",e)},_listener:function(e){var t,r,o,i,a,s,c;if(t="null"===e.origin?"file://":e.origin,"cross-storage:poll"===e.data)return window.parent.postMessage("cross-storage:ready",e.origin);if("cross-storage:ready"!==e.data){try{o=JSON.parse(e.data)}catch(e){return}if(o&&"string"==typeof o.method&&(i=o.method.split("cross-storage:")[1])){if(n._permitted(t,i))try{s=n["_"+i](o.params)}catch(e){a=e.message}else a="Invalid permissions for "+i;c=JSON.stringify({id:o.id,error:a,result:s}),r="file://"===t?"*":t,window.parent.postMessage(c,r)}}},_permitted:function(e,t){var r,o,i;if(r=["get","set","del","clear","getKeys"],!n._inArray(t,r))return!1;for(o=0;o<n._permissions.length;o++)if((i=n._permissions[o]).origin instanceof RegExp&&i.allow instanceof Array&&i.origin.test(e)&&n._inArray(t,i.allow))return!0;return!1},_set:function(e){window.localStorage.setItem(e.key,e.value)},_get:function(e){var t,n,r,o;for(t=window.localStorage,n=[],r=0;r<e.keys.length;r++){try{o=t.getItem(e.keys[r])}catch(e){o=null}n.push(o)}return n.length>1?n:n[0]},_del:function(e){for(var t=0;t<e.keys.length;t++)window.localStorage.removeItem(e.keys[t])},_clear:function(){window.localStorage.clear()},_getKeys:function(e){var t,n,r;for(r=[],n=window.localStorage.length,t=0;t<n;t++)r.push(window.localStorage.key(t));return r},_inArray:function(e,t){for(var n=0;n<t.length;n++)if(e===t[n])return!0;return!1},_now:function(){return"function"==typeof Date.now?Date.now():(new Date).getTime()}},e.exports?e.exports=n:t.CrossStorageHub=n}(ft,ft.exports);var lt={CrossStorageClient:ut.exports,CrossStorageHub:ft.exports},dt=function(){var e=t(s.mark((function e(t,n,r){var o,i;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=r,r||("dev"===n&&(o=Pe),"test"===n&&(o=Fe),"demo"===n&&(o=Be),"prod"===n&&(o=De)),i=new(0,lt.CrossStorageClient)("".concat(o,"/api/guc/view/token/hub?appId=").concat(t)),e.prev=4,e.next=7,i.onConnect();case 7:return e.abrupt("return",i);case 10:throw e.prev=10,e.t0=e.catch(4),console.log(e.t0),new Error("GUC server connect hub error:".concat(JSON.stringify(e.t0)));case 14:case"end":return e.stop()}}),e,null,[[4,10]])})));return function(t,n,r){return e.apply(this,arguments)}}(),pt="0000",ht=r((function e(n){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),o(this,"TOKENKEYV2",void 0),o(this,"REFRESH_TOKENV2",void 0),o(this,"TOKEN_TIMEOUTV2",void 0),o(this,"TOKENEXPIRESTIMEV2",void 0),o(this,"USERACTIVEV2",void 0),o(this,"USERMOVEMOUSELASTTIMEV2",void 0),o(this,"defaultconfigs",void 0),o(this,"config",void 0),o(this,"storage",void 0),o(this,"isCClient",void 0),o(this,"isBClient",void 0),o(this,"GUCUSERINFO_LOCALSTORAGE_KEY",void 0),o(this,"TOKENEXPIRESTIME",void 0),o(this,"TOKEN",void 0),o(this,"REFRESH_TOKEN",void 0),o(this,"TOKEN_TIMEOUT",void 0),o(this,"USERACTIVE",void 0),o(this,"USERMOVEMOUSELASTTIME",void 0),o(this,"isAutoRefsToken",void 0),o(this,"cureetUserIsActive",void 0),o(this,"detectionFrequency",void 0),o(this,"remainingExpirationBoundary",void 0),o(this,"domain",void 0),o(this,"dispatchUserActiveTimer",void 0),o(this,"keepingCheckViewPageTimer",void 0),o(this,"mainDomain",void 0),o(this,"isKeepMoveMouseEvent",void 0),o(this,"init",t(s.mark((function e(){var t,n,o,i,a,c,u,f,l,d,p,h,g,v=arguments;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(v.length>0&&void 0!==v[0])||v[0],n=v.length>1&&void 0!==v[1]?v[1]:function(){},o=!1,i=r,a=null,c=new URLSearchParams(window.location.search).get("code"),e.prev=6,e.next=9,dt(r.config.appId,r.config.env,r.config.server);case 9:r.storage=e.sent,e.next=18;break;case 12:e.prev=12,e.t0=e.catch(6),r.storage=null,r.config.enableCookieMode=!0,console.warn("[geega-js-sdk] cross-storage: ".concat(e.t0)),console.warn("[geega-js-sdk] warning : chrome  private /Incognito mode : not support localstorage cross-storage");case 18:return r.clearUrlCode(),e.next=21,r.pushSDKV2CredentialsToHubServer();case 21:if(!c){e.next=34;break}return e.prev=22,e.next=25,je.get("".concat(i.config.server,"/api/guc/protocol/oauth2/token/by-code?code=").concat(c,"&appId=").concat(i.config.appId,"&redirectUri=").concat(window.btoa(st("code"))));case 25:a=e.sent,e.next=33;break;case 28:return e.prev=28,e.t1=e.catch(22),console.error("[geega-js-guc-sdk]: get token by code error: code is ".concat(c,";  error:").concat(JSON.stringify(e.t1))),i.reloadUrl(),e.abrupt("return",!1);case 33:200===a.status&&a.data.code===pt?a.data.result&&a.data.result.accessToken?(u=a.data.result.accessToken,f=a.data.result.refreshToken,l=a.data.result.expiresIn,d=r.config.debug?r.config.tokenExpiresTime:1e3*(null===(p=r.decodeExp(u))||void 0===p?void 0:p.exp),r.storeCredentialsToHub(u,f,l,d)):(r.isKeepMoveMouseEvent=!1,console.error("[geega-js-guc-sdk]: can not get token  result:".concat(JSON.stringify(a)))):(r.isKeepMoveMouseEvent=!1,console.error("[geega-js-guc-sdk]: get token by code error: code is ".concat(c," [GUC server] : ").concat(null===(h=a)||void 0===h||null===(g=h.data)||void 0===g?void 0:g.msg)),i.reloadUrl());case 34:return e.next=36,i.verifyToken();case 36:return(o=e.sent)?(i.isAutoRefsToken&&(r.isKeepMoveMouseEvent=!0,i._dispatchUserActive(),i._keepUseViewPageHandler()),n&&n()):(r.isKeepMoveMouseEvent=!1,t&&i.login()),e.abrupt("return",o);case 39:case"end":return e.stop()}}),e,null,[[6,12],[22,28]])})))),o(this,"storeCredentialsToHub",function(){var e=t(s.mark((function e(t,n,o,i){var a;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.config.isStoreCookie&&(Ze(r.TOKEN,t,o),Ze(r.REFRESH_TOKEN,n,o),Ze(r.TOKEN_TIMEOUT,o.toString(),o),Ze(r.TOKENEXPIRESTIME,i.toString(),o)),!r.config.enableCookieMode){e.next=8;break}Ze(r.TOKENKEYV2,t,o,r.mainDomain),Ze(r.REFRESH_TOKENV2,n,o,r.mainDomain),Ze(r.TOKEN_TIMEOUTV2,o.toString(),o,r.mainDomain),Ze(r.TOKENEXPIRESTIMEV2,i.toString(),o,r.mainDomain),e.next=19;break;case 8:return e.next=10,r.storage;case 10:return a=e.sent,e.next=13,a.set(r.TOKEN,t);case 13:return e.next=15,a.set(r.REFRESH_TOKEN,n);case 15:return e.next=17,a.set(r.TOKEN_TIMEOUT,o.toString());case 17:return e.next=19,a.set(r.TOKENEXPIRESTIME,i.toString());case 19:case"end":return e.stop()}}),e)})));return function(t,n,r,o){return e.apply(this,arguments)}}()),o(this,"storeUserAtiveStatusToHub",function(){var e=t(s.mark((function e(t,n){var o,i,a=arguments;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=a.length>2&&void 0!==a[2]?a[2]:1,e.next=3,r.storage;case 3:if(i=e.sent,1!=o){e.next=12;break}if(r.config.isStoreCookie&&Ze(r.USERACTIVE,t,n),!r.config.enableCookieMode){e.next=10;break}Ze(r.USERACTIVEV2,t,n,r.mainDomain),e.next=12;break;case 10:return e.next=12,null==i?void 0:i.set(r.USERACTIVE,t);case 12:if(2!=o){e.next=20;break}if(r.config.isStoreCookie&&Ze(r.USERMOVEMOUSELASTTIME,t,n),!r.config.enableCookieMode){e.next=18;break}Ze(r.USERMOVEMOUSELASTTIMEV2,t,n,r.mainDomain),e.next=20;break;case 18:return e.next=20,null==i?void 0:i.set(r.USERMOVEMOUSELASTTIME,t);case 20:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),o(this,"getValuesByKeyFromHubServer",function(){var e=t(s.mark((function e(n){var o,i,a,c,u,f,l,d,p,h,g,v,E,m,y,w;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.storage;case 2:if(o=e.sent,!n){e.next=21;break}if("string"!=typeof n){e.next=14;break}if(!r.config.enableCookieMode){e.next=10;break}return i=r.getCookie(n,r.mainDomain),e.abrupt("return",i?[i]:[]);case 10:return e.next=12,null==o?void 0:o.get(n);case 12:return a=e.sent,e.abrupt("return",a?[a]:[]);case 14:if(!(n instanceof Array)){e.next=19;break}return e.next=17,Promise.all(n.map(function(){var e=t(s.mark((function e(t){var n;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=null,!r.config.enableCookieMode){e.next=5;break}n=r.getCookie(t,r.mainDomain),e.next=8;break;case 5:return e.next=7,null==o?void 0:o.get(t);case 7:n=e.sent;case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 17:return c=e.sent,e.abrupt("return",c||[]);case 19:e.next=53;break;case 21:if(!r.config.enableCookieMode){e.next=33;break}return e.next=24,r.getToken();case 24:return u=e.sent,f=r.getCookie(r.REFRESH_TOKENV2,r.mainDomain),l=r.getCookie(r.TOKEN_TIMEOUTV2,r.mainDomain),d=r.getCookie(r.TOKENEXPIRESTIMEV2,r.mainDomain),p=r.getCookie(r.USERACTIVEV2,r.mainDomain),h=r.getCookie(r.USERMOVEMOUSELASTTIMEV2,r.mainDomain),e.abrupt("return",[u,f,l,d,p,h]);case 33:if(!o){e.next=53;break}return e.next=36,o.get(r.TOKEN);case 36:return g=e.sent,e.next=39,o.get(r.REFRESH_TOKEN);case 39:return v=e.sent,e.next=42,o.get(r.TOKEN_TIMEOUT);case 42:return E=e.sent,e.next=45,o.get(r.TOKENEXPIRESTIME);case 45:return m=e.sent,e.next=48,o.get(r.USERACTIVE);case 48:return y=e.sent,e.next=51,o.get(r.USERMOVEMOUSELASTTIME);case 51:return w=e.sent,e.abrupt("return",[g,v,E,m,y,w]);case 53:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),o(this,"initVersion2Prop",(function(){var e=r.config.env;r.TOKENKEYV2=r.config.isClient?"c-".concat(e,"-guc3-token"):"b-".concat(e,"-guc3-token"),r.REFRESH_TOKENV2=r.config.isClient?"c-".concat(e,"-guc3-refresh-token"):"b-".concat(e,"-guc3-refresh-token"),r.TOKEN_TIMEOUTV2=r.config.isClient?"c-".concat(e,"-guc3-token-timeout"):"b-".concat(e,"-guc3-token-timeout"),r.TOKENEXPIRESTIMEV2=r.config.isClient?"c-".concat(e,"-token-expires-time-key"):"b-".concat(e,"-token-expires-time-key"),r.USERACTIVEV2=r.config.isClient?"c-".concat(e,"-guc3-is-active"):"b-".concat(e,"-guc3-is-active"),r.USERMOVEMOUSELASTTIMEV2=r.config.isClient?"c-".concat(e,"-guc3-move-lasttime"):"b-".concat(e,"-guc3-move-lasttime")})),o(this,"pushSDKV2CredentialsToHubServer",t(s.mark((function e(){var t,n,o,i,a,c,u,f,l;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r.config.enableCookieMode){e.next=2;break}return e.abrupt("return");case 2:if(t=r.mainDomain,n=et(r.TOKENKEYV2,t),o=et(r.REFRESH_TOKENV2,t),!n||!o){e.next=30;break}return e.next=8,r.storage;case 8:return i=e.sent,a=et(r.TOKEN_TIMEOUTV2,t),c=et(r.TOKENEXPIRESTIMEV2,t),u=et(r.USERACTIVEV2,t),f=et(r.USERMOVEMOUSELASTTIMEV2,t),e.next=15,i.set(r.TOKEN,n);case 15:return e.next=17,i.set(r.REFRESH_TOKEN,o);case 17:return e.next=19,i.set(r.TOKEN_TIMEOUT,a);case 19:return e.next=21,i.set(r.TOKENEXPIRESTIME,c);case 21:return e.next=23,r.getUserInfo();case 23:return l=e.sent,e.next=26,i.set(r.GUCUSERINFO_LOCALSTORAGE_KEY,JSON.stringify(l));case 26:return e.next=28,i.set(r.USERACTIVE,u);case 28:return e.next=30,i.set(r.USERMOVEMOUSELASTTIME,f);case 30:case"end":return e.stop()}}),e)})))),o(this,"clearUrlCode",(function(){var e=0,t=1,n=500;!function r(){e=setInterval((function(){location.search.indexOf("code")>-1?(history.replaceState(null,"null",st("code")+location.hash),n=500*(t+=1),clearInterval(e),r()):(t>9&&clearInterval(e),t+=1)}),n)}()})),o(this,"reloadUrl",(function(){console.error("[geega-js-guc-sdk] will clear cookie and reload your page after 3s"),setTimeout(t(s.mark((function e(){var t;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.clearGUCBrowserCertificate();case 2:t=st("code"),ct(t);case 4:case"end":return e.stop()}}),e)}))),3e3)})),o(this,"decodeJwt",(function(e){return $e(e)})),o(this,"decodeExp",(function(e){return $e(e)})),o(this,"initMousemoveEvent",(function(){var e=r;document.addEventListener("mousemove",function(){var n=t(s.mark((function t(n){var o,i,a;return s.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isKeepMoveMouseEvent){t.next=8;break}return t.next=3,e.getValuesByKeyFromHubServer(r.config.enableCookieMode?e.TOKEN_TIMEOUTV2:e.TOKEN_TIMEOUT);case 3:return o=t.sent,i=o[0],a=(new Date).getTime(),t.next=8,e.storeUserAtiveStatusToHub(a.toString(),i,2);case 8:case"end":return t.stop()}}),t)})));return function(e){return n.apply(this,arguments)}}(),!1)})),o(this,"_dispatchUserActive",(function(){var e=r;e.dispatchUserActiveTimer&&clearInterval(e.dispatchUserActiveTimer),e.dispatchUserActiveTimer=setInterval(t(s.mark((function t(){var n,r,o,i,a;return s.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=(new Date).getTime(),t.next=3,e.getValuesByKeyFromHubServer([e.config.enableCookieMode?e.TOKENEXPIRESTIMEV2:e.TOKENEXPIRESTIME,e.config.enableCookieMode?e.USERMOVEMOUSELASTTIMEV2:e.USERMOVEMOUSELASTTIME,e.config.enableCookieMode?e.TOKEN_TIMEOUTV2:e.TOKEN_TIMEOUT]);case 3:if(r=t.sent,o=Number(r[0]),i=Number(r[1]),a=Number(r[2]),!(n-i>e.config.liveTimeOut)){t.next=10;break}return t.next=10,e.storeUserAtiveStatusToHub("0",a,1);case 10:if(!(o-n>e.config.minTime&&n-i<e.config.liveTimeOut)){t.next=13;break}return t.next=13,e.storeUserAtiveStatusToHub("1",a,1);case 13:case"end":return t.stop()}}),t)}))),e.config.detectionFrequency)})),o(this,"_keepUseViewPageHandler",(function(){var e=r;e.keepingCheckViewPageTimer&&clearInterval(e.keepingCheckViewPageTimer),e.keepingCheckViewPageTimer=setInterval(t(s.mark((function t(){var n,o,i,a;return s.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r.getValuesByKeyFromHubServer([e.config.enableCookieMode?e.USERACTIVEV2:e.USERACTIVE,e.config.enableCookieMode?e.TOKENEXPIRESTIMEV2:e.TOKENEXPIRESTIME]);case 2:n=t.sent,o="1"===n[0],i=Number(n[1]),a=(new Date).getTime(),o?i-a<e.config.remainingExpirationBoundary&&e.refreshToken():i-a<e.config.logoutExpirationBoundary&&e.logout();case 7:case"end":return t.stop()}}),t)}))),e.config.viewPageFrequency)})),o(this,"refreshToken",t(s.mark((function e(){var n,o,i;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r,e.next=3,r.getValuesByKeyFromHubServer(r.config.enableCookieMode?r.REFRESH_TOKENV2:r.REFRESH_TOKEN);case 3:return o=e.sent,i=o[0],e.abrupt("return",new Promise((function(e,o){if(!i)throw new Error("[geega-js-guc-sdk]: can not find refreshToken ");try{je.get("".concat(n.config.server,"/api/guc/token/refresh?refreshToken=").concat(i),{}).then(function(){var i=t(s.mark((function t(i){var a,c,u,f,l,d;return s.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(200!==i.status){t.next=14;break}if(i.data.code!==pt){t.next=11;break}return c=i.data.result.accessToken,u=i.data.result.refreshToken,f=i.data.result.expiresIn,l=r.config.debug?r.config.tokenExpiresTime:1e3*(null===(d=r.decodeExp(c))||void 0===d?void 0:d.exp),t.next=8,r.storeCredentialsToHub(c,u,f,l);case 8:e(null==i||null===(a=i.data)||void 0===a?void 0:a.result),t.next=14;break;case 11:clearInterval(n.dispatchUserActiveTimer),clearInterval(n.keepingCheckViewPageTimer),o({});case 14:case"end":return t.stop()}}),t)})));return function(e){return i.apply(this,arguments)}}())}catch(e){throw n.logout(),new Error("[geega-js-guc-sdk]: refresh token error: refresh token is [".concat(i,"];  catch error:").concat(JSON.stringify(e)))}})));case 6:case"end":return e.stop()}}),e)})))),o(this,"getUserInfo",t(s.mark((function e(){var t,n,o,i,a,c,u,f,l=arguments;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=l.length>0&&void 0!==l[0]&&l[0],e.next=3,r.storage;case 3:if(n=e.sent,o=r.isCClient?"".concat(r.config.server,"/api/guc/customer/current/customer-info"):"".concat(r.config.server,"/api/guc/current/user-info"),i="",a={},r.config.enableCookieMode||!n){e.next=12;break}return e.next=10,n.get(r.GUCUSERINFO_LOCALSTORAGE_KEY);case 10:i=e.sent,a=JSON.parse(i);case 12:if(c={},!(t&&a&&Object.keys(a).length>0)){e.next=17;break}c=a,e.next=29;break;case 17:return e.next=19,r.getToken();case 19:return u=e.sent,e.next=22,je.get(o,{headers:{Authorization:"Bearer ".concat(u)}});case 22:if(200!==(f=e.sent).status){e.next=29;break}if(f.data.code!==pt){e.next=29;break}if(c=f.data.result,r.config.enableCookieMode){e.next=29;break}return e.next=29,n.set(r.GUCUSERINFO_LOCALSTORAGE_KEY,JSON.stringify(c));case 29:return e.abrupt("return","string"==typeof c?JSON.parse(c):c);case 30:case"end":return e.stop()}}),e)})))),o(this,"verifyToken",t(s.mark((function e(){var t,n,o,i;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.getToken();case 2:if(t=e.sent,n=!1,!t){e.next=21;break}return e.prev=5,e.next=8,je.get("".concat(r.config.server,"/api/guc/token/verify"),{headers:{Authorization:"Bearer ".concat(t)}});case 8:if(200!==(o=e.sent).status){e.next=13;break}n=o.data.result||!0,e.next=15;break;case 13:throw r.logout(),new Error("[geega-js-guc-sdk] verify token [".concat(t,"] :  ").concat(null==o||null===(i=o.data)||void 0===i?void 0:i.msg));case 15:e.next=21;break;case 17:throw e.prev=17,e.t0=e.catch(5),r.logout(),new Error("[GUC server] verify token error: token [".concat(t,"], catch error:").concat(JSON.stringify(e.t0)));case 21:return e.abrupt("return",n);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})))),o(this,"getMenusTree",function(){var e=t(s.mark((function e(t){var n,o,i;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return console.error("appId 必传"),e.abrupt("return");case 3:return n=[],e.next=6,r.getToken();case 6:return o=e.sent,e.next=9,je.get("".concat(r.config.server,"/api/guc/current/menu/tree?appId=").concat(t),{headers:{Authorization:"Bearer ".concat(o)}});case 9:return(i=e.sent).data.code===pt&&(n=i.data.result),e.abrupt("return",n);case 12:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),o(this,"getButtonsList",function(){var e=t(s.mark((function e(t){var n,o,i;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return console.error("appId 必传"),e.abrupt("return");case 3:return n=[],e.next=6,r.getToken();case 6:return o=e.sent,e.next=9,je.get("".concat(r.config.server,"/api/guc/current/buttons?appId=").concat(t),{headers:{Authorization:"Bearer ".concat(o)}});case 9:return(i=e.sent).data.code===pt&&(n=i.data.result),e.abrupt("return",n);case 12:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),o(this,"getCookie",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.hostname;return et(e,t)})),o(this,"getToken",t(s.mark((function e(){var t,n,o;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r.config.enableCookieMode){e.next=5;break}return t=r.getCookie(r.TOKENKEYV2,r.mainDomain),e.abrupt("return",Promise.resolve(t));case 5:return e.next=7,r.storage;case 7:return n=e.sent,e.next=10,n.get(r.TOKEN);case 10:return o=e.sent,e.abrupt("return",o);case 12:case"end":return e.stop()}}),e)})))),o(this,"DelCookie",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return tt(e,t)})),o(this,"login",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(r.isBClient){var t=e||st("code");ct("".concat(r.config.server,"/api/guc/protocol/oauth2/auth?appId=").concat(r.config.appId,"&redirectUri=").concat(window.btoa(t)))}if(r.isCClient){var n=e||st("code");ct("".concat(r.config.server,"/api/guc/protocol/oauth2/auth?appId=").concat(r.config.appId,"&redirectUri=").concat(window.btoa(n),"&page_type=c"))}})),o(this,"clearGUCBrowserCertificate",t(s.mark((function e(){var t;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(localStorage.removeItem(r.GUCUSERINFO_LOCALSTORAGE_KEY),localStorage.removeItem(r.TOKENEXPIRESTIME),r.config.enableCookieMode){e.next=8;break}return e.next=5,r.storage;case 5:return t=e.sent,e.next=8,t.del(r.TOKEN,r.REFRESH_TOKEN,r.TOKEN_TIMEOUT,r.TOKENEXPIRESTIME,r.USERACTIVE,r.USERMOVEMOUSELASTTIME,r.TOKENKEYV2,r.REFRESH_TOKENV2,r.TOKEN_TIMEOUTV2,r.TOKENEXPIRESTIMEV2);case 8:nt(r.mainDomain,"/",[r.TOKEN,r.REFRESH_TOKEN,r.TOKEN_TIMEOUT,r.TOKENEXPIRESTIME,r.TOKENKEYV2,r.REFRESH_TOKENV2,r.TOKEN_TIMEOUTV2,r.TOKENEXPIRESTIMEV2]),nt(".geega.com","/",[r.TOKEN,r.REFRESH_TOKEN,r.TOKEN_TIMEOUT,r.TOKENEXPIRESTIME,r.TOKENKEYV2,r.REFRESH_TOKENV2,r.TOKEN_TIMEOUTV2,r.TOKENEXPIRESTIMEV2]),r.dispatchUserActiveTimer&&clearInterval(r.dispatchUserActiveTimer),r.keepingCheckViewPageTimer&&clearInterval(r.keepingCheckViewPageTimer);case 12:case"end":return e.stop()}}),e)})))),o(this,"logout",t(s.mark((function e(){var t,n,o,i,a,c,u,f,l=arguments;return s.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=l.length>0&&void 0!==l[0]?l[0]:"",n=l.length>1&&void 0!==l[1]&&l[1],!r.isBClient){e.next=15;break}return e.next=5,r.getToken();case 5:if(o=e.sent,i=t||st("code"),a=i,!n){e.next=12;break}return e.next=11,r.clearGUCBrowserCertificate();case 11:a="".concat(r.config.server,"/api/guc/protocol/oauth2/auth?appId=").concat(r.config.appId,"&redirectUri=").concat(window.btoa(i));case 12:return e.next=14,r.clearGUCBrowserCertificate();case 14:ct("".concat(r.config.server,"/api/guc/protocol/oauth2/logout?token=").concat(o,"&redirectUri=").concat(window.btoa(a)));case 15:if(!r.isCClient){e.next=28;break}return e.next=18,r.getToken();case 18:if(c=e.sent,u=t||st("code"),f=u,!n){e.next=25;break}f="".concat(r.config.server,"/api/guc/protocol/oauth2/auth?appId=").concat(r.config.appId,"&redirectUri=").concat(window.btoa(u),"&page_type=c"),e.next=28;break;case 25:return e.next=27,r.clearGUCBrowserCertificate();case 27:ct("".concat(r.config.server,"/api/guc/protocol/oauth2/logout?token=").concat(c,"&redirectUri=").concat(window.btoa(f)));case 28:case"end":return e.stop()}}),e)})))),o(this,"getConfigInfo",(function(){return Promise.resolve(r.config)})),this.defaultconfigs={env:"dev",appId:"1",isClient:!1,liveTimeOut:36e4,minTime:12e4,isAutoRefsToken:!0,detectionFrequency:6e4,viewPageFrequency:12e4,remainingExpirationBoundary:12e5,logoutExpirationBoundary:6e5,debug:!1,tokenExpiresTime:(new Date).getTime(),enableCookieMode:!1,isStoreCookie:!1,server:"",version:"3.0.83"};var i=Object.assign({},this.defaultconfigs,n);this.config=i,this.config.debug&&(console.log("检测间隔detectionFrequency值:【".concat(this.config.detectionFrequency/1e3,"s】")),console.log("剩余remainingExpirationBoundary值:【".concat(this.config.remainingExpirationBoundary/1e3/60,"min】")),console.log("活跃存活liveTimeOut值为:【".concat(this.config.liveTimeOut/1e3,"s】")),console.log("debug设置token过期tokenExpiresTime值为:【".concat(function(e){var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate()," ").concat(at(t.getHours(),2),":").concat(at(t.getMinutes(),2),":").concat(at(t.getMilliseconds(),2))}(this.config.tokenExpiresTime),"】"))),this.storage=null,this.isKeepMoveMouseEvent=this.config.isAutoRefsToken,this.config.appId||console.error("geega-guc-js-next-sdk error: appId doesnot exist");var a=this.config.server,c=this.config.env;this.isCClient=this.config.isClient,this.isBClient=!this.config.isClient,c||console.error("geega-guc-js-next-sdk error: env doesnot exist"),this.config.server||("dev"===c&&(a=Pe),"test"===c&&(a=Fe),"demo"===c&&(a=Be),"prod"===c&&(a=De)),this.GUCUSERINFO_LOCALSTORAGE_KEY=this.config.isClient?"c-guc-user-info-localstorage-key":"b-guc-user-info-localstorage-key",this.TOKENEXPIRESTIME=this.config.isClient?"c-token-expires-time-key":"b-token-expires-time-key",this.TOKEN=this.config.isClient?"c-guc3-token":"b-guc3-token",this.REFRESH_TOKEN=this.config.isClient?"c-guc3-refresh-token":"b-guc3-refresh-token",this.TOKEN_TIMEOUT=this.config.isClient?"c-guc3-token-timeout":"b-guc3-token-timeout",this.USERACTIVE=this.config.isClient?"c-guc3-is-active":"b-guc3-is-active",this.USERMOVEMOUSELASTTIME=this.config.isClient?"c-guc3-move-lasttime":"b-guc3-move-lasttime",this.config.server=a,this.isAutoRefsToken=this.config.isAutoRefsToken,this.cureetUserIsActive=!0,this.detectionFrequency=this.config.detectionFrequency,this.remainingExpirationBoundary=this.config.remainingExpirationBoundary,this.domain=window.location.host,this.dispatchUserActiveTimer=null,this.keepingCheckViewPageTimer=null,this.TOKENKEYV2="",this.REFRESH_TOKENV2="",this.TOKEN_TIMEOUTV2="",this.TOKENEXPIRESTIMEV2="",this.USERACTIVEV2="",this.USERMOVEMOUSELASTTIMEV2="",this.mainDomain=function(){if(e=window.location.hostname,/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(e))return window.location.hostname;var e,t=window.document.domain.split("."),n=t.length;return n>3?".".concat(t[n-3],".").concat(t[n-2],".").concat(t[n-1]):n>2?".".concat(t[n-2],".").concat(t[n-1]):1==n?"".concat(t[0]):void 0}(),console.log(this.mainDomain),this.initVersion2Prop(),this.initMousemoveEvent(),console.log("[guc-sdk-js] server URL:",this.config.server)}));return ht}));
