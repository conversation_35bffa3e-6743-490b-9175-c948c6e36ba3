<template>
	<view class="bc_f3f3f7 myContainerPage">
		<u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff"
			leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
		<!-- {{ nlsMap }} -->
		<view class="myContainer ma10">
			<u--form labelPosition="left" :model="model" labelWidth="100">
				<u-form-item label="操作类型" borderBottom required labelWidth="100">
					<u-radio-group v-model="model.czfs" placement="column" style="width: 100%;">
						<view style="display: flex;flex-direction: row-reverse">
							<u-radio v-for="item in radiolist1" :key="item.name" :customStyle="{ marginRight: '10rpx' }"
								:label="item.name" :name="item.name">
							</u-radio>
						</view>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="托盘号" borderBottom required labelWidth="100">
					<u--input v-model="model.durableName" border="none" placeholder="请扫描或输入"></u--input>
					<view class="iconfont icon-saoma" @click="scan('durableName')"></view>
				</u-form-item>
				<u-form-item label="库位号" borderBottom required labelWidth="100">
					<u--input v-model="model.stockLocationCode" border="none" placeholder="请扫描或输入"></u--input>
					<view class="iconfont icon-saoma" @click="scan('stockLocationCode')"></view>
				</u-form-item>

			</u--form>
			<view class="mt10">
				<u--form v-show="model.czfs === '电芯静置'" labelPosition="left" :model="modelOld" labelWidth="100">

					<u-form-item label="托盘号:" borderBottom labelWidth="100">
						<view class="w100x flex right"> {{ modelOld.durableName }} </view>
					</u-form-item>
					<u-form-item label="托盘类型:" borderBottom labelWidth="100">
						<view class="w100x flex right"> 电芯托盘 </view>
					</u-form-item>
					<u-form-item label="托盘状态:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.durableState }} </view>
					</u-form-item>

					<u-form-item label="托盘位置:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.stockLocationCode }}</view>
					</u-form-item>

					<u-form-item label="静置开始时间:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.startTime ?
							moment(modelOld.startTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
					</u-form-item>
					<u-form-item label="静置预计结束时间:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.endTime ?
							moment(modelOld.endTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
					</u-form-item>
					<u-form-item label="操作人:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.eventUser }}</view>
					</u-form-item>
					<u-form-item label="静置要求时长(Min):" borderBottom labelWidth="120">
						<u--input v-model="modelOld.standMinutes" type="number"></u--input>
					</u-form-item>
				</u--form>
				<u--form v-show="model.czfs === 'PACK静置'" labelPosition="left" :model="modelOld" labelWidth="100">

					<u-form-item label="托盘号:" borderBottom labelWidth="100">
						<view class="w100x flex right"> {{ modelOld.durableName }} </view>
					</u-form-item>
					<u-form-item label="托盘类型:" borderBottom labelWidth="100">
						<view class="w100x flex right"> PACK托盘 </view>
					</u-form-item>
					<u-form-item label="托盘容量:" borderBottom labelWidth="120">
						<view class="w100x flex right"> 目前{{ modelOld.lotQuantity }}/最大{{ modelOld.capacity }} </view>
					</u-form-item>

					<u-form-item label="托盘状态:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.durableState }}</view>
					</u-form-item>
					<u-form-item label="托盘位置:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.stockLocationCode }}</view>
					</u-form-item>

					<u-form-item label="产品工单:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.productOrderName }}</view>
					</u-form-item>
					<u-form-item label="产品描述:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.productSpecDesc }}</view>
					</u-form-item>
					<u-form-item label="产品编码:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.productSpecName }}</view>
					</u-form-item>


					<u-form-item label="静置开始时间:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.startTime ?
							moment(modelOld.startTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
					</u-form-item>
					<u-form-item label="静置预计结束时间:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.endTime ?
							moment(modelOld.endTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</view>
					</u-form-item>
					<u-form-item label="操作人:" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.eventUser }}</view>
					</u-form-item>
					<u-form-item label="静置要求时长(Min):" borderBottom labelWidth="120">
						<view class="w100x flex right"> {{ modelOld.standMinutes }}</view>
					</u-form-item>
				</u--form>
			</view>
			<view class="btnContainer" @click="SubmitEvent">{{ btnText }}</view>
		</view>

	</view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import moment from 'moment';
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
	mixins: [useNls, ScrollMixin, PrintPackageMixin],
	components: {
		NoData,
	},
	data() {
		this.changeStockLocationCode = this.$debounce(this.changeStockLocationCode, 1000)
		this.changeDurableName = this.$debounce(this.changeDurableName, 1000)
		return {
			pageParams: {},
			btnText: '下架',
			isInFocus: true,
			pageTitle: '',
			globalMap: getApp().globalData.globalMap, // 获取全局数据
			nlsMap: {
			},
			model: {},
			modelOld: {},
			modelOld: {},
			list: [], // 
			radiolist1: [
				{
					name: '电芯静置'
				},
				{
					name: 'PACK静置'
				}
			],
		}
	},
	computed: {},
	watch: {
		'model.durableName': {
			handler(val) {
				this.changeDurableName(val)
			}
		},
		'model.stockLocationCode': {
			handler(val) {
				this.changeStockLocationCode(val)
			}
		},
		'model.czfs': {
			handler(val) {
				this.initModel()
			}
		},
	},
	async onLoad(options) {
		let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
		this.pageParams = pageParams
		this.pageTitle = pageParams.pageTitle // 标题
		await this.initNls(pageParams, this.nlsMap)
		this.initModel()
		this.initModel2()
	},
	methods: {
		moment,
		initModel() {

			this.modelOld = {
				durableName: '',         // 托盘号
				maxQuantity: '',          // 托盘容量
				durableState: '',        // 托盘状态
				durableLocation: '',     // 托盘位置
				productOrderName: '',           // 产品工单
				productSpecDesc: '',         // 产品描述
				productSpecName: '',         // 产品编码
				startTime: '',           // 静置开始时间
				endTime: '',             // 静置预计结束时间
				eventUser: '',           // 操作人
				standMinutes: 0,         // 静置要求时长
				stockLocationCode: '',
				capacity: '',
				lotQuantity: '',

			};
		},
		initModel2() {
			this.model = {
				czfs: "PACK静置",
				durableName: '',
				stockLocationCode: '',
			};
		},
		async changeStockLocationCode(value) {
			if (!value) {
				return
			}
			this.modelOld.stockLocationCode = value
		},
		async changeDurableName(value) {
			if (!value) {
				return
			}
			this.modelOld.durableName = value
			let eventName = 'BOX';
			if (this.model.czfs === 'PACK静置') {
				eventName = 'PACK'
			}
			let params = {
				durableName: value,
				eventName: eventName,
				eventUser: this.$getLocal(USER_ID),
			};
			let res = await this.$service.ThreeCodeToOne.getDurableStanding(params);

			if (res.success) {
				if (res.data.stockLocationCode != null) {
					const item = res.data;
					this.modelOld.durableName = item.durableName || '';
					this.modelOld.maxQuantity = item.maxQuantity || 0;
					this.modelOld.durableLocation = item.durableLocation || '';
					this.modelOld.productOrderName = item.productOrderName || '';
					this.modelOld.productSpecDesc = item.productSpecDesc || '';
					this.modelOld.productSpecName = item.productSpecName || '';
					this.modelOld.startTime = item.startTime || '';
					this.modelOld.endTime = item.endTime || '';
					this.modelOld.eventUser = item.eventUser || '';
					this.modelOld.standMinutes = item.standMinutes || 0;
					this.modelOld.lotQuantity = item.lotQuantity || 0;
					this.modelOld.capacity = item.capacity || 0;
					this.modelOld.stockLocationCode = item.stockLocationCode || '';

					// 判断 endTime 与当前时间的关系，设置状态
					const now = new Date();
					const endTime = item.endTime ? new Date(item.endTime) : null;

					if (endTime && endTime > now) {
						this.modelOld.durableState = '静置中';
					} else {
						this.modelOld.durableState = '静置结束';
					}
					this.btnText = '下架'

				} else {
					const item = res.data;
					this.modelOld.durableLocation = item.durableLocation || '';
					this.modelOld.productOrderName = item.productOrderName || '';
					this.modelOld.productSpecDesc = item.productSpecDesc || '';
					this.modelOld.productSpecName = item.productSpecName || '';
					this.modelOld.startTime = item.startTime || '';
					this.modelOld.endTime = item.endTime || '';
					this.modelOld.eventUser = item.eventUser || '';
					this.modelOld.standMinutes = item.standMinutes || 240;
					this.modelOld.lotQuantity = item.lotQuantity || 0;
					this.modelOld.capacity = item.capacity || 0;
					this.btnText = '上架'
					this.modelOld.durableState = '闲置状态';
				}

			}
			this.model.durableName = ''

		},
		async SubmitEvent() {

			uni.showModal({
				title: '提示',
				content: '是否进行' + this.btnText + '?',
				cancelText: '取消',
				confirmText: '确认',
				cancelColor: '#666',
				confirmColor: '#409eff',
				success: async (res) => {
					if (res.confirm) {
						try {
							let eventName = 'BOX';
							if (this.model.czfs === 'PACK静置') {
								eventName = 'PACK'
							}
							if (this.btnText === '上架') {
								if (this.modelOld.stockLocationCode === null || this.modelOld.stockLocationCode === "") {
									return this.$Toast('请扫描库位!');
								}
								let params = {
									durableName: this.modelOld.durableName,
									eventName: eventName,
									standMinutes: this.modelOld.standMinutes,
									stockLocationCode: this.modelOld.stockLocationCode,
									eventUser: this.$getLocal(USER_ID),
								};
								let res = await this.$service.ThreeCodeToOne.packing(params);
								if (res.success) {
									this.$Toast(this.btnText + '成功');
									this.initModel()
									this.model.durableName = ''
									this.model.stockLocationCode = ''
								} else {
									uni.showModal({
										title: '提示',
										content: error.msg,
										cancelText: '确认',
										confirmText: '我知道了',
										cancelColor: '#666',
										confirmColor: '#409eff',
										success: (res) => {
											if (res.confirm) {

											}
											if (res.cancel) { }
										},
									})
								}
							} else {
								if (this.modelOld.durableState != '静置结束') {
									return this.$Toast('静置未结束不能执行下架操作');
								}
								let params = {
									durableName: this.modelOld.durableName,
									eventName: eventName,
									eventUser: this.$getLocal(USER_ID),
								};
								let res = await this.$service.ThreeCodeToOne.unPacking(params);
								if (res.success) {
									this.$Toast(this.btnText + '成功');
									this.initModel()
									this.model.durableName = ''
									this.model.stockLocationCode = ''
								} else {
									uni.showModal({
										title: '提示',
										content: error.msg,
										cancelText: '确认',
										confirmText: '我知道了',
										cancelColor: '#666',
										confirmColor: '#409eff',
										success: (res) => {
											if (res.confirm) {

											}
											if (res.cancel) { }
										},
									})
								}
							}

						} catch (error) {
							console.error('接口调用异常', error);
						}
					}
					if (res.cancel) { }
				},
			})

		},
		scan(key) {
			// #ifdef H5
			switch (key) {
				case 'carrierName':
					this.model.carrierName = 'LA202312010002' // 
					break;
				default:
					break;
			}
			// #endif
			//#ifdef APP-PLUS
			uni.scanCode({
				success: (res) => {
					this.$set(this.model, key, res.result)
				},
			})
			// #endif
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';

/* 右下角圆形按钮样式 */
.info-btn {
	position: fixed;
	bottom: 20px;
	right: 20px;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: #00aaff;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
	font-size: 20px;
	cursor: pointer;
}

/* 鼠标悬浮效果 */
.info-btn:hover {
	background-color: #0088cc;
}
</style>
