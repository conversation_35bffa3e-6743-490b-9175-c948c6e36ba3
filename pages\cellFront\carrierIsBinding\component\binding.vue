<template>
  <view class="listPage pl5 pr5 pb10">
    <view class="topContainer">
      <u--form labelPosition="left" :model="formModel" labelWidth="100">
        <u-form-item label="载具编码" borderBottom required labelWidth="100">
          <u--input v-model="formModel.trayName" border="none" focus placeholder="请输入或扫描载具编码"></u--input>
          <view class="iconfont icon-saoma" @click="scan('trayName')"></view>
        </u-form-item>
        <u-form-item label="产品条码" borderBottom required labelWidth="100">
          <u--input v-model="formModel.cellId24" border="none" placeholder="请输入或扫描产品条码" :focus="focus_cellId24"></u--input>
          <view class="iconfont icon-saoma" @click="scan('cellId24')"></view>
        </u-form-item>
        <u-form-item label="已装" labelWidth="100">
          <view class="w100x flex right" v-if="model.length > 0">
            {{ model.length }}
          </view>
        </u-form-item>
      </u--form>
    </view>
    <view class="listContainer">
      <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
        <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w100">位置</view>
        <view class="h35 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">产品条码</view>
      </view>
      <view class="table_content">
        <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" @scrolltolower="lower">
          <view v-for="(item, index) in model" :key="index" class="flex bl_e1e1e1 h50">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w100">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1">{{ item.cellId24 }}</view>
          </view>
          <view class="pt100" v-if="!model || model.length === 0">
            <u-empty mode="data"></u-empty>
          </view>
          <u-loadmore v-else fontSize="14" loading-text="努力加载中..." margin-top="20" margin-bottom="40" :status="status" />
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
import { USER_ID } from '@/utils/common/evtName.js'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'binding',
  mixins: [ScrollMixin],
  data() {
    this.changeTrayName = this.$debounce(this.changeTrayName, 1000)
    this.changeCellId24 = this.$debounce(this.changeCellId24, 1000)
    return {
      formModel: {
        trayName: null, // 载具
        cellId24: null, //产品条码
      },
      focus_cellId24: false,
    };
  },
  watch: {
    'formModel.trayName': {
      handler(val) {
        this.focus_cellId24 = false
        this.changeTrayName(val)
      }
    },
    'formModel.cellId24': {
      handler(val) {
        this.changeCellId24(val)
      }
    },
  },
  created() {
    this.initSearchModel()
  },

  methods: {
    changeTrayName(value) {
      if (!value) {
        return this.model = []
      }
      this.formModel.trayName = value
      this.formModel.cellId24 = null
      this.getData(true, true)
    },
    async changeCellId24(value) {
      if (!value) return
      let params = {
        cellId24: value,
        operateNo: '10001',
        trayName: this.formModel.trayName,
      }
      this.$service.carrierIsBind.queryScanProduct(params).then(res => {
        let cellId24 = res.data.cellId24
        let unbind = {
          operateNo: '10001',
          cellId24List: [].concat(cellId24),
          trayName: this.formModel.trayName,
          userId: this.$getLocal(USER_ID)
        }
        this.$service.carrierIsBind.operateTrayProduct(unbind).then(res => {
          // this.model.push({ cellId24 })
          this.$Toast('绑定成功')
          this.formModel.cellId24 = null
          this.getData(true, true)
        })
        // uni.showModal({
        //   title: '提示',
        //   content: `是否确认绑定产品条码为[${cellId24}]的载具？`,
        //   cancelText: '取消',
        //   confirmText: '确认',
        //   cancelColor: '#666',
        //   confirmColor: '#409eff',
        //   success: (res) => {
        //     if (res.confirm) {
        //       let unbind = {
        //         operateNo: '10001',
        //         cellId24List: [].concat(cellId24),
        //         trayName: this.formModel.trayName,
        //         userId: this.$getLocal(USER_ID)
        //       }
        //       this.$service.carrierIsBind.operateTrayProduct(unbind).then(res => {
        //         this.model.push({ cellId24 })
        //         this.$Toast('绑定成功')
        //         this.formModel.cellId24 = null
        //       })
        //     }
        //     if (res.cancel) { }
        //   },
        // })
      }).catch(e=>{
        this.formModel.cellId24 = null
      })
    },
    initSearchModel() {
      this.searchModel = {
        operateNo: '10001',
        trayName: null,
        pageNo: this.pageNumber,
        limit: 99999,
      }
    },
    async getData(clearOldData = false, refresh = false) {
      clearOldData && (this.pageNumber = 1)
      refresh && (this.model = [])
      this.searchModel.pageNo = this.pageNumber
      this.searchModel.limit = this.pageSize
      let params = JSON.parse(JSON.stringify(this.searchModel))
      params.trayName = this.formModel.trayName
      await this.$service.carrierIsBind.queryPageList(params).then((res) => {
        let records = res.data && res.data.trayProductPage && res.data.trayProductPage.records || []
        this.model = clearOldData ? records : [...this.model, ...records]

        if (this.searchModel.pageNo === 1) {
          this.focus_cellId24 = true
        }
        if (this.searchModel.pageNo == res.data.trayProductPage.pages) {
          this.status = 'nomore'
        } else {
          this.status = 'loadmore'
        }
        this.refresherTriggered = false
      }).catch((e) => {
        this.refresherTriggered = false
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'trayName':
          this.formModel.trayName = 'tuopan002'
          break;
        case 'cellId24':
          this.formModel.cellId24 = '666CZ00W37812GC8S1370066'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.formModel, key, res.result)
        },
      })
      // #endif
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/uform.scss';
@import '@/styles/publicStyle.scss';

.page {
  background-color: #f3f3f7;
}

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: 100%;
  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-top: 20rpx;
    .table_header {
      flex-shrink: 0;
    }
    .table_content {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>