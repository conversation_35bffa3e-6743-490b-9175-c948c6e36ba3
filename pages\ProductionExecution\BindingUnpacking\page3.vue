<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <view class="myContainer ma5">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="载具类型" required borderBottom labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('durableType')">
            <view v-if="model.durableType">{{ $utils.filterObjLabel(dicts.durableTypeList, model.durableType) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'durableType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>
        <u-form-item label="载具编码" borderBottom required labelWidth="100">
          <template v-if="model.durableType">
            <u--input v-model="model.durableName" border="none" placeholder="请扫描"></u--input>
            <view class="iconfont icon-saoma" @click="scan('durableName')"></view>
          </template>
        </u-form-item>

        <u-form-item label="载具名称" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.durableText }}
          </view>
        </u-form-item>

        <u-form-item label="满载数量" borderBottom labelWidth="100">
          <view class="w100x flex right">
            {{ model.capacity }}
          </view>
        </u-form-item>

        <!-- <u-form-item label="条码号" borderBottom required labelWidth="100">
          <u--input v-model="model.container" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('container')"></view>
        </u-form-item> -->

        <u-form-item label="已绑数量" labelWidth="100">
          <view class="w100x flex right"> {{ model.lotQuantity }} </view>
          <u-icon class="ml2" @click="gotoQuery" name="info-circle-fill" color="#2979ff" size="28"></u-icon>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import _ from "lodash";
export default {
  data() {
    this.changedurableName = this.$debounce(this.changedurableName, 1000)
    return {
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      rulesTip: {
        durableType: '载具类型不能为空',
        durableName: '载具编码不能为空',
      },
      model: {
        durableType: '', //载具类型
        durableName: '',// 载具编码
        durableText: '', //  载具名称
        capacity: '', //满载数量
        lotQuantity: '', // 已绑数量
      },

      columns: [],
      select: false,
      selectType: '',
      dicts: {
        durableTypeList: [], // 载具类型
      },

    }
  },
  computed: {},
  watch: {
    'model.durableName': {
      handler(val) {
        this.changedurableName(val)
      }
    },
  },
  async onLoad(options) {
    let nlsMap = JSON.parse(decodeURIComponent(options.nlsMap))
    this.pageTitle = nlsMap.page3Title // 标题
    this.nlsMap = nlsMap

    this.initModel()
    this.getCarrierTypeList()
  },
  methods: {
    async changedurableName(value) {
      this.model.durableText = ''
      this.model.capacity = ''
      this.model.lotQuantity = ''
      if (!value) return
      let params = {
        durableType: this.model.durableType,
        durableName: value,
      }
      try {
        let res = await this.$service.BindingUnpacking.getDurableInfo(params)
        if (res.datas.length > 0) {
          this.model.durableText = res.datas[0].durableText
          this.model.capacity = res.datas[0].capacity
          this.model.lotQuantity = res.datas[0].lotQuantity
        } else {
          this.$Toast(`该载具类型不存在[${this.model.durableName}]载具编码`)
          this.model.durableName = null
        }
      } catch (error) {
        // this.model.durableName = null
      }
    },
    getCarrierTypeList() {
      this.$service.common.getDictByQueryId('Common_GetCarrierType').then(res => {
        this.dicts.durableTypeList = res.datas.map(item => ({
          label: item.text,
          value: item.value,
        }))
      })
    },


    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'durableType':
          this.columns = this.dicts.durableTypeList
          break;
        default:
          break;
      }
    },

    gotoQuery() {
      if (!this.model.durableName) {
        return this.$Toast('载具编码不能为空！')
      }
      uni.navigateTo({
        url: `/pages/ProductionExecution/BindingUnpacking/detail?durableName=${this.model.durableName}&nlsMap=${encodeURIComponent(JSON.stringify(this.nlsMap))}`,
        events: {
        }
      })
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
      if (this.selectType == 'durableType') {
        this.model.durableName = ''
      }
    },
    GetAreaNameList() {
      this.$service.common.getDictByQueryId('MachineTask_GetAreaName').then(res => {
        this.dicts.areaNameList = res.datas.map(item => ({
          label: item.areaText,
          value: item.areaName,
        }))
      })
    },

    initModel() {
      this.model = {
        durableType: '', //载具类型
        durableName: '',// 载具编码
        durableText: '', //  载具名称
        capacity: '', //满载数量
        lotQuantity: '', // 已绑数量
      }
    },
    async unbind() {
      let params = {
        containerType: this.model.durableType,
        container: this.model.durableName,
        flag: '3',
      }
      try {
        let res = await this.$service.BindingUnpacking.bindOrUnBind(params)
        this.$Toast('操作成功')
        this.model.durableName = ''
        this.model.durableText = ''
        this.model.capacity = ''
        this.model.lotQuantity = ''
      } catch (error) {
      }
    },
    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      if (this.model.lotQuantity == 0) {
        return this.$Toast('已解绑数量为空!')
      }
      uni.showModal({
        title: '提示',
        content: `是否全部解绑？`,
        cancelText: '取消',
        confirmText: '确认',/* 只可以4个字 */
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.unbind()
          }
          if (res.cancel) { }
        },
      })
    },

    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
