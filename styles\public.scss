page {
  --safe-area-inset-top: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;
}

@supports (top: constant(safe-area-inset-top)) {
  page {
    --safe-area-inset-top: constant(safe-area-inset-top);
    --safe-area-inset-right: constant(safe-area-inset-right);
    --safe-area-inset-bottom: constant(safe-area-inset-bottom);
    --safe-area-inset-left: constant(safe-area-inset-left);
  }

}

@supports (top: env(safe-area-inset-top)) {
  page {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

view {
  word-break: break-all;
  line-height: 40rpx;
}


.pageContainer {
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
}

.listPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
}

/* 页面结构 */
.myContainerPage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  .myContainer {
    flex: 1;
    overflow-y: scroll;
  }
}
/* 底部按钮 */
.btnContainer {
  width: calc(100vw - 20rpx);
  margin: 0 auto 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #409eff;
  font-weight: 600;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  border-radius: 22rpx;
}

.btnContainer2 {
  @extend .flex;
  @extend .between;
  width: calc(100vw - 20rpx); 
  margin: 0 auto;
  >view {
    width: 48%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 30rpx;
    text-align: center;
    border-radius: 22rpx;
  }
}

::v-deep .u-button--success {
  background: #409eff !important;
}

a {
  text-decoration: none;
}

a:hover {
  color: #409EFF;
}

.break-spaces {
  white-space: break-spaces;
}

.nowrap {
  white-space: nowrap;
}

.hid-one {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hid-two {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

// 文字隐藏 一起使用 fontmore fontrow-1
.fontmore {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.fontrow-1 {
  -webkit-line-clamp: 1;
}

.fontrow-2 {
  -webkit-line-clamp: 2;
}

.fontrow-3 {
  -webkit-line-clamp: 3;
}

.shadow {
  box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1), 0 0 5px rgba(0, 0, 0, 0.02);
}


.br0 {
  border-radius: 0px;
}

.br4 {
  border-radius: 4px;
}

.br6 {
  border-radius: 6px;
}

.br8 {
  border-radius: 8px;
}

.br10 {
  border-radius: 10px;
}

.br12 {
  border-radius: 12px;
}

.br15 {
  border-radius: 30rpx;
}
.br20{
  border-radius: 40rpx;
}

.br50x {
  border-radius: 50%;
}

.w_auto {
  width: auto;
}

.w10x {
  width: 10%;
}

.w15x {
  width: 15%;
}

.w20x {
  width: 20%;
}

.w25x {
  width: 25%;
}

.w30x {
  width: 30%;
}

.w33x {
  width: 33%;
}

.w33_3x {
  width: 33.3%;
}

.w35x {
  width: 35%;
}

.w40x {
  width: 40%;
}

.w45x {
  width: 35%;
}

.w48x {
  width: 48%;
}

.w50x {
  width: 50%;
}

.w55x {
  width: 55%;
}

.w60x {
  width:60%;
}

.w65x {
  width:65%;
}

.w70x {
  width:70%;
}

.w75x {
  width:75%;
}

.w80x {
  width: 80%;
}

.w85x {
  width: 85%;
}

.w90x {
  width: 90%;
}

.w95x {
  width: 95%;
}

.w100x {
  width: 100%;
}

.w1 {
  width: 2rpx;
}

.w4 {
  width: 8rpx;
}

.w2{
  width: 4rpx;
}

.w7{
  width: 14rpx;
}

.w8{
  width: 16rpx;
}

.w9 {
  width: 18rpx;
}

.w10 {
  width: 20rpx;
}

.w12 {
  width: 24rpx;
}

.w13 {
  width: 26rpx;
}

.w14 {
  width: 28rpx;
}

.w15 {
  width: 30rpx;
}

.w16 {
  width: 32rpx;
}

.w18 {
  width: 36rpx;
}

.w20 {
  width: 40rpx;
}

.w22 {
  width: 44rpx;
}

.w24 {
  width: 48rpx;
}

.w25 {
  width: 50rpx;
}

.w30 {
  width: 60rpx;
}

.w32 {
  width: 64rpx;
}

.w35 {
  width: 70rpx;
}

.w37 {
  width: 74rpx;
}

.w76r {
  width: 76rpx;
}

.w40 {
  width: 80rpx;
}

.w43 {
  width: 86rpx;
}

.w44 {
  width: 88rpx;
}

.w48 {
  width: 96rpx;
}

.w80r {
  width: 80rpx;
}

.w90r {
  width: 90rpx;
}

.w92r {
  width: 92rpx;
}

.w93r {
  width: 93rpx;
}

.w50 {
  width: 100rpx;
}

.w56 {
  width: 112rpx;
}

.w58 {
  width: 116rpx;
}

.w104r {
  width: 104rpx;
}

.w60 {
  width: 120rpx;
}

.w64 {
  width: 128rpx;
}

.w70 {
  width: 140rpx;
}

.w80 {
  width: 160rpx;
}

.w85 {
  width: 170rpx;
}

.w144r {
  width: 144rpx;
}

.w52 {
  width: 104rpx;
}

.w75 {
  width: 150rpx;
}

.w155r {
  width: 155rpx;
}

.w80 {
  width: 160rpx;
}

.w168r {
  width: 168rpx;
}

.w90 {
  width: 180rpx;
}

.w95 {
  width: 190rpx;
}

.w198r {
  width: 198rpx;
}

.w100 {
  width: 200rpx;
}

.w104 {
  width: 208rpx;
}

.w105 {
  width: 210rpx;
}

.w133 {
  width: 266rpx;
}

.w110 {
  width: 220rpx;
}

.w115 {
  width: 230rpx;
}

.w120 {
  width: 240rpx;
}

.w125 {
  width: 250rpx;
}

.w130 {
  width: 260rpx;
}

.w135 {
  width: 270rpx;
}

.w140 {
  width: 280rpx;
}

.w145 {
  width: 290rpx;
}

.w150 {
  width: 300rpx;
}

.w155 {
  width: 310rpx;
}

.w160 {
  width: 320rpx;
}

.w165 {
  width: 330rpx;
}

.w170 {
  width: 340rpx;
}

.w175 {
  width: 350rpx;
}

.w180 {
  width: 360rpx;
}

.w185 {
  width: 370rpx;
}

.w190 {
  width: 380rpx;
}

.w195 {
  width: 390rpx;
}

.w200 {
  width: 400rpx;
}

.w205 {
  width: 410rpx;
}

.w210 {
  width: 420rpx;
}

.w215 {
  width: 430rpx;
}

.w220 {
  width: 440rpx;
}

.w225 {
  width: 450rpx;
}

.w230 {
  width: 460rpx;
}

.w235 {
  width: 470rpx;
}

.w240 {
  width: 480rpx;
}

.w245 {
  width: 490rpx;
}

.w250 {
  width: 500rpx;
}

.w255 {
  width: 510rpx;
}

.w260 {
  width: 520rpx;
}

.w280 {
  width: 560rpx;
}

.w300 {
  width: 600rpx;
}

.w230r {
  width: 230rpx;
}

.w240r {
  width: 240rpx;
}

.w150 {
  width: 300rpx;
}

.w325r {
  width: 325rpx;
}

.w334r {
  width: 334rpx;
}

.w320 {
  width: 640rpx;
}

.w340 {
  width: 680rpx;
}

.w345 {
  width: 690rpx;
}

.w354r {
  width: 354rpx;
}

.w356r {
  width: 356rpx;
}

.w710r {
  width: 710rpx;
}

.w360 {
  width: 720rpx;
}

.w365 {
  width: 730rpx;
}

.w375 {
  width: 750rpx;
}

.w750r {
  width: 750rpx;
}

.maw_100x {
  max-width: 100%;
}

.maw_120 {
  max-width: 200rpx;
}

.maw_133 {
  max-width: 266rpx;
}

.mh100v {
  min-height: 100vh;
}

.mh40 {
  min-height: 80rpx;
}
.mah_100x {
  max-height: 100%;
}

.mah270v {
  max-height: 270px;
}

.mah190v {
  max-height: 190px;
}

.h_auto {
  height: auto;
}

.h100v {
  height: 100vh;
}

.h100x {
  height: 100%;
}

.h95x {
  height: 95%;
}

.h90x {
  height: 90%;
}

.h85x {
  height: 85%;
}

.h80x {
  height: 80%;
}

.h75x {
  height: 75%;
}

.h70x {
  height: 70%;
}

.h65x {
  height: 65%;
}

.h60x {
  height: 60%;
}

.h55x {
  height: 55%;
}

.h50x {
  height: 50%;
}

.h45x {
  height: 45%;
}

.h40x {
  height: 40%;
}

.h35x {
  height: 35%;
}

.h30x {
  height: 30%;
}

.h25x {
  height: 25%;
}

.h20x {
  height: 20%;
}

.h15x {
  height: 15%;
}

.h10x {
  height: 10%;
}

.h1 {
  height: 2rpx;
}

.h2 {
  height: 4rpx;
}

.h4 {
  height: 8rpx;
}

.h5 {
  height: 10rpx;
}

.h8 {
  height: 16rpx;
}


.h9 {
  height: 18rpx;
}

.h10 {
  height: 20rpx;
}

.h7 {
  height: 14rpx;
}

.h12{
  height: 24rpx;
}

.h13{
  height: 26rpx;
}

.h14 {
  height: 28rpx;
}

.h15 {
  height: 30rpx;
}

.h16 {
  height: 32rpx;
}

.h17 {
  height: 34rpx;
}

.h18 {
  height: 36rpx;
}

.h20 {
  height: 40rpx;
}

.h22 {
  height: 44rpx;
}

.h24 {
  height: 48rpx;
}

.h25 {
  height: 50rpx;
}

.h26 {
  height: 52rpx;
}

.h27 {
  height: 54rpx;
}

.h30 {
  height: 60rpx;
}

.h31 {
  height: 62rpx;
}

.h32 {
  height: 64rpx;
}

.h35 {
  height: 70rpx;
}

.h36 {
  height: 72rpx;
}

.h37 {
  height: 74rpx;
}

.h76r {
  height: 76rpx;
}

.h40 {
  height: 80rpx;
}

.h41 {
  height: 82rpx;
}

.h44 {
  height: 88rpx;
}

.h45 {
  height: 90rpx;
}

.h92r {
  height: 92rpx;
}

.h95r {
  height: 95rpx;
}

.h97r {
  height: 97rpx;
}

.h50 {
  height: 100rpx;
}

.h52 {
  height: 104rpx;
}

.h105r {
  height: 105rpx;
}

.h55 {
  height: 110rpx;
}

.h114r {
  height: 114rpx;
}

.h116r {
  height: 116rpx;
}

.h58 {
  height: 116rpx;
}

.h59 {
  height: 118rpx;
}

.h60 {
  height: 120rpx;
}

.h62 {
  height: 124rpx;
}

.h64 {
  height: 128rpx;
}

.h129r {
  height: 129rpx;
}

.h138r {
  height: 138rpx;
}

.h70 {
  height: 140rpx;
}

.h144r {
  height: 144rpx;
}

.h145r {
  height: 145rpx;
}

.h75 {
  height: 150rpx;
}

.h152r {
  height: 152rpx;
}

.h80 {
  height: 160rpx;
}

.h85 {
  height: 170rpx;
}

.h172r {
  height: 172rpx;
}

.h174r {
  height: 174rpx;
}

.h90 {
  height: 180rpx;
}

.h183r {
  height: 183rpx;
}

.h184r {
  height: 184rpx;
}

.h185 {
  height: 370rpx;
}

.h190 {
  height: 380rpx;
}

.h192r {
  height: 192rpx;
}

.h111 {
  height: 222rpx;
}

.hauto {
  height: auto;
}

.h100 {
  height: 200rpx;
}

.h110 {
  height: 220rpx;
}

.h139 {
  height: 278rpx;
}

.h218r {
  height: 218rpx;
}

.h225r {
  height: 225rpx;
}

.h227r {
  height: 227rpx;
}

.h230r {
  height: 230rpx;
}

.h232r {
  height: 232rpx;
}

.h120 {
  height: 240rpx;
}

.h288r {
  height: 288rpx;
}

.h260r {
  height: 260rpx;
}

.h296r {
  height: 296rpx;
}

.h140 {
  height: 280rpx;
}

.h150 {
  height: 300rpx;
}

.h152 {
  height: 304rpx;
}

.h306r {
  height: 306rpx;
}

.h332r {
  height: 332rpx;
}

.h170 {
  height: 340rpx;
}

.h175 {
  height: 350rpx;
}

.h380r {
  height: 380rpx;
}

.h200 {
  height: 400rpx;
}

.h220 {
  height: 440rpx;
}

.h432r {
  height: 432rpx;
}

.h452r {
  height: 452rpx;
}

.h452r {
  height: 452rpx;
}

.h278 {
  height: 556rpx;
}

.h300 {
  height: 600rpx;
}
.mato {
  margin: auto;
}

.ma0 {
  margin: 0;
}

.ma1 {
  margin: 2rpx;
}

.ma2 {
  margin: 4rpx;
}

.ma3 {
  margin: 6rpx;
}

.ma4 {
  margin: 8rpx;
}

.ma5 {
  margin: 10rpx;
}

.ma10 {
  margin: 20rpx;
}

.ma12 {
  margin: 24rpx;
}

.ma15 {
  margin: 30rpx;
}

.ma20 {
  margin: 40rpx;
}

.ma25 {
  margin: 50rpx;
}

.ma30 {
  margin: 60rpx;
}

.ma35 {
  margin: 70rpx;
}

.ma40 {
  margin: 80rpx;
}

.ma45 {
  margin: 90rpx;
}

.ma50 {
  margin: 100rpx;
}

.mt10v {
  margin-top: 10vh;
}

.mt70v {
  margin-top: 70vh;
}

.mt0 {
  margin-top: 0;
}

.mt1 {
  margin-top: 2rpx;
}

.mt2 {
  margin-top: 4rpx;
}

.mt3 {
  margin-top: 6rpx;
}

.mt4 {
  margin-top: 8rpx;
}

.mt5 {
  margin-top: 10rpx;
}

.mt6 {
  margin-top: 12rpx;
}

.mt7 {
  margin-top: 14rpx;
}

.mt8 {
  margin-top: 16rpx;
}

.mt9 {
  margin-top: 18rpx;
}

.mt10 {
  margin-top: 20rpx;
}

.mt21r {
  margin-top: 21rpx;
}

.mt11 {
  margin-top: 22rpx;
}

.mt12 {
  margin-top: 24rpx;
}

.mt13 {
  margin-top: 26rpx;
}

.mt14 {
  margin-top: 28rpx;
}

.mt29 {
  margin-top: 29px;
}

.mt15 {
  margin-top: 30rpx;
}

.mt20 {
  margin-top: 40rpx;
}

.mt22 {
  margin-top: 44rpx;
}

.mt47r {
  margin-top: 47rpx;
}

.mt25 {
  margin-top: 50rpx;
}

.mt57r {
  margin-top: 57rpx;
}

.mt30 {
  margin-top: 60rpx;
}

.mt35 {
  margin-top: 70rpx;
}

.mt40 {
  margin-top: 80rpx;
}

.mt45 {
  margin-top: 90rpx;
}

.mt50 {
  margin-top: 100rpx;
}

.mb0 {
  margin-bottom: 0;
}

.mb1 {
  margin-bottom: 2rpx;
}

.mb2 {
  margin-bottom: 4rpx;
}

.mb3 {
  margin-bottom: 6rpx;
}

.mb4 {
  margin-bottom: 8rpx;
}

.mb9r {
  margin-bottom: 9rpx;
}

.mb5 {
  margin-bottom: 10rpx;
}

.mb11r {
  margin-bottom: 11rpx;
}

.mb6 {
  margin-bottom: 12rpx;
}

.mb7 {
  margin-bottom: 14rpx;
}

.mb8 {
  margin-bottom: 16rpx;
}

.mb9 {
  margin-bottom: 18rpx;
}

.mb10 {
  margin-bottom: 20rpx;
}

.mb11 {
  margin-bottom: 22rpx;
}

.mb12 {
  margin-bottom: 24rpx;
}

.mb25r {
  margin-bottom: 25rpx;
}

.mb13 {
  margin-bottom: 26rpx;
}

.mb14 {
  margin-bottom: 28rpx;
}

.mb15 {
  margin-bottom: 30rpx;
}

.mb34r {
  margin-bottom: 34rpx;
}

.mb36r {
  margin-bottom: 36rpx;
}

.mb20 {
  margin-bottom: 40rpx;
}

.mb43r {
  margin-bottom: 43rpx;
}

.mb25 {
  margin-bottom: 50rpx;
}

.mb30 {
  margin-bottom: 60rpx;
}

.mb35 {
  margin-bottom: 70rpx;
}

.mb40 {
  margin-bottom: 80rpx;
}

.mb41 {
  margin-bottom: 82rpx;
}

.mb45 {
  margin-bottom: 90rpx;
}

.mb50 {
  margin-bottom: 100rpx;
}

.ml_auto {
  margin-left: auto;
}

.ml0 {
  margin-left: 0;
}

.ml1 {
  margin-left: 2rpx;
}

.ml2 {
  margin-left: 4rpx;
}

.ml3 {
  margin-left: 6rpx;
}

.ml4 {
  margin-left: 8rpx;
}

.ml5 {
  margin-left: 10rpx;
}

.ml6 {
  margin-left: 12rpx;
}

.ml7 {
  margin-left: 14rpx;
}

.ml15r {
  margin-left: 15rpx;
}

.ml8 {
  margin-left: 16rpx;
}

.ml9 {
  margin-left: 18rpx;
}

.ml10 {
  margin-left: 20rpx;
}

.ml10r {
  margin-left: 10rpx;
}

.ml12r {
  margin-left: 12rpx;
}

.ml20r {
  margin-left: 20rpx;
}

.ml21r {
  margin-left: 21rpx;
}

.ml11 {
  margin-left: 22rpx;
}

.ml23r {
  margin-left: 23rpx;
}

.ml12 {
  margin-left: 24rpx;
}

.ml13 {
  margin-left: 26rpx;
}

.ml14 {
  margin-left: 28rpx;
}

.ml15 {
  margin-left: 30rpx;
}

.ml18 {
  margin-left: 36rpx;
}

.ml20 {
  margin-left: 40rpx;
}

.ml22 {
  margin-left: 44rpx;
}

.ml23 {
  margin-left: 46rpx;
}

.ml26 {
  margin-left: 52rpx;
}


.ml25 {
  margin-left: 50rpx;
}

.ml30 {
  margin-left: 60rpx;
}

.ml35 {
  margin-left: 70rpx;
}

.ml40 {
  margin-left: 80rpx;
}

.ml45 {
  margin-left: 90rpx;
}

.ml50 {
  margin-left: 100rpx;
}

.ml60 {
  margin-left: 120rpx;
}

.mr_auto {
  margin-right: auto;
}

.mr0 {
  margin-right: 0;
}

.mr1 {
  margin-right: 2rpx;
}

.mr2 {
  margin-right: 4rpx;
}

.mr3 {
  margin-right: 6rpx;
}

.mr4 {
  margin-right: 8rpx;
}

.mr5 {
  margin-right: 10rpx;
}

.mr6 {
  margin-right: 12rpx;
}

.mr7 {
  margin-right: 14rpx;
}

.mr8 {
  margin-right: 16rpx;
}

.mr9 {
  margin-right: 18rpx;
}

.mr10 {
  margin-right: 20rpx;
}

.mr10r {
  margin-right: 10rpx;
}

.mr12r {
  margin-right: 12rpx;
}

.mr20r {
  margin-right: 20rpx;
}

.mr21rpx {
  margin-right: 21rpx;
}

.mr11 {
  margin-right: 22rpx;
}

.mr12 {
  margin-right: 24rpx;
}

.mr13 {
  margin-right: 26rpx;
}

.mr14 {
  margin-right: 28rpx;
}

.mr15 {
  margin-right: 30rpx;
}

.mr16 {
  margin-right: 32rpx;
}

.mr18 {
  margin-right: 36rpx;
}

.mr20 {
  margin-right: 40rpx;
}

.mr22 {
  margin-right: 44rpx;
}

.mr42r {
  margin-right: 42rpx;
}

.mr25 {
  margin-right: 50rpx;
}

.mr30 {
  margin-right: 60rpx;
}

.mr33 {
  margin-right: 66rpx;
}

.mr35 {
  margin-right: 70rpx;
}

.mr36 {
  margin-right: 72rpx;
}

.mr40 {
  margin-right: 80rpx;
}

.mr45 {
  margin-right: 90rpx;
}

.mr50 {
  margin-right: 100rpx;
}

.mr60 {
  margin-right: 120rpx;
}

.pa0 {
  padding: 0 !important;
}

.pa1 {
  padding: 2rpx;
}

.pa2 {
  padding: 4rpx;
}

.pa3 {
  padding: 6rpx;
}

.pa4 {
  padding: 8rpx;
}

.pa5 {
  padding: 10rpx;
}

.pa8 {
  padding: 16rpx;
}

.pa10 {
  padding: 20rpx;
}

.pa12 {
  padding: 24rpx;
}

.pa13 {
  padding: 26rpx;
}

.pa15 {
  padding: 30rpx;
}

.pa20 {
  padding: 40rpx;
}

.pa25 {
  padding: 50rpx;
}

.pa30 {
  padding: 60rpx;
}

.pa35 {
  padding: 70rpx;
}

.pa40 {
  padding: 80rpx;
}

.pa45 {
  padding: 90rpx;
}

.pa50 {
  padding: 100rpx;
}

.pt0 {
  padding-top: 0;
}

.pt1 {
  padding-top: 2rpx;
}

.pt2 {
  padding-top: 4rpx;
}

.pt5r {
  padding-top: 5rpx;
}

.pt3 {
  padding-top: 6rpx;
}

.pt4 {
  padding-top: 8rpx;
}

.pt5 {
  padding-top: 10rpx;
}

.pt6 {
  padding-top: 12rpx;
}

.pt7 {
  padding-top: 14rpx;
}

.pt15r {
  padding-top: 15rpx;
}

.pt8 {
  padding-top: 16rpx;
}

.pt9 {
  padding-top: 18rpx;
}

.pt10 {
  padding-top: 20rpx;
}

.pt11 {
  padding-top: 22rpx;
}

.pt12 {
  padding-top: 24rpx;
}

.pt25r {
  padding-top: 25rpx;
}


.pt13 {
  padding-top: 26rpx;
}

.pt14 {
  padding-top: 28rpx;
}

.pt15 {
  padding-top: 30rpx;
}

.pt16 {
  padding-top: 32rpx;
}

.pt33r {
  padding-top: 33rpx;
}

.pt17 {
  padding-top: 34rpx;
}

.pt20 {
  padding-top: 40rpx;
}

.pt21 {
  padding-top: 42rpx;
}

.pt23 {
  padding-top: 46rpx;
}


.pt25 {
  padding-top: 50rpx;
}

.pt20r {
  padding-top: 20rpx;
}

.pt40r {
  padding-top: 40rpx;
}

.pt57r {
  padding-top: 57rpx;
}

.pt30 {
  padding-top: 60rpx;
}

.pt35 {
  padding-top: 70rpx;
}

.pt40 {
  padding-top: 80rpx;
}

.pt45 {
  padding-top: 90rpx;
}

.pt50 {
  padding-top: 100rpx;
}

.pt55 {
  padding-top: 110rpx;
}

.pt60 {
  padding-top: 120rpx;
}

.pt80 {
  padding-top: 160rpx;
}

.pt90 {
  padding-top: 180rpx;
}

.pt100 {
  padding-top: 200rpx;
}

.pb0 {
  padding-bottom: 0;
}

.pb1 {
  padding-bottom: 2rpx;
}

.pb2 {
  padding-bottom: 4rpx;
}

.pb3 {
  padding-bottom: 6rpx;
}

.pb4 {
  padding-bottom: 8rpx;
}

.pb5 {
  padding-bottom: 10rpx;
}

.pb6 {
  padding-bottom: 12rpx;
}

.pb7 {
  padding-bottom: 14rpx;
}

.pb8 {
  padding-bottom: 16rpx;
}

.pb9 {
  padding-bottom: 18rpx;
}

.pb10 {
  padding-bottom: 20rpx;
}

.pb11 {
  padding-bottom: 22rpx;
}

.pb12 {
  padding-bottom: 24rpx;
}

.pb25r {
  padding-bottom: 25rpx;
}

.pb13 {
  padding-bottom: 26rpx;
}

.pb14 {
  padding-bottom: 28rpx;
}

.pb15 {
  padding-bottom: 30rpx;
}

.pb16 {
  padding-bottom: 32rpx;
}

.pb33r {
  padding-bottom: 33rpx;
}

.pb17 {
  padding-bottom: 34rpx;
}

.pb18 {
  padding-bottom: 36rpx;
}

.pb20 {
  padding-bottom: 40rpx;
}

.pb20r {
  padding-bottom: 20rpx;
}

.pb40r {
  padding-bottom: 40rpx;
}

.pb22 {
  padding-bottom: 44rpx;
}

.pb23 {
  padding-bottom: 46rpx;
}

.pb25 {
  padding-bottom: 50rpx;
}

.pb30 {
  padding-bottom: 60rpx;
}

.pb35 {
  padding-bottom: 70rpx;
}

.pb40 {
  padding-bottom: 80rpx;
}

.pb45 {
  padding-bottom: 90rpx;
}

.pb50 {
  padding-bottom: 100rpx;
}

.pb60 {
  padding-bottom: 120rpx;
}

.pl0 {
  padding-left: 0;
}

.pl1 {
  padding-left: 2rpx;
}

.pl2 {
  padding-left: 4rpx;
}

.pl3 {
  padding-left: 6rpx;
}

.pl4 {
  padding-left: 8rpx;
}

.pl5 {
  padding-left: 10rpx;
}

.pl6 {
  padding-left: 12rpx;
}

.pl7 {
  padding-left: 14rpx;
}

.pl8 {
  padding-left: 16rpx;
}

.pl17r {
  padding-left: 17rpx;
}

.pl9 {
  padding-left: 18rpx;
}

.pl10 {
  padding-left: 20rpx;
}

.pl21r {
  padding-left: 21rpx;
}

.pl11 {
  padding-left: 22rpx;
}

.pl12 {
  padding-left: 24rpx;
}

.pl13 {
  padding-left: 26rpx;
}

.pl14 {
  padding-left: 28rpx;
}

.pl15 {
  padding-left: 30rpx;
}

.pl16 {
  padding-left: 32rpx;
}

.pl18 {
  padding-left: 36rpx;
}


.pl20 {
  padding-left: 40rpx;
}

.pl20r {
  padding-left: 20rpx;
}

.pl40r {
  padding-left: 40rpx;
}

.pl43r {
  padding-left: 43rpx;
}

.pl22 {
  padding-left: 44rpx;
}

.pl24 {
  padding-left: 48rpx;
}

.pl25 {
  padding-left: 50rpx;
}

.pl30 {
  padding-left: 60rpx;
}

.pl32 {
  padding-left: 64rpx;
}

.pl35 {
  padding-left: 70rpx;
}

.pl38 {
  padding-left: 76rpx;
}

.pl40 {
  padding-left: 80rpx;
}

.pl45 {
  padding-left: 90rpx;
}

.pl95r {
  padding-left: 95rpx;
}

.pl50 {
  padding-left: 100rpx;
}

.pl115r {
  padding-left: 115rpx;
}

.pl60 {
  padding-left: 120rpx;
}

.pr0 {
  padding-right: 0;
}

.pr1 {
  padding-right: 2rpx;
}

.pr2 {
  padding-right: 4rpx;
}

.pr3 {
  padding-right: 6rpx;
}

.pr4 {
  padding-right: 8rpx;
}

.pr5 {
  padding-right: 10rpx;
}

.pr6 {
  padding-right: 12rpx;
}

.pr7 {
  padding-right: 14rpx;
}

.pr8 {
  padding-right: 16rpx;
}

.pr17r {
  padding-right: 17rpx;
}

.pr9 {
  padding-right: 18rpx;
}

.pr10 {
  padding-right: 20rpx;
}

.pr21r {
  padding-right: 21rpx;
}

.pr11 {
  padding-right: 22rpx;
}

.pr12 {
  padding-right: 24rpx;
}

.pr13 {
  padding-right: 26rpx;
}

.pr14 {
  padding-right: 28rpx;
}

.pr15 {
  padding-right: 30rpx;
}

.pr16 {
  padding-right: 32rpx;
}

.pr19 {
  padding-right: 38rpx;
}

.pr20 {
  padding-right: 40rpx;
}

.pr20r {
  padding-right: 20rpx;
}

.pr40r {
  padding-right: 40rpx;
}

.pr22 {
  padding-right: 44rpx;
}

.pr25 {
  padding-right: 50rpx;
}

.pr30 {
  padding-right: 60rpx;
}

.pr32 {
  padding-right: 64rpx;
}

.pr35 {
  padding-right: 70rpx;
}

.pr38 {
  padding-right: 76rpx;
}

.pr40 {
  padding-right: 80rpx;
}

.pr45 {
  padding-right: 90rpx;
}


.pr48 {
  padding-right: 96rpx;
}

.pr113r {
  padding-right: 113rpx;
}

.pr50 {
  padding-right: 100rpx;
}

.pr60 {
  padding-right: 120rpx;
}

.pr125r {
  padding-right: 125rpx;
}

.pr100 {
  padding-right: 200rpx;
}

.pr312r {
  padding-right: 312rpx;
}

.pr231r {
  padding-right: 231rpx;
}

.pr200 {
  padding-right: 400rpx;
}

.fs10 {
  font-size: 20rpx;
}

.fs11 {
  font-size: 22rpx !important;
}

.fs12 {
  font-size: 24rpx;
}

.fs25r {
  font-size: 25rpx!important;
}

.fs13 {
  font-size: 26rpx !important;
}

.fs14 {
  font-size: 28rpx !important;
}

.fs29r {
  font-size: 29rpx !important;
}

.fs15 {
  font-size: 30rpx !important;
}

.fs16 {
  font-size: 32rpx !important;
}

.fs17 {
  font-size: 34rpx !important;
}

.fs18 {
  font-size: 36rpx !important;
}

.fs19 {
  font-size: 38rpx !important;
}

.fs20 {
  font-size: 40rpx;
}

.fs22 {
  font-size: 44rpx;
}

.fs24 {
  font-size: 48rpx;
}

.fs25 {
  font-size: 50rpx!important;
}

.fs26 {
  font-size: 52rpx;
}

.fs28 {
  font-size: 56rpx;
}

.fs30 {
  font-size: 60rpx !important;
}

.fb {
  font-weight: bold;
}

.fw_500 {
  font-weight: 500;
}

.fw_600 {
  font-weight: 600;
}

.txt_c {
  text-align: center;
}

.txt_l {
  text-align: left;
}

.txt_r {
  text-align: right;
}

.txt_j {
  text-align: justify;
}

.vat {
  vertical-align: top;
}

.vam {
  vertical-align: middle;
}

.vab {
  vertical-align: bottom;
}

.pr {
  position: relative;
}

.pa {
  position: absolute;
}

.pf {
  position: fixed;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.t0 {
  top: 0;
}

.b0 {
  bottom: 0;
}

.l0 {
  left: 0
}

.l35 {
  left: 35px;
}

.r0 {
  right: 0;
}

.r8 {
  right: 16rpx;
}

.r10 {
  right: 20rpx;
}

.r15 {
  right: 15px;
}

.db {
  display: block;
}

.dib {
  display: inline-block;
}

.dn {
  display: none !important;
}

.lin1 {
  line-height: 2rpx;
}

.lin2 {
  line-height: 4rpx;
}

.lin10 {
  line-height: 20rpx;
}

.lin15 {
  line-height: 30rpx;
}

.lin17 {
  line-height: 34rpx;
}

.lin18 {
  line-height: 36rpx;
}

.lin19 {
  line-height: 38rpx;
}

.lin20 {
  line-height: 40rpx;
}

.lin24 {
  line-height: 48rpx;
}

.lin25 {
  line-height: 50rpx;
}

.lin27 {
  line-height: 54rpx;
}

.lin30 {
  line-height: 60rpx;
}

.lin32 {
  line-height: 64rpx;
}

.lin35 {
  line-height: 70rpx;
}

.lin36 {
  line-height: 72rpx;
}

.lin76r {
  line-height: 76rpx;
}

.lin40 {
  line-height: 80rpx;
}

.lin44 {
  line-height: 88rpx;
}

.lin48 {
  line-height: 96rpx;
}

.lin88r {
  line-height: 88rpx;
}

.lin45 {
  line-height: 90rpx;
}

.lin92r {
  line-height: 92rpx;
}

.lin50 {
  line-height: 100rpx;
}

.lin55 {
  line-height: 110rpx;
}

.lin60 {
  line-height: 120rpx;
}

.lin64 {
  line-height: 128rpx;
}

.lin60r {
  line-height: 60rpx;
}

.lin70 {
  line-height: 140rpx;
}

.lin105r {
  line-height: 105rpx;
}


.lin80 {
  line-height: 160rpx;
}
.lin80r {
  line-height: 80rpx;
}


.lin90 {
  line-height: 180rpx;
}

.lin100 {
  line-height: 200rpx;
}

.b_f5f5f5 {
  border: 1px solid #f5f5f5;
}

.b_transparent {
  border: 1px solid transparent;
}

.b_dcdee2_dashed {
  border: 1px dashed #dcdee2;
}


.bb_f5f5f5 { 
  border-bottom: 1px solid #f5f5f5;
}

.bb_999_dashed {
  border-bottom: 1px dashed #999999;
}

.bb_eee{
  border-bottom: 1px solid #eee;
}

.bl_e1e1e1 { // 表格
  border-left: 1px solid #e1e1e1;
}

.bt_e1e1e1 { // 表格
  border-top: 1px solid #e1e1e1;
}

.bb_e1e1e1 { // 表格
  border-bottom: 1px solid #e1e1e1;
}

.br_e1e1e1 { // 表格
  border-right: 1px solid #e1e1e1;
}

.bc_f1f1f1 { // 表格
  background: #f1f1f1;
}

.bc_fff {
  background: #ffffff;
}

.bc_f3f3f7 {
  background: #f3f3f7;
}




.bc_000{
  background: rgba(0,0,0,.5);
}

.bc_fb {
  background: #fbfbfd;
}

.bc_f5f5f5 {
  background: #f5f5f5;
}

.bc_00b17b {
  background: #409eff;
}


.c_fff {
  color: #ffffff
}


.c_333 {
  color: #333333
}

.c_666 {
  color: #666666
}

.c_999 {
  color: #999999
}

.c_000 {
  color: #000000;
}

.c_A3A3A3 {
  color: #A3A3A3;
}

.c_efefef {
  color: #efefef;
}

.c_5d66c9 {
  color: #5d66c9;
}


.c_00b17b {
  color: #409eff;
}


.c_c0c4cc {
  color: #c0c4cc;
}
.bc_e9283e {
  background: #e9283e !important;
}

.bc_no {
  background: none;
}

.nSele {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.hover {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.hover:active {
  opacity: 0.7;
}


.vbh {
  visibility: hidden;
}

.ovh {
  overflow: hidden;
}

.ova {
  overflow: auto;
}

.ovs {
  overflow: scroll;
}

.clear {
  zoom: 1;
}

.clear::after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: '';
}

.zin-1 {
  z-index: -1;
}

.zin8 {
  z-index: 8;
}

.zin9 {
  z-index: 9;
}

.zin10 {
  z-index: 10;
}

.zin99 {
  z-index: 99;
}

.zin100 {
  z-index: 100;
}

.zin999 {
  z-index: 999;
}

.flex {
  display: flex;
}

.flex_none {
  flex: none;
}

.flex0 {
  flex: 0;
}

.flex1 {
  flex: 1;
}

.flex2 {
  flex: 2;
}

.flex3 {
  flex: 3;
}

.flex4 {
  flex: 4;
}

.flex5 {
  flex: 5;
}

.flex6 {
  flex: 6;
}

.flex7 {
  flex: 7;
}

.flex8 {
  flex: 8;
}

.flex9 {
  flex: 9;
}

.flex10 {
  flex: 10;
}

.flex11 {
  flex: 11;
}

.flex12 {
  flex: 12;
}

.flex13 {
  flex: 13;
}

.flex14 {
  flex: 14;
}

.flex15 {
  flex: 15;
}

.flex_wrap {
  flex-wrap: wrap;
}

.flex_shrink0 {
  flex-shrink: 0;
}

.wcenter {
  justify-content: center;
}

.hcenter {
  align-items: center;
}

.center {
  align-items: center;
  justify-content: center;
}

.left {
  align-items: center;
  justify-content: flex-start;
}

.right {
  align-items: center;
  justify-content: flex-end;
}

.top {
  align-items: flex-start;
}

.bottom {
  align-items: flex-end;
}

.between {
  justify-content: space-between;
}

.around{
  justify-content: space-around;
}

.flex-jcse {
  justify-content: space-evenly;
}

.flex_rowr {
  flex-direction: row-reverse;
}

.flex_column {
  flex-direction: column;
}

.flex_columnr {
  flex-direction: column-reverse;
}

.break-word {
  word-break: break-word;
}

.keep-all {
  word-break: keep-all
}

.bs {
  box-sizing: border-box;
}

/*禁用按钮*/
.disabled {
  color: #c5c8ce !important;
  background-color: #f7f7f7 !important;
  border: 1px solid #dcdee2 !important;
  cursor: not-allowed !important;
}


/*超过宽度出现三个点*/
.overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
