<template>
  <view class="bc_fff listPageMaterial">
    <u-navbar title="工装卸料" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" leftText="返回" :placeholder="true"></u-navbar>
    <view class="listContainer ml10 mr10 mb10">
      <view class="myContainer ml10 mr10 mb10">
        <u--form labelPosition="left" :model="form" labelWidth="100">
          <u-form-item label="设备编码:" required labelWidth="100">
            <u--input v-model="form.machineName" border="none" focus placeholder="请扫描或输入设备编码" @focus="focusEvent('machineName')"></u--input>
            <view class="iconfont icon-saoma" @click="scan('machineName')"></view>
          </u-form-item>
          <u-form-item label="设备描述:" labelWidth="100">
            <view class="w100x flex right">
              {{ form.machineDescription }}
            </view>
          </u-form-item>
        </u--form>
      </view>
      <view class="bc_f5f5f5 h30 lin30 fb pl10 mb4">已安装明细</view>
      <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7">
        <view v-if="simpleTrackProduct.length > 0">
          <view class="mb10 br10 bc_fff pa10 b_dcdee2_dashed" v-for="(ele, index) in simpleTrackProduct" :key="index">
            <view class="flex between h40 hcenter c_999">
              <view>工装编码:</view>
              <view>{{ ele.durableName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>工装描述:</view>
              <view>{{ ele.durableNameDesc }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>安装位置:</view>
              <view>{{ ele.portName }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>额度使用寿命:</view>
              <view>{{ ele.usedCountTotal }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>警戒使用寿命:</view>
              <view>{{ ele.warningUsedCount }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>已使用寿命:</view>
              <view>{{ ele.usedCount }}</view>
            </view>
            <view class="flex between h40 hcenter c_999">
              <view>剩余使用寿命:</view>
              <view>{{ ele.residueUsedCount }}</view>
            </view>
            <view class="btn" @click="submit(ele)">卸载</view>
          </view>
        </view>
        <NoData v-else></NoData>
      </scroll-view>
    </view>
  </view>
</template>


<script>
import NoData from '@/components/NoData/noData'
import ScrollMixin from "@/mixins/ScrollMixin";
export default {
  name: 'rollerConfirmDetail',
  mixins: [ScrollMixin],
  components: {
    NoData,
  },
  watch: {
    'form.machineName': {
      handler(val) {
        this.changeMachineName(val)
      }
    },
  },
  data() {
    this.changeMachineName = this.$debounce(this.changeMachineName, 1000)
    return {
      rulesTip: {
        machineName: '设备号不能为空！',
      },
      form: {},
      simpleTrackProduct: []
    };
  },

  onLoad(e) {
    this.initModel()
  },
  methods: {
    initModel() {
      this.form = {
        machineName: null, // 设备号
        machineDescription: null, // 搅拌机描述
        quantity: null // 数量
      }
    },
    focusEvent(type) {
      this.form[type] = ''
    },
    /* 设备号 */
    async changeMachineName(value) {
      if (!value) return
      let params = {
        machineName: value
      }
      this.$service.ToolingAndInspection.getMachineData(params).then((res) => {
        if (res && res.success) {
          if (res.datas.length > 0) {
            let { machineName, description } = res.datas[0]
            this.form.machineName = machineName
            this.form.machineDescription = description
            this.getData(value)
          }
        }
      })
    },
    getData(machineName) {
      let params = {
        machineName,
      }
      this.$service.ToolingAndInspection.getDurableListByMachineName(params).then((res) => {
        if (res.datas.length > 0) {
          this.simpleTrackProduct = res.datas
        } else {
          this.simpleTrackProduct = []
        }
      })
    },
    submit(item) {
      let params = {
        ...item
      }
      this.$service.ToolingAndInspection.durableUnLoading(params).then((res) => {
        if (res && res.success) {
          this.$Toast('卸载成功！')
          setTimeout(() => {
            this.getData(this.form.machineName)
          }, 800);
        }
      })
    },
    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.form.machineName = 'G.EQ.DCUT03.01'
          break;
        default:
          break;
      }
      // #endif
      //#ifndef H5
      if (key === 'machineName') {
        uni.scanCode({
          success: (res) => {
            this.$set(this.form, key, res.result)
          },
        })
      }
      // #endif
    },
  },
};
</script>


<style lang="scss" scoped>
@import "../../../styles/publicStyle.scss";
// .u-form {
//   /deep/ .uni-input-input {
//     text-align: right !important;
//   }
// }
.listPageMaterial {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom)- 200rpx);

  .topContainer {
    flex-shrink: 0;
  }

  .listContainer {
    flex: 1;
    overflow: hidden;
  }
  .btn {
    margin: 0 auto;
    height: 34px;
    line-height: 34px;
    background-color: #409eff;
    font-weight: 600;
    color: #fff;
    font-size: 15px;
    text-align: center;
    border-radius: 11px;
  }
  /deep/ .uni-input-input {
    text-align: right !important;
  }
}
</style>