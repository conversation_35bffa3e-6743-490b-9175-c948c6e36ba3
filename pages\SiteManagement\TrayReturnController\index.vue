<template>
  <view class="bc_f3f3f7 myContainerPage">
    <u-navbar :title="pageTitle" :autoBack="true" height="50px" :titleStyle="{ color: '#fff' }" leftIconColor="#fff" leftIcon="" :leftText="globalMap.lbBack" :placeholder="true"> </u-navbar>
    <!-- {{ nlsMap }} -->
    <view class="myContainer ma10">
      <u--form labelPosition="left" :model="model" labelWidth="100">
        <u-form-item label="退库类型" borderBottom required labelWidth="100">
          <view class="w100x flex right" @click="checkSelect('trayReturnType')">
            <view v-if="model.trayReturnType">{{ $utils.filterObjLabel(dicts.trayReturnTypeList, model.trayReturnType) }}</view>
            <view class="c_c0c4cc" v-else>请选择</view>
            <view class="ml5" :style="{ transform: select && selectType === 'trayReturnType' ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'all 0.5s' }">
              <u-icon name="arrow-down"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="托盘标签" borderBottom required labelWidth="100">
          <u--input v-model="model.trayNo" border="none" placeholder="请扫描"></u--input>
          <view class="iconfont icon-saoma" @click="scan('trayNo')"></view>
        </u-form-item>

        <u-form-item label="物料标签" labelWidth="100">
          <template v-if="model.trayReturnType">
            <u--input v-model="model.consumableName" border="none" placeholder="请扫描或输入"></u--input>
            <view class="iconfont icon-saoma" @click="scan('consumableName')"></view>
          </template>
        </u-form-item>
      </u--form>

      <u-picker v-if="select" :show="select" :columns="[columns]" keyName="label" @confirm="selectFirm" @cancel="select = false" @close="select = false" :closeOnClickOverlay="true"></u-picker>

      <view class="mt10">
        <view class="table_header bt_e1e1e1 bl_e1e1e1 flex">
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w50">序号</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料标签</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">物料批次</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 flex1">退库数量</view>
          <view class="h40 fs14 fb flex center bc_f1f1f1 bb_e1e1e1 br_e1e1e1 w60">操作</view>
        </view>
        <view class="table_content">
          <view class="flex bl_e1e1e1" style="min-height: 60rpx" v-for="(ele, index) in list" :key="index">
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w50">{{ index + 1 }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableSpecText }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.consumableName }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.dateCode }}</view>
            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 flex1 txt_c">{{ ele.quantity }}</view>

            <view class="fs13 flex center bc_fff bb_e1e1e1 br_e1e1e1 w60 pl4 pr4 pt2 pb2">
              <view class="w80x" @click="deleteItem(ele, index)">
                <u-button type="error" text="删除" :customStyle="{ height: '50rpx' }"> </u-button>
              </view>
            </view>
          </view>
          <NoData v-if="!list || list.length === 0"></NoData>
        </view>

        <!-- refresher-enabled :refresher-triggered="refresherTriggered" @refresherrefresh="refresherrefresh" :scroll-top="scrollTop" @scroll="onScroll" refresher-background="#f3f3f7" @scrolltolower="lower" -->
        <!-- <scroll-view class="h100x" scroll-y :scroll-top="scrollTop" @scroll="onScroll">
          <view class="mb10 br10 bc_fff pa10" v-for="(ele, index) in list" :key="index">
            <view class="flex between h30 hcenter c_999">
              <view> 物料</view>
              <view>{{ $utils.optionShowConfig(ele.consumableSpecName, ele.consumableSpecText) }}</view>
            </view>
            <view class="flex between h30 hcenter c_999">
              <view> 物料标签</view>
              <view>{{ ele.consumableName }}</view>
            </view>
            <view class="flex between h30 hcenter c_999">
              <view> 物料批次</view>
              <view>{{ ele.dateCode }}</view>
            </view>
            <view class="flex between h30 hcenter c_999">
              <view>退库数量</view>
              <view>{{ ele.quantity }}</view>
            </view>
            <view>
              <u-button type="error" text="删除" @click="deleteItem(ele, index)"></u-button>
            </view>
          </view>
        </scroll-view>
        <view @click="goTop">
          <u-back-top :scroll-top="old.scrollTop" :top="600" :bottom="150" :duration="100"></u-back-top>
        </view> -->
      </view>
    </view>

    <view class="btnContainer" @click="submit">确定</view>
  </view>
</template>

<script>
import ScrollMixin from "@/mixins/ScrollMixin";
import NoData from '@/components/NoData/noData'
import useNls from "@/mixins/useNls";
import _ from "lodash";
import PrintPackageMixin from "@/mixins/printPackageMixin";
export default {
  mixins: [useNls, ScrollMixin,PrintPackageMixin],
  components: {
    NoData,
  },
  data() {
    this.changetrayNo = this.$debounce(this.changetrayNo, 1000)
    this.changeconsumableName = this.$debounce(this.changeconsumableName, 1000)
    return {
      pageParams: {},
      pageTitle: '',
      globalMap: getApp().globalData.globalMap, // 获取全局数据
      nlsMap: {

      },
      columns: [],
      select: false,
      selectType: '',
      rulesTip: {
        trayReturnType: '退库类型不能为空',
        trayNo: '托盘标签不能为空',
      },
      trayNoParmas: {},
      model: {},
      list: [], // 物料扫描数据
      // list: Array.from({ length: 20 }, (v, i) => i),
      dicts: {
        trayReturnTypeList: [], // 库位编码
      },
    }
  },
  computed: {
  },
  watch: {
    'model.trayNo': {
      handler(val) {
        this.changetrayNo(val)
      }
    },
    'model.consumableName': {
      handler(val) {
        this.changeconsumableName(val)
      }
    },
  },
  async onLoad(options) {
    let pageParams = JSON.parse(decodeURIComponent(options.pageParams))
    this.pageParams = pageParams
    this.pageTitle = pageParams.pageTitle // 标题
    await this.initNls(pageParams, this.nlsMap)
    this.getEnumValue('TrayReturnType', 'trayReturnTypeList') // 退库类型
    this.initModel()
  },
  methods: {
    getEnumValue(enumname, key) {
      const params = {
        enumname: enumname,
      }
      this.$service.common.getEnumValue(params).then(res => {
        this.dicts[key] = res.datas.map((item, index) => ({
          value: item.value,
          label: item.text
        }))
      })
    },
    deleteItem(item, index) {
      uni.showModal({
        title: '提示',
        content: `是否确认删除？`,
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#666',
        confirmColor: '#409eff',
        success: (res) => {
          if (res.confirm) {
            this.list.splice(index, 1)
          }
          if (res.cancel) { }
        },
      })
    },
    checkSelect(type) {
      this.select = true
      this.selectType = type
      switch (type) {
        case 'trayReturnType':
          this.columns = this.dicts.trayReturnTypeList
          break;
        default:
          break;
      }
    },
    async selectFirm(e) {
      this.$set(this.model, this.selectType, e.value[0].value)
      this.select = false
    },

    initModel() {
      this.model = {
        trayReturnType: '',  // 退库类型
        trayNo: '',  // 物料箱标签
        consumableSpecName: '',// 物料标签
      }
    },

    // 设置
    submit() {
      for (let key in this.rulesTip) {
        if (_.isEmpty(this.model[key])) {
          this.$Toast(this.rulesTip[key])
          return
        }
      }
      let parmas = {
        trayNo: this.model.trayNo,
        trayReturnType: this.model.trayReturnType,
        returnConsumableItemList: this.list,
      }
      this.$service.TrayReturnController.submit(parmas).then(res => {
        this.$Toast('操作成功')
        this.model.trayNo = ''
        this.model.consumableName = ''
        this.list = []
        if (res.datas.length > 0) {
          setTimeout(() => {
            this.myprintPackage(res.datas) // 打印
          }, 800);
        }
      })
    },

    async changetrayNo(value) {
      if (!value) return
      this.list = []
      try {
        let parmas = {
          trayNo: value,
        }
        let res = await this.$service.TrayReturnController.scanTrayNo(parmas)
      } catch (error) {
        this.model.trayNo = ''
      }
    },

    async changeconsumableName(value) {
      if (!value) return

      let findIndex = this.list.findIndex(item => item.consumableName == value)
      if (findIndex > -1) {
        this.$Toast('该标签已存在!')
        this.model.consumableName = ''
        return
      }
      try {
        let params = {
          trayNo: this.model.trayNo,
          consumableName: value,
          trayReturnType: this.model.trayReturnType
        }
        let res = await this.$service.TrayReturnController.scanConsumable(params)
        this.$Toast('操作成功!')
        this.list.push(res.datas[0])
        this.model.consumableName = ''
      } catch (error) {
        this.model.consumableName = ''
      }
    },


    scan(key) {
      // #ifdef H5
      switch (key) {
        case 'machineName':
          this.model.machineName = 'ALINAK01' // ALINAK01  ALIMSA01
          break;
        default:
          break;
      }
      // #endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: (res) => {
          this.$set(this.model, key, res.result)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/uform.scss';
@import '../../../styles/publicStyle.scss';
</style>
